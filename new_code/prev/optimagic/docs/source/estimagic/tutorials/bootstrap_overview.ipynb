{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Bootstrap Tutorial\n", "\n", "This notebook contains a tutorial on how to use the bootstrap functionality provided by estimagic. We start with the simplest possible example of calculating standard errors and confidence intervals for an OLS estimator without as well as with clustering. Then we progress to more advanced examples.\n", "\n", "In the example here, we will work with the \"exercise\" example dataset taken from the seaborn library.\n", "\n", "The working example will be a linear regression to investigate the effects of exercise time on pulse."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import seaborn as sns\n", "import statsmodels.api as sm\n", "\n", "import estimagic as em"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prepare the dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = sns.load_dataset(\"exercise\", index_col=0)\n", "replacements = {\"1 min\": 1, \"15 min\": 15, \"30 min\": 30}\n", "df = df.replace({\"time\": replacements})\n", "df[\"constant\"] = 1\n", "\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Doing a very simple bootstrap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The first thing we need is a function that calculates the bootstrap outcome, given an empirical or re-sampled dataset. The bootstrap outcome is the quantity for which you want to calculate standard errors and confidence intervals. In most applications those are just parameter estimates.\n", "\n", "In our case, we want to regress \"pulse\" on \"time\" and a constant. Our outcome function looks as follows:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def ols_fit(data):\n", "    y = data[\"pulse\"]\n", "    x = data[[\"constant\", \"time\"]]\n", "    params = sm.OLS(y, x).fit().params\n", "\n", "    return params"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In general, the user-specified outcome function may return any pytree (e.g. numpy.ndarray, pandas.DataFrame, dict etc.). In the example here, it returns a pandas.Series."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we are ready to calculate confidence intervals and standard errors."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results_without_cluster = em.bootstrap(data=df, outcome=ols_fit)\n", "results_without_cluster.ci()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results_without_cluster.se()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The above function call represents the minimum that a user has to specify, making full use of the default options, such as drawing a 1_000 bootstrap draws, using the \"percentile\" bootstrap confidence interval, not making use of parallelization, etc.\n", "\n", "If, for example, we wanted to take 10_000 draws, while parallelizing on two cores, and using a \"bc\" type confidence interval, we would simply call the following:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results_without_cluster2 = em.bootstrap(\n", "    data=df, outcome=ols_fit, n_draws=10_000, n_cores=2\n", ")\n", "\n", "results_without_cluster2.ci(ci_method=\"bc\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Doing a clustered bootstrap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In the cluster robust variant of the bootstrap, the original dataset is divided into clusters according to the values of some user-specified variable, and then clusters are drawn uniformly with replacement in order to create the different bootstrap samples. \n", "\n", "In order to use the cluster robust boostrap, we simply specify which variable to cluster by. In the example we are working with, it seems sensible to cluster on individuals, i.e. on the column \"id\" of our dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results_with_cluster = em.bootstrap(data=df, outcome=ols_fit, cluster_by=\"id\")\n", "\n", "results_with_cluster.se()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see that the estimated standard errors are indeed of smaller magnitude when we use the cluster robust bootstrap. \n", "\n", "Finally, we can compare our bootstrap results to a regression on the full sample using statsmodels' OLS function.\n", "We see that the cluster robust bootstrap yields standard error estimates very close to the ones of the cluster robust regression, while the regular bootstrap seems to overestimate the standard errors of both coefficients.\n", "\n", "**Note**: We would not expect the asymptotic statsmodels standard errors to be exactly the same as the bootstrapped standard errors.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y = df[\"pulse\"]\n", "x = df[[\"constant\", \"time\"]]\n", "\n", "\n", "cluster_robust_ols = sm.OLS(y, x).fit(cov_type=\"cluster\", cov_kwds={\"groups\": df[\"id\"]})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Splitting up the process"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In many situations, the above procedure is enough. However, sometimes it may be important to split the bootstrapping process up into smaller steps. Examples for such situations are:\n", "\n", "1. You want to look at the bootstrap estimates\n", "2. You want to do a bootstrap with a low number of draws first and add more draws later without duplicated calculations\n", "3. You have more bootstrap outcomes than just the parameters\n", "\n", "### 1. Accessing bootstrap outcomes\n", "\n", "The bootstrap outcomes are stored in the results object you get back when calling the bootstrap function. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result = em.bootstrap(data=df, outcome=ols_fit, seed=1234)\n", "my_outcomes = result.outcomes\n", "\n", "my_outcomes[:5]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To further compare the cluster bootstrap to the uniform bootstrap, let's plot the sampling distribution of the parameters on time. We can again see that the standard error is smaller when we cluster on the subject id. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_clustered = em.bootstrap(data=df, outcome=ols_fit, seed=1234, cluster_by=\"id\")\n", "my_outcomes_clustered = result_clustered.outcomes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# clustered distribution in blue\n", "sns.histplot(\n", "    pd.DataFrame(my_outcomes_clustered)[\"time\"], kde=True, stat=\"density\", linewidth=0\n", ")\n", "\n", "# non-clustered distribution in orange\n", "sns.histplot(\n", "    pd.DataFrame(my_outcomes)[\"time\"],\n", "    kde=True,\n", "    stat=\"density\",\n", "    linewidth=0,\n", "    color=\"orange\",\n", ");"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Calculating standard errors and confidence intervals from existing bootstrap result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you've already run ``bootstrap`` once, you can simply pass the existing result object to a new call of ``bootstrap``. Estimagic reuses the existing bootstrap outcomes and now only draws ``n_draws`` - ``n_existing`` outcomes instead of drawing entirely new ``n_draws``. Depending on the ``n_draws`` you specified (this is set to 1_000 by default), this may save considerable computation time. \n", "\n", "We can go on and compute confidence intervals and standard errors, just the same way as before, with several methods (e.g. \"percentile\" and \"bc\"), yet without duplicated evaluations of the bootstrap outcome function. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["my_results = em.bootstrap(\n", "    data=df,\n", "    outcome=ols_fit,\n", "    existing_result=result,\n", ")\n", "my_results.ci(ci_method=\"t\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can use this to calculate confidence intervals with several methods (e.g. \"percentile\" and \"bc\") without duplicated evaluations of the bootstrap outcome function."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Extending bootstrap results with more draws\n", "\n", "It is often the case that, for speed reasons, you set the number of bootstrap draws quite low, so you can look at the results earlier and later decide that you need more draws. \n", "\n", "As an example, we will take an initial sample of 500 draws. We then extend it with another 1500 draws. \n", "\n", "*Note*: It is very important to use a different random seed when you calculate the additional outcomes!!!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["initial_result = em.bootstrap(data=df, outcome=ols_fit, seed=5471, n_draws=500)\n", "initial_result.ci(ci_method=\"t\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combined_result = em.bootstrap(\n", "    data=df, outcome=ols_fit, existing_result=initial_result, seed=2365, n_draws=2000\n", ")\n", "combined_result.ci(ci_method=\"t\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Using less draws than totally available bootstrap outcomes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You have a large sample of bootstrap outcomes but want to compute summary statistics only on a subset? No problem! Estimagic got you covered. You can simply pass any number of ``n_draws`` to your next call of ``bootstrap``, regardless of the size of the existing sample you want to use. We already covered the case where ``n_draws`` > ``n_existing`` above, in which case estimagic draws the remaining bootstrap outcomes for you.\n", "\n", "If ``n_draws`` <= ``n_existing``, estimagic takes a random subset of the existing outcomes - and voilà! "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["subset_result = em.bootstrap(\n", "    data=df, outcome=ols_fit, existing_result=combined_result, seed=4632, n_draws=500\n", ")\n", "subset_result.ci(ci_method=\"t\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Accessing the bootstrap samples"]}, {"cell_type": "markdown", "metadata": {}, "source": ["It is also possible to just access the bootstrap samples. You may do so, for example, if you want to calculate your bootstrap outcomes in parallel in a way that is not yet supported by estimagic (e.g. on a large cluster or super-computer)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from estimagic.bootstrap_samples import get_bootstrap_samples\n", "\n", "rng = np.random.default_rng(1234)\n", "my_samples = get_bootstrap_samples(data=df, rng=rng)\n", "my_samples[0]"]}], "metadata": {"kernelspec": {"display_name": "estimagic", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}, "vscode": {"interpreter": {"hash": "e8a16b1bdcc80285313db4674a5df2a5a80c75795379c5d9f174c7c712f05b3a"}}}, "nbformat": 4, "nbformat_minor": 4}