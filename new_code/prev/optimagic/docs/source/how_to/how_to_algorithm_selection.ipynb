{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["(how-to-select-algorithms)=\n", "# How to select a local optimizer\n", "\n", "This guide explains how to choose a local optimizer that works well for your problem. \n", "Depending on your [strategy for global optimization](how_to_globalization.ipynb) it \n", "is also relevant for global optimization problems. \n", "\n", "## Important facts \n", "\n", "- There is no optimizer that works well for all problems \n", "- Making the right choice can lead to enormous speedups\n", "- Making the wrong choice can mean that you [don't solve your problem at all](algo-selection-how-important). Sometimes,\n", "optimizers fail silently!\n", "\n", "\n", "## The three steps for selecting algorithms\n", "\n", "Algorithm selection is a mix of theory and experimentation. We recommend the following \n", "steps:\n", "\n", "1. **Theory**: Based on the properties of your problem, start with 3 to 5 candidate algorithms. \n", "You may use the decision tree below.\n", "2. **Experiments**: Run the candidate algorithms for a small number of function \n", "evaluations and compare the results in a *criterion plot*. As a rule of thumb, use \n", "between `n_params` and `10 * n_params` evaluations. \n", "3. **Optimization**: Re-run the algorithm with the best results until \n", "convergence. Use the best parameter vector from the experiments as start parameters.\n", "\n", "We will walk you through the steps in an [example](algo-selection-example-problem)\n", "below. These steps work well for most problems but sometimes you need \n", "[variations](algo-selection-steps-variations).\n", "\n", "\n", "## A decision tree \n", "\n", "This is a practical guide for narrowing down the set of algorithms to experiment with:\n", "\n", "```{mermaid}\n", "graph LR\n", "    classDef highlight fill:#FF4500;\n", "    A[\"Do you have<br/>nonlinear<br/>constraints?\"] -- yes --> B[\"differentiable?\"]\n", "    B[\"Is your objective function differentiable?\"] -- yes --> C[\"ipopt<br/>nlopt_slsqp<br/>scipy_trust_constr\"]\n", "    B[\"differentiable?\"] -- no --> D[\"scipy_cobyla<br/>nlopt_cobyla\"]\n", "\n", "    A[\"Do you have<br/>nonlinear constraints?\"] -- no --> E[\"Can you exploit<br/>a least-squares<br/>structure?\"]\n", "    E[\"Can you exploit<br/>a least-squares<br/>structure?\"] -- yes --> F[\"differentiable?\"]\n", "    E[\"Can you exploit<br/>a least-squares<br/>structure?\"] -- no --> G[\"differentiable?\"]\n", "\n", "    F[\"differentiable?\"] -- yes --> H[\"scipy_ls_lm<br/>scipy_ls_trf<br/>scipy_ls_dogbox\"]\n", "    F[\"differentiable?\"] -- no --> I[\"nag_dflos<br/>pounders<br/>tao_pounders\"]\n", "\n", "    G[\"differentiable?\"] -- yes --> J[\"scipy_lbfgsb<br/>nlopt_lbfgsb<br/>fides\"]\n", "    G[\"differentiable?\"] -- no --> <PERSON>[\"nlopt_bobyqa<br/>nlopt_neldermead<br/>neldermead_parallel\"]\n", "\n", "```\n", "\n", "Going through the different questions will give you a list of candidate algorithms. \n", "All algorithms in that list are designed for the same problem class but use different \n", "approaches to solve the problem. Which of them works best for your problem can only be \n", "found out through experimentation.\n", "\n", "```{note}\n", "Many books on numerical optimization focus strongly on the inner workings of algorithms.\n", "They will, for example, describe the difference between a trust-region algorithm and a \n", "line-search algorithm in a lot of detail. We have an [intuitive explanation](../explanation/explanation_of_numerical_optimizers.md) of this too. Understanding these details is important for configuring and\n", "troubleshooting optimizations, but not for algorithm selection. For example, If you have\n", "a scalar, differentiable problem without nonlinear constraints, the decision tree \n", "suggests `fides` and two variants of `lbfgsb`. `fides` is a trust-region algorithm, \n", "`lbfgsb` is a line-search algorithm. Both are designed to solve the same kinds of \n", "problems and which one works best needs to be found out through experimentation.\n", "```\n", "\n", "## Filtering algorithms \n", "\n", "An even more fine-grained version of the decision tree is built into optimagic's \n", "algorithm selection tool, which can filter algorithms based on the properties of \n", "your problem. To make this concrete, assume we are looking for a **local** optimizer for \n", "a **differentiable** problem with a **scalar** objective function and \n", "**bound constraints**. \n", "\n", "To find all algorithms that match our criteria, we can simply type:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import optimagic as om\n", "\n", "om.algos.Local.GradientBased.Scalar.Bounded.All"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The available filters are: GradientBased, GradientFree, Global, Local, Bounded, \n", "LinearConstrained, NonlinearConstrained, Scalar, LeastSquares, Likelihood, and Parallel.\n", "You can apply them in any order your want. They are also discoverable, i.e. the \n", "autocomplete feature of your editor will show you all filters you can apply on top of \n", "your current selection.\n", "\n", "Using `.All` after applying filters shows you all algorithms optimagic knows of that \n", "satisfy your criteria. Some of them require optional dependencies. To show only the \n", "algorithms that are available with the packages you have currently installed, use \n", "`.Available` instead of `.All`."]}, {"cell_type": "markdown", "metadata": {}, "source": ["An even more fine-grained way of filtering is described in [Filtering Algorithms Using Bounds](filtering_algorithms_using_bounds)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["(algo-selection-example-problem)=\n", "\n", "## An example problem\n", "\n", "As an example we use the [Trid function](https://www.sfu.ca/~ssurjano/trid.html). The Trid function has no local minimum except \n", "the global one. It is defined for any number of dimensions, we will pick 20. As starting \n", "values we will pick the vector [0, 1, ..., 19]. \n", "\n", "A Python implementation of the function and its gradient looks like this:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "import plotly.io as pio\n", "\n", "pio.renderers.default = \"notebook_connected\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "\n", "def trid_scalar(x):\n", "    \"\"\"Implement Trid function: https://www.sfu.ca/~ssurjano/trid.html.\"\"\"\n", "    return ((x - 1) ** 2).sum() - (x[1:] * x[:-1]).sum()\n", "\n", "\n", "def trid_gradient(x):\n", "    \"\"\"Calculate gradient of trid function.\"\"\"\n", "    l1 = np.insert(x, 0, 0)\n", "    l1 = np.delete(l1, [-1])\n", "    l2 = np.append(x, 0)\n", "    l2 = np.delete(l2, [0])\n", "    return 2 * (x - 1) - l1 - l2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 1: Theory\n", "\n", "\n", "\n", "Let's go through the decision tree for the Trid function:\n", "\n", "1. **No** nonlinear constraints our solution needs to satisfy\n", "2.  **No** least-squares structure we can exploit \n", "3.  **Yes**, the function is differentiable. We even have a closed form gradient that \n", "we would like to use. \n", "\n", "We therefore end up with the candidate algorithms `scipy_lbfgsb`, `nlopt_lbfgsb`, and \n", "`fides`.\n", "\n", "```{note}\n", "If your function is differentiable but you do not have a closed form gradient (yet), \n", "we suggest to use at least one gradient based optimizer and one gradient free optimizer.\n", "in your experiments. Optimagic will use numerical gradients in that case. For details, \n", "see [here](how_to_derivatives.ipynb).\n", "```\n", "\n", "\n", "### Step 2: Experiments\n", "\n", "To find out which algorithms work well for our problem, we simply run optimizations with\n", "all candidate algorithms in a loop and store the result in a dictionary. We limit the \n", "number of function evaluations to 8. Since some algorithms only support a maximum number\n", "of iterations as stopping criterion we also limit the number of iterations to 8.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = {}\n", "for algo in [\"scipy_lbfgsb\", \"nlopt_lbfgsb\", \"fides\"]:\n", "    results[algo] = om.minimize(\n", "        fun=trid_scalar,\n", "        jac=trid_gradient,\n", "        params=np.arange(20),\n", "        algorithm=algo,\n", "        algo_options={\"stopping_maxfun\": 8, \"stopping_maxiter\": 8},\n", "    )\n", "\n", "fig = om.criterion_plot(results, max_evaluations=8)\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["All optimizers work pretty well here and since this is a very simple problem, any of them \n", "would probably find the optimum in a reasonable time. However, `nlopt_lbfgsb` is a bit \n", "better than the others, so we will select it for the next step. In more difficult\n", "examples, the difference between optimizers can be much more pronounced.\n", "\n", "### Step 3: Optimization \n", "\n", "All that is left to do is to run the optimization until convergence with the best \n", "optimizer. To avoid duplicated calculations, we can already start from the previously \n", "best parameter vector:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["best_x = results[\"nlopt_lbfgsb\"].params\n", "results[\"nlopt_lbfgsb_complete\"] = om.minimize(\n", "    fun=trid_scalar,\n", "    jac=trid_gradient,\n", "    params=best_x,\n", "    algorithm=\"nlopt_lbfgsb\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Looking at the result in a criterion plot we can see that the optimizer converges after \n", "a bit more than 30 function evaluations. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig = om.criterion_plot(results)\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["(algo-selection-steps-variations)=\n", "\n", "## Variations of the four steps\n", "\n", "The four steps described above work very well in most situations. However, sometimes \n", "it makes sense to deviate: \n", "\n", "- If you are unsure about some of the questions in step 1, select more algorithms for \n", "the experimentation phase and run more than 1 algorithm until convergence. \n", "- If it is very important to find a precise optimum, run more than 1 algorithm until \n", "convergence. \n", "- If you have a very fast objective function, simply run all candidate algorithms until \n", "convergence. \n", "- If you have a differentiable objective function but no closed form derivative, use \n", "at least one gradient based optimizer and one gradient free optimizer in the \n", "experiments. See [here](how_to_derivatives.ipynb) to learn more about derivatives.\n", "\n", "\n", "(algo-selection-how-important)=\n", "\n", "## How important was it?\n", "\n", "The Trid function is differentiable and very well behaved in almost every aspect. \n", "Moreover, it has a very short runtime. One would think that any optimizer can find its \n", "optimum. So let's compare the selected optimizer with a few others:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = {}\n", "for algo in [\"nlopt_lbfgsb\", \"scipy_neldermead\", \"scipy_cobyla\"]:\n", "    results[algo] = om.minimize(\n", "        fun=trid_scalar,\n", "        jac=trid_gradient,\n", "        params=np.arange(20),\n", "        algorithm=algo,\n", "    )\n", "\n", "fig = om.criterion_plot(results)\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see that our chosen optimizer solves the problem with less than 35 function \n", "evaluations. At this point, the two gradient-free optimizers have not yet made \n", "significant progress. CoByLA gets reasonably close to an optimum after about 4k \n", "evaluations. <PERSON><PERSON><PERSON><PERSON><PERSON> gets stuck after 8k evaluations and fails to solve the problem. \n", "\n", "This example shows not only that the choice of optimizer is important but that the commonly \n", "held belief that gradient free optimizers are generally more robust than gradient based \n", "ones is dangerous! The <PERSON>eld<PERSON>-<PERSON> algorithm did \"converge\" and reports success, but\n", "did not find the optimum. It did not even get stuck in a local optimum because we know \n", "that the Trid function does not have local optima except the global one. It just got \n", "stuck somewhere. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results[\"scipy_neldermead\"].success"]}], "metadata": {"kernelspec": {"display_name": "optimagic-docs", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 4}