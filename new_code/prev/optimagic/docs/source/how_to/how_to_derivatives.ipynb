{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["(how-to-jac)=\n", "\n", "# How to speed up your optimization using derivatives\n", "\n", "Many optimization algorithms use derivatives to find good search directions. If you \n", "use a derivative based optimizer but do not provide derivatives of your objective \n", "function, optimagic calculates a numerical derivative for you. \n", "\n", "While this numerical derivative is usually precise enough to find good search directions \n", "it requires `n + 1` evaluations of the objective function (where `n` is the number of \n", "free parameters). For large `n` this becomes very slow.\n", "\n", "This how-to guide shows how you can speed up your optimization by parallelizing \n", "numerical derivatives or by providing closed form derivatives. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parallel numerical derivatives\n", "\n", "If you have a computer with a few idle cores, the easiest way to speed up your\n", "optimization with a gradient based optimizer is to calculate numerical derivatives \n", "in parallel:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import plotly.io as pio\n", "\n", "pio.renderers.default = \"notebook_connected\"\n", "\n", "import optimagic as om\n", "\n", "\n", "def sphere(x):\n", "    return x @ x\n", "\n", "\n", "res = om.minimize(\n", "    fun=sphere,\n", "    params=np.arange(5),\n", "    algorithm=\"scipy_lbfgsb\",\n", "    numdiff_options=om.NumdiffOptions(n_cores=6),\n", ")\n", "res.params.round(6)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Of course, for this super fast objective function, parallelizing will not yield an actual \n", "speedup. But if your objective function takes 100 milliseconds or longer to evaluate, \n", "you can parallelize efficiently to up to `n + 1` cores. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Custom derivatives\n", "\n", "If you don't want to solve your speed problem by throwing more compute at it, you can \n", "provide a derivative to optimagic that is faster than doing `n + 1` evaluations of `fun`. \n", "Here we show you how to hand-code it, but in practice you would usually use JAX or another \n", "autodiff framework to create the derivative."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def sphere_gradient(x):\n", "    return 2 * x\n", "\n", "\n", "res = om.minimize(\n", "    fun=sphere,\n", "    params=np.arange(5),\n", "    algorithm=\"scipy_lbfgsb\",\n", "    jac=sphere_gradient,\n", ")\n", "res.params.round(6)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this example, the evaluation of `sphere_gradient` is even faster than evaluating `sphere`. \n", "\n", "In non-trivial functions, there are synergies between calculating the objective value and \n", "its derivative. Therefore, you can also provide a function that evaluates both at the same time. In such a case, providing fun is optional."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def sphere_fun_and_gradient(x):\n", "    return x @ x, 2 * x\n", "\n", "\n", "res = om.minimize(\n", "    fun=sphere,  # optional when fun_and_jac is provided\n", "    params=np.arange(5),\n", "    algorithm=\"scipy_lbfgsb\",\n", "    fun_and_jac=sphere_fun_and_gradient,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`fun_and_jac` can be provided in addition to or instead of `jac` or `fun`. Providing them \n", "together gives optimagic more opportunities to save \n", "time by evaluating just the function that is needed for a given optimizer. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Derivatives with flexible params\n", "\n", "Derivatives are compatible with any format of params. In general, the gradients have \n", "just the same structure as your params. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def dict_fun(x):\n", "    return x[\"a\"] ** 2 + x[\"b\"] ** 4\n", "\n", "\n", "def dict_gradient(x):\n", "    return {\"a\": 2 * x[\"a\"], \"b\": 4 * x[\"b\"] ** 3}\n", "\n", "\n", "res = om.minimize(\n", "    fun=dict_fun,\n", "    params={\"a\": 1, \"b\": 2},\n", "    algorithm=\"scipy_lbfgsb\",\n", "    jac=dict_gradient,\n", ")\n", "res.params"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This is also the convention that JAX uses, so any derivative you get via JAX will be \n", "compatible with optimagic. \n", "\n", "## Derivatives for least-squares functions\n", "\n", "When minimizing least-squares functions, you don't need the gradient of the objective \n", "value but the jacobian of the least-squares residuals. Moreover, this jacobian function \n", "needs to be decorated with the `mark.least_squares` decorator. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@om.mark.least_squares\n", "def ls_sphere(params):\n", "    return params\n", "\n", "\n", "@om.mark.least_squares\n", "def ls_sphere_jac(params):\n", "    return np.eye(len(params))\n", "\n", "\n", "res = om.minimize(\n", "    fun=ls_sphere,\n", "    params=np.arange(3),\n", "    algorithm=\"scipy_ls_lm\",\n", "    jac=ls_sphere_jac,\n", ")\n", "res.params.round(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `fun_and_jac` argument works just analogous to the scalar case. \n", "\n", "Derivatives of least-squares functions again work with all valid formats of params. \n", "However, the structure of the jacobian can be a bit complicated. Again, JAX will do \n", "the right thing here, so we strongly suggest you calculate all your jacobians via JAX,\n", "especially if your params are not a flat numpy array. \n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Derivatives that work for scalar and least-squares optimizers\n", "\n", "If you want to seamlessly switch between scalar and least-squares optimizers, you can \n", "do so by providing even more versions of derivatives to `minimize`. You probably won't \n", "ever need this, but here is how you would do it. To pretend that this can be useful, \n", "we compare a scalar and a least squares optimizer in a criterion_plot:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = {}\n", "for algorithm in [\"scipy_lbfgsb\", \"scipy_ls_lm\"]:\n", "    results[algorithm] = om.minimize(\n", "        fun=ls_sphere,\n", "        params=np.arange(5),\n", "        algorithm=algorithm,\n", "        jac=[sphere_gradient, ls_sphere_jac],\n", "    )\n", "\n", "fig = om.criterion_plot(results)\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We see that both optimizers were super fast in solving this problem (mainly because the problem is so simple) and in this case the scalar optimizer was even faster. However, in non-trivial problems it almost always pays of to exploit the least-squares structure if you can."]}], "metadata": {"kernelspec": {"display_name": "optimagic", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}