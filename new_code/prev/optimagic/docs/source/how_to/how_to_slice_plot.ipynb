{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to visualize an optimization problem\n", "\n", "Plotting the criterion function of an optimization problem can answer important questions\n", "- Is the function smooth?\n", "- Is the function flat in some directions?\n", "- Should the optimization problem be scaled?\n", "- Is a candidate optimum a global one?\n", "\n", "Below we show how to make a slice plot of the criterion function."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The simple sphere function (again)\n", "\n", "Let's look at the simple sphere function again. This time, we specify params as dictionary, but of course, any other params format (recall [pytrees](https://jax.readthedocs.io/en/latest/pytrees.html)) would work just as well. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import plotly.io as pio\n", "\n", "pio.renderers.default = \"notebook_connected\"\n", "\n", "import optimagic as om"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def sphere(params):\n", "    x = np.array(list(params.values()))\n", "    return x @ x"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["params = {\"alpha\": 0, \"beta\": 0, \"gamma\": 0, \"delta\": 0}\n", "bounds = om.Bounds(\n", "    lower={name: -5 for name in params},\n", "    upper={name: i + 2 for i, name in enumerate(params)},\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating a simple slice plot"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig = om.slice_plot(\n", "    func=sphere,\n", "    params=params,\n", "    bounds=bounds,\n", ")\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Interpreting the plot\n", "\n", "The plot gives us the following insights:\n", " \n", "- There is no sign of local optima. \n", "- There is no sign of noise or non-differentiablities (careful, grid might not be fine enough).\n", "- The problem seems to be convex.\n", "\n", "-> We would expect almost any derivative based optimizer to work well here (which we know to be correct in that case)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Using advanced options"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig = om.slice_plot(\n", "    func=sphere,\n", "    params=params,\n", "    bounds=bounds,\n", "    # selecting a subset of params\n", "    selector=lambda x: [x[\"alpha\"], x[\"beta\"]],\n", "    # evaluate func in parallel\n", "    n_cores=4,\n", "    # rename the parameters\n", "    param_names={\"alpha\": \"Alpha\", \"beta\": \"Beta\"},\n", "    title=\"Amazing Plot\",\n", "    # number of gridpoints in each dimension\n", "    n_gridpoints=50,\n", ")\n", "fig.show()"]}], "metadata": {"kernelspec": {"display_name": "optimagic", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 4}