{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["(how-to-bounds)=\n", "\n", "# How to specify bounds\n", "\n", "## Constraints vs bounds \n", "\n", "optimagic distinguishes between bounds and constraints. Bounds are lower and upper bounds for parameters. In the literature, they are sometimes called box constraints. Examples for general constraints are linear constraints, probability constraints, or nonlinear constraints. You can find out more about general constraints in the next section on [How to specify constraints](how_to_constraints.md)."]}, {"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["## Example objective function\n", "\n", "Let’s again look at the sphere function:"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "import optimagic as om"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["def fun(x):\n", "    return x @ x"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["res = om.minimize(fun=fun, params=np.arange(3), algorithm=\"scipy_lbfgsb\")\n", "res.params.round(5)"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["## A<PERSON>y params\n", "\n", "For params that are a `numpy.ndarray`, one can specify the lower and/or upper-bounds as an array of the same length.\n", "\n", "**Lower bounds**"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["res = om.minimize(\n", "    fun=fun,\n", "    params=np.arange(3),\n", "    bounds=om.Bounds(lower=np.ones(3)),\n", "    algorithm=\"scipy_lbfgsb\",\n", ")\n", "res.params"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["**Lower & upper-bounds**"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["res = om.minimize(\n", "    fun=fun,\n", "    params=np.arange(3),\n", "    algorithm=\"scipy_lbfgsb\",\n", "    bounds=om.Bounds(\n", "        lower=np.array([-2, -np.inf, 1]),\n", "        upper=np.array([-1, np.inf, np.inf]),\n", "    ),\n", ")\n", "res.params"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON> params\n", "\n", "Now let's look at a case where params is a more general pytree. We also update the sphere function by adding an intercept. Since the criterion always decreases when decreasing the intercept, there is no unrestricted solution. Lets fix a lower bound only for the intercept."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["params = {\"x\": np.arange(3), \"intercept\": 3}\n", "\n", "\n", "def fun(params):\n", "    return params[\"x\"] @ params[\"x\"] + params[\"intercept\"]"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["res = om.minimize(\n", "    fun=fun,\n", "    params=params,\n", "    algorithm=\"scipy_lbfgsb\",\n", "    bounds=om.Bounds(lower={\"intercept\": -2}),\n", ")\n", "res.params"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["optimagic tries to match the user provided bounds with the structure of params. This allows you to specify bounds for subtrees of params. In case your subtree specification results in an unidentified matching, optimagic will tell you so with a `InvalidBoundsError`.  "]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["## params data frame\n", "\n", "It often makes sense to specify your parameters in a `pandas.DataFrame`, where you can utilize the multiindex for parameter naming. In this case, you can specify bounds as extra columns `lower_bound` and `upper_bound`.\n", "\n", "> **Note**\n", "> The columns are called `*_bound` instead of `*_bounds` like the argument passed to `minimize` or `maximize`. "]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "params = pd.DataFrame(\n", "    {\"value\": [0, 1, 2, 3], \"lower_bound\": [0, 1, 1, -2]},\n", "    index=pd.MultiIndex.from_tuples([(\"x\", k) for k in range(3)] + [(\"intercept\", 0)]),\n", ")\n", "params"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["def fun(params):\n", "    x = params.loc[\"x\"][\"value\"].to_numpy()\n", "    intercept = params.loc[\"intercept\"][\"value\"].iloc[0]\n", "    value = x @ x + intercept\n", "    return float(value)"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["res = om.minimize(\n", "    fun,\n", "    params=params,\n", "    algorithm=\"scipy_lbfgsb\",\n", ")\n", "res.params"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["(filtering_algorithms_using_bounds)=\n", "\n", "## Filtering algorithms\n", "\n", "It is further possible to filter algorithms based on whether they support bounds, if bounds are required to run, and if infinite bounds are supported. The AlgoInfo class provides all information about the chosen algorithm, which can be accessed with algo.algo_info... . Suppose we are looking for a optimizer that supports bounds and strictly require them for the algorithm to run properly.\n", "\n", "To find all algorithms that support bounds and cannot run without bounds, we can simply do:\n"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["from optimagic.algorithms import AVAILABLE_ALGORITHMS\n", "\n", "algos_with_bounds_support = [\n", "    algo\n", "    for name, algo in AVAILABLE_ALGORITHMS.items()\n", "    if algo.algo_info.supports_bounds\n", "]\n", "my_selection = [\n", "    algo for algo in algos_with_bounds_support if algo.algo_info.needs_bounds\n", "]\n", "my_selection[0:3]"]}, {"cell_type": "markdown", "id": "19", "metadata": {}, "source": ["Similarly, to find all algorithms that support infinite values in bounds , we can do:"]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["my_selection2 = [\n", "    algo\n", "    for algo in algos_with_bounds_support\n", "    if algo.algo_info.supports_infinite_bounds\n", "]\n", "my_selection2[0:3]"]}, {"cell_type": "markdown", "id": "21", "metadata": {}, "source": ["In case you you forget to specify bounds for a optimizer that strictly requires them or pass infinite values in bounds to a optimizer which does not support them, optimagic will raise an `IncompleteBoundsError`. "]}, {"cell_type": "markdown", "id": "22", "metadata": {}, "source": ["## Coming from scipy"]}, {"cell_type": "markdown", "id": "23", "metadata": {}, "source": ["If `params` is a flat numpy array, you can also provide bounds in any format that \n", "is supported by [`scipy.optimize.minimize`](\n", "https://docs.scipy.org/doc/scipy/reference/generated/scipy.optimize.minimize.html). "]}], "metadata": {"kernelspec": {"display_name": "optimagic-docs", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}