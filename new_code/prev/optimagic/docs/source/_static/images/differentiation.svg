<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="405pt" height="291.6pt" viewBox="0 0 405 291.6" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2024-05-15T12:38:45.956057</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.1, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 291.6
L 405 291.6
L 405 0
L 0 0
L 0 291.6
z
" style="fill: none"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 7.2 284.4
L 397.8 284.4
L 397.8 7.2
L 7.2 7.2
L 7.2 284.4
z
" style="fill: none"/>
   </g>
   <g id="PolyCollection_1"/>
   <g id="PolyCollection_2"/>
   <g id="line2d_1">
    <path d="M 24.954545 19.8
L 28.541322 34.593243
L 32.128099 48.577969
L 35.714876 61.761658
L 39.301653 74.153727
L 42.88843 85.76549
L 46.475207 96.610117
L 50.061983 106.702582
L 53.64876 116.059602
L 57.235537 124.699574
L 60.822314 132.6425
L 64.409091 139.909912
L 67.995868 146.524785
L 71.582645 152.511449
L 75.169421 157.895491
L 78.756198 162.703657
L 82.342975 166.963745
L 85.929752 170.7045
L 89.516529 173.955495
L 93.103306 176.74702
L 96.690083 179.109964
L 100.27686 181.075689
L 103.863636 182.67591
L 107.450413 183.942571
L 111.03719 184.90772
L 114.623967 185.603382
L 118.210744 186.061433
L 121.797521 186.313477
L 125.384298 186.39072
L 128.971074 186.323851
L 132.557851 186.142916
L 136.144628 185.877206
L 139.731405 185.555141
L 143.318182 185.204153
L 146.904959 184.850586
L 150.491736 184.519589
L 154.078512 184.235018
L 157.665289 184.019341
L 161.252066 183.893553
L 164.838843 183.877091
L 168.42562 183.987758
L 172.012397 184.241656
L 175.599174 184.653118
L 179.18595 185.234658
L 182.772727 185.996915
L 186.359504 186.948617
L 189.946281 188.096544
L 193.533058 189.445503
L 197.119835 190.998306
L 200.706612 192.755762
L 204.293388 194.716675
L 207.880165 196.877843
L 211.466942 199.234078
L 215.053719 201.77822
L 218.640496 204.501168
L 222.227273 207.391916
L 225.81405 210.437595
L 229.400826 213.623524
L 232.987603 216.933267
L 236.57438 220.348699
L 240.161157 223.850076
L 243.747934 227.416111
L 247.334711 231.024059
L 250.921488 234.649808
L 254.508264 238.26797
L 258.095041 241.851982
L 261.681818 245.37421
L 265.268595 248.806059
L 268.855372 252.118082
L 272.442149 255.280099
L 276.028926 258.261313
L 279.615702 261.03043
L 283.202479 263.555783
L 286.789256 265.805459
L 290.376033 267.747417
L 293.96281 269.349621
L 297.549587 270.580162
L 301.136364 271.407381
L 304.72314 271.8
L 308.309917 271.727237
L 311.896694 271.15893
L 315.483471 270.065658
L 319.070248 268.41885
L 322.657025 266.190905
L 326.243802 263.355291
L 329.830579 259.886658
L 333.417355 255.760931
L 337.004132 250.955407
L 340.590909 245.448845
L 344.177686 239.221545
L 347.764463 232.255432
L 351.35124 224.53412
L 354.938017 216.042981
L 358.524793 206.769201
L 362.11157 196.701828
L 365.698347 185.831818
L 369.285124 174.152069
L 372.871901 161.657451
L 376.458678 148.344821
L 380.045455 134.213041
" clip-path="url(#p6bca91e8e3)" style="fill: none; stroke: #3b7cd0; stroke-width: 8; stroke-linecap: square"/>
   </g>
   <g id="line2d_2">
    <path d="M 202.5 273.875021
L 202.5 15.516
" clip-path="url(#p6bca91e8e3)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #3b7cd0; stroke-width: 1.5"/>
   </g>
   <g id="line2d_3">
    <path d="M 116.590909 136.295636
L 119.980519 138.148883
L 123.37013 140.00213
L 126.75974 141.855377
L 130.149351 143.708624
L 133.538961 145.561871
L 136.928571 147.415118
L 140.318182 149.268365
L 143.707792 151.121613
L 147.097403 152.97486
L 150.487013 154.828107
L 153.876623 156.681354
L 157.266234 158.534601
L 160.655844 160.387848
L 164.045455 162.241095
L 167.435065 164.094342
L 170.824675 165.94759
L 174.214286 167.800837
L 177.603896 169.654084
L 180.993506 171.507331
L 184.383117 173.360578
L 187.772727 175.213825
L 191.162338 177.067072
L 194.551948 178.920319
L 197.941558 180.773567
L 201.331169 182.626814
L 204.720779 184.480061
L 208.11039 186.333308
L 211.5 188.186555
L 214.88961 190.039802
L 218.279221 191.893049
L 221.668831 193.746296
L 225.058442 195.599544
L 228.448052 197.452791
L 231.837662 199.306038
L 235.227273 201.159285
L 238.616883 203.012532
L 242.006494 204.865779
L 245.396104 206.719026
L 248.785714 208.572273
L 252.175325 210.425521
L 255.564935 212.278768
L 258.954545 214.132015
L 262.344156 215.985262
L 265.733766 217.838509
L 269.123377 219.691756
L 272.512987 221.545003
L 275.902597 223.39825
L 279.292208 225.251498
L 282.681818 227.104745
" clip-path="url(#p6bca91e8e3)" style="fill: none; stroke: #f04f43; stroke-width: 8; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p6bca91e8e3">
   <rect x="7.2" y="7.2" width="390.6" height="277.2"/>
  </clipPath>
 </defs>
</svg>
