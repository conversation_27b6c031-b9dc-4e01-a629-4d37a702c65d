#!/usr/bin/env python3
"""
Quick import time comparison between original and modified optimagic.
This script runs the import time tests directly in the current environments.
"""

import subprocess
import sys
import time
import json
from pathlib import Path


def run_import_time_test(python_path: str, iterations: int = 5) -> dict:
    """Run import time test using python -X importtime."""
    print(f"Testing with Python: {python_path}")
    
    times = []
    detailed_outputs = []
    
    for i in range(iterations):
        print(f"  Iteration {i+1}/{iterations}")
        
        # Use -X importtime for detailed timing
        result = subprocess.run(
            [python_path, "-X", "importtime", "-I", "-c", "import optimagic"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            detailed_outputs.append(result.stderr)
            
            # Extract total time from stderr
            lines = result.stderr.strip().split('\n')
            if lines:
                # Look for the main import line
                for line in lines:
                    if "import optimagic" in line or line.strip().endswith("optimagic"):
                        try:
                            parts = line.split('|')
                            if len(parts) >= 2:
                                cumulative_time = int(parts[1].strip().split()[0])
                                times.append(cumulative_time / 1_000_000)  # Convert to seconds
                                break
                        except (ValueError, IndexError):
                            continue
        else:
            print(f"Error: {result.stderr}")
    
    if not times:
        # Fallback to simple timing
        print("  Falling back to simple timing...")
        for i in range(iterations):
            result = subprocess.run(
                [python_path, "-c", "import time; start=time.perf_counter(); import optimagic; print(time.perf_counter()-start)"],
                capture_output=True,
                text=True
            )
            if result.returncode == 0:
                try:
                    times.append(float(result.stdout.strip()))
                except ValueError:
                    pass
    
    if not times:
        return {"error": "No valid measurements"}
    
    return {
        "times": times,
        "mean": sum(times) / len(times),
        "min": min(times),
        "max": max(times),
        "std": (sum((t - sum(times)/len(times))**2 for t in times) / len(times))**0.5,
        "sample_output": detailed_outputs[0] if detailed_outputs else None
    }


def test_functionality(python_path: str) -> bool:
    """Test that optimagic works correctly."""
    test_code = '''
import optimagic as om
import numpy as np

def sphere(x):
    return x @ x

try:
    result = om.minimize(
        fun=sphere,
        params=np.array([1.0, 2.0]),
        algorithm="scipy_lbfgsb"
    )
    print(f"SUCCESS: {result.success}, fun={result.fun:.2e}")
except Exception as e:
    print(f"ERROR: {e}")
    exit(1)
'''
    
    result = subprocess.run(
        [python_path, "-c", test_code],
        capture_output=True,
        text=True
    )
    return result.returncode == 0 and "SUCCESS" in result.stdout


def main():
    """Main testing function."""
    print("Quick Optimagic Import Time Comparison")
    print("=" * 50)
    
    # Test both versions using their source directories
    tests = [
        {
            "name": "Original",
            "path": "/home/<USER>/learnings/gsoc/pygad_new/optimagic",
            "python": sys.executable
        },
        {
            "name": "Modified", 
            "path": "/home/<USER>/learnings/gsoc/pygad_new/new_code/optimagic",
            "python": sys.executable
        }
    ]
    
    results = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "tests": {}
    }
    
    for test in tests:
        print(f"\n{'='*60}")
        print(f"Testing {test['name']} Version")
        print(f"Path: {test['path']}")
        print(f"{'='*60}")
        
        # Change to the test directory and add to Python path
        original_cwd = Path.cwd()
        original_path = sys.path.copy()
        
        try:
            # Change to source directory
            import os
            os.chdir(test['path'])
            
            # Add src directory to Python path if it exists
            src_path = Path(test['path']) / "src"
            if src_path.exists():
                sys.path.insert(0, str(src_path))
            else:
                sys.path.insert(0, test['path'])
            
            # Test functionality first
            print("Testing functionality...")
            if test_functionality(test['python']):
                print("✓ Functionality test passed")
                
                # Measure import time
                print("Measuring import time...")
                timing_result = run_import_time_test(test['python'])
                
                if "error" not in timing_result:
                    print(f"✓ Import time: {timing_result['mean']:.4f}s (±{timing_result['std']:.4f}s)")
                    results["tests"][test['name'].lower()] = {
                        "path": test['path'],
                        "functionality": "passed",
                        "timing": timing_result
                    }
                else:
                    print(f"✗ Timing measurement failed: {timing_result['error']}")
                    results["tests"][test['name'].lower()] = {
                        "path": test['path'],
                        "functionality": "passed",
                        "timing": {"error": timing_result['error']}
                    }
            else:
                print("✗ Functionality test failed")
                results["tests"][test['name'].lower()] = {
                    "path": test['path'],
                    "functionality": "failed",
                    "timing": {"error": "functionality test failed"}
                }
                
        finally:
            # Restore original state
            os.chdir(original_cwd)
            sys.path = original_path
    
    # Save results
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    filename = f"quick_import_comparison_{timestamp}.json"
    with open(filename, 'w') as f:
        json.dump(results, f, indent=2)
    
    # Print summary
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")
    
    if "original" in results["tests"] and "modified" in results["tests"]:
        orig = results["tests"]["original"]
        mod = results["tests"]["modified"]
        
        if (orig.get("functionality") == "passed" and 
            mod.get("functionality") == "passed" and
            "mean" in orig.get("timing", {}) and 
            "mean" in mod.get("timing", {})):
            
            orig_time = orig["timing"]["mean"]
            mod_time = mod["timing"]["mean"]
            improvement = (orig_time - mod_time) / orig_time * 100
            
            print(f"Original version: {orig_time:.4f}s (±{orig['timing']['std']:.4f}s)")
            print(f"Modified version: {mod_time:.4f}s (±{mod['timing']['std']:.4f}s)")
            print(f"Improvement: {improvement:.1f}% faster")
            
            if improvement > 0:
                print("✓ Modified version is faster!")
            else:
                print("✗ Modified version is slower")
        else:
            print("Could not compare - one or both tests failed")
            for name, data in results["tests"].items():
                print(f"{name}: functionality={data.get('functionality')}, timing={data.get('timing', {}).get('error', 'ok')}")
    
    print(f"\nDetailed results saved to: {filename}")


if __name__ == "__main__":
    main()
