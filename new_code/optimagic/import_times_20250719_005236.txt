Optimagic Import Time Measurement
========================================
Timestamp: 2025-07-19 00:52:36
Python version: 3.13.5 | packaged by Anaconda, Inc. | (main, Jun 12 2025, 16:09:02) [GCC 11.2.0]

optimagic: 1.4135s (±0.2405s)
  Times: [1.8765529329994024, 1.2769724789995962, 1.309742671001004, 1.4034875679990364, 1.2008365820001927]
optimagic.optimizers: 1.2558s (±0.0805s)
  Times: [1.2804473569995025, 1.1098032870013412, 1.2533481270002085, 1.3550151950003055, 1.2805373909995978]
optimagic.optimizers.pygmo_optimizers: 1.1545s (±0.0266s)
  Times: [1.2033849230010674, 1.1430075409989513, 1.1301879279999412, 1.16077534300166, 1.1350519120005629]
optimagic.optimizers.ipopt: 1.1523s (±0.0174s)
  Times: [1.1717066830005933, 1.1240459780001402, 1.1640581589999783, 1.1605856080004742, 1.1410001780004677]
optimagic.optimizers.fides: 1.1777s (±0.0410s)
  Times: [1.1963701819986454, 1.1712648199991236, 1.1548411439998745, 1.1218931389994395, 1.2439096959988092]
optimagic.optimizers.tao_optimizers: 1.3734s (±0.0441s)
  Times: [1.3323491109986207, 1.3144033740009036, 1.4300765349998983, 1.4093019000010827, 1.380898730001718]
optimagic.optimizers.nevergrad_optimizers: 1.1898s (±0.1087s)
  Times: [1.4068747060009628, 1.1392456060002587, 1.136271918001512, 1.124947168998915, 1.1416485219997412]
optimagic.optimizers.nag_optimizers: 1.3144s (±0.1077s)
  Times: [1.5165313470006367, 1.256238623000172, 1.3201070779996371, 1.2750219549998292, 1.2040133039990906]
optimagic.optimizers.iminuit_migrad: 1.2224s (±0.0436s)
  Times: [1.307116195001072, 1.2013860160004697, 1.219170911999754, 1.196639656998741, 1.1876404020003974]
optimagic.optimizers.bayesian_optimizer: 1.2517s (±0.0512s)
  Times: [1.3327465700003813, 1.1721065570000064, 1.247638883000036, 1.2436586629992235, 1.2621000119997916]
optimagic.optimizers.tranquilo: 1.2319s (±0.0739s)
  Times: [1.3685359499995684, 1.2419336389993987, 1.1874815580013092, 1.2061113159998058, 1.155210877999707]
