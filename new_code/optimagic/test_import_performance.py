#!/usr/bin/env python3
"""
Comprehensive import time testing for optimagic before and after optional dependency changes.

This script creates clean environments, installs optimagic packages, and measures import times
with and without optional dependencies to quantify the improvement.
"""

import os
import subprocess
import sys
import time
import json
from pathlib import Path
from typing import Dict, List


def run_command(cmd: str, cwd: str = None, capture_output: bool = True) -> subprocess.CompletedProcess:
    """Run a shell command and return the result."""
    print(f"Running: {cmd}")
    if cwd:
        print(f"  in directory: {cwd}")
    
    result = subprocess.run(
        cmd,
        shell=True,
        cwd=cwd,
        capture_output=capture_output,
        text=True
    )
    
    if result.returncode != 0 and capture_output:
        print(f"Error: {result.stderr}")
    
    return result


def create_conda_env(env_name: str, python_version: str = "3.10") -> bool:
    """Create a new conda environment."""
    print(f"\n{'='*60}")
    print(f"Creating conda environment: {env_name}")
    print(f"{'='*60}")
    
    # Remove existing environment if it exists
    run_command(f"conda env remove -n {env_name} -y", capture_output=False)
    
    # Create new environment
    result = run_command(f"conda create -n {env_name} python={python_version} -y", capture_output=False)
    return result.returncode == 0


def install_optimagic_from_source(env_name: str, source_path: str) -> bool:
    """Install optimagic from source in the specified environment."""
    print(f"\nInstalling optimagic from source: {source_path}")
    
    # Install in development mode
    result = run_command(
        f"conda run -n {env_name} pip install -e .",
        cwd=source_path,
        capture_output=False
    )
    return result.returncode == 0


def install_optional_dependencies(env_name: str) -> bool:
    """Install optional dependencies that affect import time."""
    print(f"\nInstalling optional dependencies in {env_name}")
    
    dependencies = [
        "bayesian-optimization",  # Heavy ML library
        "pygmo",                  # Genetic algorithms
        "cyipopt",               # IPOPT interface
        "fides",                 # Trust region optimizer
        "nevergrad",             # Facebook optimization
        "DFO-LS",               # NAG algorithms
        "Py-BOBYQA",            # NAG algorithms
        "tranquilo",            # Trust region
        "iminuit",              # Already in core deps but test anyway
    ]
    
    # Install via conda where possible, pip otherwise
    conda_deps = ["pygmo", "cyipopt", "tranquilo", "iminuit"]
    pip_deps = ["bayesian-optimization", "fides", "nevergrad", "DFO-LS", "Py-BOBYQA"]
    
    # Install conda dependencies
    if conda_deps:
        conda_cmd = f"conda install -n {env_name} -c conda-forge {' '.join(conda_deps)} -y"
        result = run_command(conda_cmd, capture_output=False)
        if result.returncode != 0:
            print(f"Warning: Some conda dependencies failed to install")
    
    # Install pip dependencies
    for dep in pip_deps:
        result = run_command(f"conda run -n {env_name} pip install {dep}", capture_output=False)
        if result.returncode != 0:
            print(f"Warning: Failed to install {dep}")
    
    return True


def measure_import_time_detailed(env_name: str, iterations: int = 5) -> Dict:
    """Measure detailed import time using python -X importtime."""
    print(f"\nMeasuring detailed import time in {env_name}")
    
    times = []
    detailed_outputs = []
    
    for i in range(iterations):
        print(f"  Iteration {i+1}/{iterations}")
        
        # Use -X importtime for detailed timing
        result = run_command(
            f'conda run -n {env_name} python -X importtime -I -c "import optimagic"'
        )
        
        if result.returncode == 0:
            # Parse the stderr output which contains timing info
            detailed_outputs.append(result.stderr)
            
            # Extract total time from the last line
            lines = result.stderr.strip().split('\n')
            if lines:
                last_line = lines[-1]
                # Look for timing info in the format "import time: self [us] | cumulative [us] | imported package"
                try:
                    # The last line usually contains the total import time
                    parts = last_line.split('|')
                    if len(parts) >= 2:
                        cumulative_time = int(parts[1].strip().split()[0])
                        times.append(cumulative_time / 1_000_000)  # Convert microseconds to seconds
                except (ValueError, IndexError):
                    print(f"Warning: Could not parse timing from: {last_line}")
    
    if not times:
        print("Warning: No valid timing measurements obtained")
        return {"error": "No valid measurements"}
    
    return {
        "times": times,
        "mean": sum(times) / len(times),
        "min": min(times),
        "max": max(times),
        "std": (sum((t - sum(times)/len(times))**2 for t in times) / len(times))**0.5,
        "detailed_outputs": detailed_outputs
    }


def measure_simple_import_time(env_name: str, iterations: int = 5) -> Dict:
    """Measure simple import time."""
    print(f"\nMeasuring simple import time in {env_name}")
    
    times = []
    
    for i in range(iterations):
        print(f"  Iteration {i+1}/{iterations}")
        
        result = run_command(
            f'conda run -n {env_name} python -c "import time; start=time.perf_counter(); import optimagic; print(time.perf_counter()-start)"'
        )
        
        if result.returncode == 0:
            try:
                import_time = float(result.stdout.strip())
                times.append(import_time)
            except ValueError:
                print(f"Warning: Could not parse time from: {result.stdout}")
    
    if not times:
        return {"error": "No valid measurements"}
    
    return {
        "times": times,
        "mean": sum(times) / len(times),
        "min": min(times),
        "max": max(times),
        "std": (sum((t - sum(times)/len(times))**2 for t in times) / len(times))**0.5
    }


def test_optimagic_functionality(env_name: str) -> bool:
    """Test that optimagic works correctly."""
    print(f"\nTesting optimagic functionality in {env_name}")
    
    test_code = '''
import optimagic as om
import numpy as np

def sphere(x):
    return x @ x

try:
    result = om.minimize(
        fun=sphere,
        params=np.array([1.0, 2.0]),
        algorithm="scipy_lbfgsb"
    )
    print(f"SUCCESS: {result.success}, fun={result.fun:.2e}")
except Exception as e:
    print(f"ERROR: {e}")
    exit(1)
'''
    
    result = run_command(f'conda run -n {env_name} python -c "{test_code}"')
    return result.returncode == 0 and "SUCCESS" in result.stdout


def save_results(results: Dict, filename: str):
    """Save results to a JSON file."""
    with open(filename, 'w') as f:
        json.dump(results, f, indent=2)
    print(f"\nResults saved to: {filename}")


def main():
    """Main testing function."""
    print("Optimagic Import Performance Testing")
    print("=" * 50)
    
    # Paths
    original_path = "/home/<USER>/learnings/gsoc/pygad_new/optimagic"
    modified_path = "/home/<USER>/learnings/gsoc/pygad_new/new_code/optimagic"
    
    # Environment names
    env_original = "optimagic_original_test"
    env_modified = "optimagic_modified_test"
    
    results = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "test_description": "Comparing import times before and after moving optional dependencies",
        "environments": {}
    }
    
    # Test scenarios
    scenarios = [
        ("minimal", "Without optional dependencies"),
        ("full", "With optional dependencies")
    ]
    
    for env_name, source_path, version in [
        (env_original, original_path, "original"),
        (env_modified, modified_path, "modified")
    ]:
        print(f"\n{'#'*80}")
        print(f"TESTING {version.upper()} VERSION")
        print(f"{'#'*80}")
        
        results["environments"][version] = {}
        
        for scenario, description in scenarios:
            print(f"\n{'-'*60}")
            print(f"Scenario: {scenario} - {description}")
            print(f"{'-'*60}")
            
            current_env = f"{env_name}_{scenario}"
            
            # Create environment
            if not create_conda_env(current_env):
                print(f"Failed to create environment {current_env}")
                continue
            
            # Install optimagic
            if not install_optimagic_from_source(current_env, source_path):
                print(f"Failed to install optimagic in {current_env}")
                continue
            
            # Install optional dependencies for full scenario
            if scenario == "full":
                install_optional_dependencies(current_env)
            
            # Test functionality
            if not test_optimagic_functionality(current_env):
                print(f"Functionality test failed in {current_env}")
                continue
            
            # Measure import times
            simple_times = measure_simple_import_time(current_env)
            detailed_times = measure_import_time_detailed(current_env)
            
            results["environments"][version][scenario] = {
                "description": description,
                "simple_import": simple_times,
                "detailed_import": detailed_times,
                "functionality_test": "passed"
            }
    
    # Save results
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    save_results(results, f"import_performance_comparison_{timestamp}.json")
    
    # Print summary
    print(f"\n{'='*80}")
    print("SUMMARY")
    print(f"{'='*80}")
    
    for version in ["original", "modified"]:
        if version in results["environments"]:
            print(f"\n{version.upper()} VERSION:")
            for scenario in ["minimal", "full"]:
                if scenario in results["environments"][version]:
                    data = results["environments"][version][scenario]
                    if "simple_import" in data and "mean" in data["simple_import"]:
                        mean_time = data["simple_import"]["mean"]
                        print(f"  {scenario:8}: {mean_time:.4f}s (±{data['simple_import']['std']:.4f}s)")
    
    # Calculate improvements
    try:
        orig_minimal = results["environments"]["original"]["minimal"]["simple_import"]["mean"]
        mod_minimal = results["environments"]["modified"]["minimal"]["simple_import"]["mean"]
        improvement_minimal = (orig_minimal - mod_minimal) / orig_minimal * 100
        
        orig_full = results["environments"]["original"]["full"]["simple_import"]["mean"]
        mod_full = results["environments"]["modified"]["full"]["simple_import"]["mean"]
        improvement_full = (orig_full - mod_full) / orig_full * 100
        
        print(f"\nIMPROVEMENTS:")
        print(f"  Minimal deps: {improvement_minimal:.1f}% faster")
        print(f"  Full deps:    {improvement_full:.1f}% faster")
        
    except KeyError:
        print("\nCould not calculate improvements due to missing data")


if __name__ == "__main__":
    main()
