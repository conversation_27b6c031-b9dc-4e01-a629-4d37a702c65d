#!/usr/bin/env python3
"""Script to measure optimagic import time and track improvements.

This script measures the time it takes to import optimagic and its components,
helping to quantify the impact of moving optional dependency imports into methods.
"""

import sys
import time
import subprocess
from pathlib import Path


def measure_import_time(module_name: str, iterations: int = 5) -> dict:
    """Measure the time to import a module.

    Args:
        module_name: Name of the module to import
        iterations: Number of times to measure (for averaging)

    Returns:
        Dictionary with timing statistics
    """
    times = []

    for _ in range(iterations):
        # Use subprocess to get clean import time (no cached imports)
        # The subprocess approach automatically handles cache clearing
        result = subprocess.run(
            [sys.executable, "-c", f"import time; start=time.perf_counter(); import {module_name}; print(time.perf_counter()-start)"],
            capture_output=True,
            text=True
        )

        if result.returncode != 0:
            print(f"Error importing {module_name}: {result.stderr}")
            return {"error": result.stderr}

        try:
            import_time = float(result.stdout.strip())
            times.append(import_time)
        except ValueError:
            print(f"Error parsing time for {module_name}: {result.stdout}")
            return {"error": f"Could not parse time: {result.stdout}"}

    return {
        "module": module_name,
        "times": times,
        "mean": sum(times) / len(times),
        "min": min(times),
        "max": max(times),
        "std": (sum((t - sum(times)/len(times))**2 for t in times) / len(times))**0.5
    }


def measure_optimagic_components():
    """Measure import times for optimagic and its key components."""
    components = [
        "optimagic",
        "optimagic.optimizers",
        "optimagic.optimizers.pygmo_optimizers",
        "optimagic.optimizers.ipopt", 
        "optimagic.optimizers.fides",
        "optimagic.optimizers.tao_optimizers",
        "optimagic.optimizers.nevergrad_optimizers",
        "optimagic.optimizers.nag_optimizers",
        "optimagic.optimizers.iminuit_migrad",
        "optimagic.optimizers.bayesian_optimizer",
        "optimagic.optimizers.tranquilo",
    ]
    
    results = {}
    print("Measuring import times...")
    print("=" * 60)
    
    for component in components:
        print(f"Measuring {component}...")
        result = measure_import_time(component)
        results[component] = result
        
        if "error" in result:
            print(f"  ERROR: {result['error']}")
        else:
            print(f"  Mean: {result['mean']:.4f}s (±{result['std']:.4f}s)")
    
    return results


def print_summary(results: dict):
    """Print a summary of the timing results."""
    print("\n" + "=" * 60)
    print("IMPORT TIME SUMMARY")
    print("=" * 60)
    
    total_time = 0
    successful_imports = 0
    
    for module, result in results.items():
        if "error" not in result:
            print(f"{module:40} {result['mean']:.4f}s (±{result['std']:.4f}s)")
            total_time += result['mean']
            successful_imports += 1
        else:
            print(f"{module:40} ERROR")
    
    print("-" * 60)
    print(f"{'Total time for successful imports':40} {total_time:.4f}s")
    print(f"{'Successful imports':40} {successful_imports}")
    print(f"{'Failed imports':40} {len(results) - successful_imports}")


def save_results(results: dict, filename: str = None):
    """Save results to a file for comparison."""
    if filename is None:
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"import_times_{timestamp}.txt"
    
    with open(filename, "w") as f:
        f.write("Optimagic Import Time Measurement\n")
        f.write("=" * 40 + "\n")
        f.write(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Python version: {sys.version}\n\n")
        
        for module, result in results.items():
            if "error" not in result:
                f.write(f"{module}: {result['mean']:.4f}s (±{result['std']:.4f}s)\n")
                f.write(f"  Times: {result['times']}\n")
            else:
                f.write(f"{module}: ERROR - {result['error']}\n")
    
    print(f"\nResults saved to: {filename}")


def main():
    """Main function to run import time measurements."""
    print("Optimagic Import Time Measurement")
    print("=" * 40)
    print("This script measures the time to import optimagic components.")
    print("Each measurement is averaged over 5 iterations.\n")
    
    # Change to the optimagic directory
    script_dir = Path(__file__).parent
    optimagic_dir = script_dir / "src"
    if optimagic_dir.exists():
        import os
        os.chdir(script_dir)
        sys.path.insert(0, str(optimagic_dir))
    
    results = measure_optimagic_components()
    print_summary(results)
    save_results(results)
    
    return results


if __name__ == "__main__":
    main()
