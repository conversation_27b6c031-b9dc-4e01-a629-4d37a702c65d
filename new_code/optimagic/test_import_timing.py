#!/usr/bin/env python3
"""
Test optimagic import timing with detailed analysis.
This script measures import time and shows which optional dependencies are installed.
"""

import subprocess
import re
import sys
from pathlib import Path

NUM_RUNS = 10

def check_optional_dependencies():
    """Check which optional dependencies are currently installed."""
    optional_deps = [
        'pygmo', 'cyipopt', 'fides', 'petsc4py', 'nevergrad', 
        'dfols', 'pybobyqa', 'tranquilo', 'bayes_opt'
    ]
    
    print("🔍 Checking optional dependencies:")
    installed = []
    for dep in optional_deps:
        try:
            __import__(dep)
            print(f"  ✅ {dep}")
            installed.append(dep)
        except ImportError:
            print(f"  ❌ {dep}")
    
    print(f"\n📊 Total optional dependencies installed: {len(installed)}/{len(optional_deps)}")
    return installed

def measure_import_time():
    """Measure optimagic import time using python -X importtime."""
    times = []
    
    print(f"\n⏱️  Running 'import optimagic' {NUM_RUNS} times...\n")
    
    # Pattern to match the optimagic import line
    pattern = re.compile(r'import time:\s+\d+\s+\|\s+(\d+)\s+\|\s+optimagic$')
    
    for i in range(NUM_RUNS):
        print(f"Run {i+1:2d}...", end=" ")
        result = subprocess.run(
            ['python', '-X', 'importtime', '-I', '-c', 'import optimagic'],
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            print(f"❌ Error: {result.stderr}")
            continue
        
        match = None
        for line in result.stderr.splitlines():
            match = pattern.search(line)
            if match:
                break
        
        if match:
            cumulative_us = int(match.group(1))
            times.append(cumulative_us)
            print(f"{cumulative_us / 1_000_000:.3f} sec")
        else:
            print("❌ Failed to parse timing")
    
    return times

def analyze_results(times, installed_deps):
    """Analyze and display the timing results."""
    if not times:
        print("\n❌ No valid timing data found.")
        return
    
    avg_us = sum(times) / len(times)
    min_us = min(times)
    max_us = max(times)
    std_us = (sum((t - avg_us)**2 for t in times) / len(times))**0.5
    
    print(f"\n{'='*60}")
    print("📈 RESULTS SUMMARY")
    print(f"{'='*60}")
    print(f"Average import time: {avg_us / 1_000_000:.3f} seconds")
    print(f"Min import time:     {min_us / 1_000_000:.3f} seconds")
    print(f"Max import time:     {max_us / 1_000_000:.3f} seconds")
    print(f"Std deviation:       {std_us / 1_000_000:.3f} seconds")
    print(f"Valid measurements:  {len(times)}/{NUM_RUNS}")
    
    # Context about dependencies
    print(f"\n🔗 Context:")
    print(f"Optional dependencies installed: {len(installed_deps)}")
    if len(installed_deps) == 0:
        print("⚠️  No optional dependencies installed - changes won't show much improvement")
    elif len(installed_deps) < 5:
        print("🔶 Some optional dependencies installed - moderate improvement expected")
    else:
        print("🔥 Many optional dependencies installed - significant improvement expected!")
    
    return avg_us / 1_000_000

def main():
    """Main function."""
    print("🚀 Optimagic Import Time Tester")
    print("=" * 40)
    
    # Show current directory and Python version
    print(f"📁 Current directory: {Path.cwd()}")
    print(f"🐍 Python version: {sys.version}")
    
    # Check dependencies
    installed_deps = check_optional_dependencies()
    
    # Measure timing
    times = measure_import_time()
    
    # Analyze results
    avg_time = analyze_results(times, installed_deps)
    
    # Save results to file
    if times:
        import time as time_module
        timestamp = time_module.strftime("%Y%m%d_%H%M%S")
        filename = f"import_timing_{timestamp}.txt"
        
        with open(filename, 'w') as f:
            f.write(f"Optimagic Import Timing Results\n")
            f.write(f"Timestamp: {time_module.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Directory: {Path.cwd()}\n")
            f.write(f"Python: {sys.version}\n")
            f.write(f"Optional deps installed: {len(installed_deps)}\n")
            f.write(f"Average time: {avg_time:.3f} seconds\n")
            f.write(f"All times: {[t/1_000_000 for t in times]}\n")
        
        print(f"\n💾 Results saved to: {filename}")

if __name__ == "__main__":
    main()
