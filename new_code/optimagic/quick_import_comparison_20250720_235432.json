{"timestamp": "2025-07-20 23:54:08", "tests": {"original": {"path": "/home/<USER>/learnings/gsoc/pygad_new/optimagic", "functionality": "passed", "timing": {"times": [1.665977, 1.662808, 1.700482, 1.761801, 1.693872], "mean": 1.696988, "min": 1.662808, "max": 1.761801, "std": 0.03564434592470449, "sample_output": "import time: self [us] | cumulative | imported package\nimport time:       245 |        245 |   _io\nimport time:        84 |         84 |   marshal\nimport time:       426 |        426 |   posix\nimport time:       800 |       1554 | _frozen_importlib_external\nimport time:       104 |        104 |   time\nimport time:       282 |        386 | zipimport\nimport time:        61 |         61 |     _codecs\nimport time:       717 |        777 |   codecs\nimport time:       543 |        543 |   encodings.aliases\nimport time:       735 |       2055 | encodings\nimport time:       213 |        213 | encodings.utf_8\nimport time:       120 |        120 | _signal\nimport time:        39 |         39 |     _abc\nimport time:       209 |        248 |   abc\nimport time:       192 |        440 | io\nimport time:        63 |         63 |       _stat\nimport time:       114 |        176 |     stat\nimport time:       993 |        993 |     _collections_abc\nimport time:        86 |         86 |       errno\nimport time:       178 |        178 |       genericpath\nimport time:       240 |        504 |     posixpath\nimport time:       601 |       2272 |   os\nimport time:       117 |        117 |   _sitebuiltins\nimport time:       892 |        892 |   encodings.utf_8_sig\nimport time:       643 |        643 |   _distutils_hack\nimport time:       162 |        162 |   sitecustomize\nimport time:      2551 |       6635 | site\nimport time:       150 |        150 | linecache\nimport time:       221 |        221 |   __future__\nimport time:       173 |        173 |               itertools\nimport time:       185 |        185 |               keyword\nimport time:       124 |        124 |                 _operator\nimport time:       798 |        921 |               operator\nimport time:       225 |        225 |               reprlib\nimport time:        86 |         86 |               _collections\nimport time:      1163 |       2751 |             collections\nimport time:        74 |         74 |             _functools\nimport time:       992 |       3817 |           functools\nimport time:       291 |        291 |           types\nimport time:      1678 |       5784 |         enum\nimport time:        94 |         94 |           _sre\nimport time:       269 |        269 |             re._constants\nimport time:       453 |        722 |           re._parser\nimport time:       126 |        126 |           re._casefix\nimport time:       423 |       1363 |         re._compiler\nimport time:       164 |        164 |         copyreg\nimport time:       603 |       7913 |       re\nimport time:       264 |        264 |           _weakrefset\nimport time:       897 |       1161 |         weakref\nimport time:       189 |       1350 |       copy\nimport time:      1339 |       1339 |           _ast\nimport time:       665 |        665 |           contextlib\nimport time:      1382 |       3386 |         ast\nimport time:       172 |        172 |             _opcode\nimport time:       253 |        253 |             _opcode_metadata\nimport time:       279 |        703 |           opcode\nimport time:      1002 |       1705 |         dis\nimport time:       162 |        162 |           importlib\nimport time:        72 |        234 |         importlib.machinery\nimport time:       164 |        164 |           token\nimport time:        44 |         44 |           _tokenize\nimport time:       939 |       1146 |         tokenize\nimport time:      2533 |       9002 |       inspect\nimport time:      1032 |      19294 |     dataclasses\nimport time:        64 |         64 |       _typing\nimport time:      3104 |       3168 |     typing\nimport time:       402 |        402 |       warnings\nimport time:       188 |        188 |       numpy.version\nimport time:       156 |        156 |       numpy._expired_attrs_2_0\nimport time:       251 |        251 |           numpy._utils._convertions\nimport time:       305 |        555 |         numpy._utils\nimport time:       625 |       1180 |       numpy._globals\nimport time:        41 |         41 |         numpy._distributor_init_local\nimport time:       806 |        846 |       numpy._distributor_init\nimport time:       780 |        780 |                   _datetime\nimport time:       191 |        971 |                 datetime\nimport time:       301 |        301 |                 math\nimport time:       299 |        299 |                 numpy.exceptions\nimport time:      3871 |       3871 |                 numpy._core._exceptions\nimport time:       212 |        212 |                     _contextvars\nimport time:       126 |        337 |                   contextvars\nimport time:       193 |        530 |                 numpy._core.printoptions\nimport time:       324 |        324 |                 numpy.dtypes\nimport time:     16789 |      23082 |               numpy._core._multiarray_umath\nimport time:       279 |        279 |                 numpy._utils._inspect\nimport time:       465 |        743 |               numpy._core.overrides\nimport time:       867 |      24691 |             numpy._core.multiarray\nimport time:       303 |        303 |             numpy._core.umath\nimport time:       457 |        457 |               numbers\nimport time:       287 |        287 |               numpy._core._dtype\nimport time:       143 |        143 |               numpy._core._string_helpers\nimport time:       436 |        436 |               numpy._core._type_aliases\nimport time:       568 |       1888 |             numpy._core.numerictypes\nimport time:       221 |        221 |               numpy._core._ufunc_config\nimport time:       300 |        300 |                       _struct\nimport time:       229 |        528 |                     struct\nimport time:       336 |        336 |                     _compat_pickle\nimport time:       411 |        411 |                     _pickle\nimport time:      1252 |       2525 |                   pickle\nimport time:       268 |       2793 |                 numpy._core._methods\nimport time:      4864 |       7656 |               numpy._core.fromnumeric\nimport time:       274 |       8149 |             numpy._core._machar\nimport time:       522 |        522 |                 numpy._core.shape_base\nimport time:       198 |        198 |                 numpy._core._asarray\nimport time:       836 |        836 |                 numpy._core.arrayprint\nimport time:      1255 |       2809 |               numpy._core.numeric\nimport time:       503 |       3311 |             numpy._core.einsumfunc\nimport time:       306 |        306 |             numpy._core.function_base\nimport time:       440 |        440 |             numpy._core.getlimits\nimport time:       234 |        234 |             numpy._core.memmap\nimport time:       513 |        513 |             numpy._core.records\nimport time:      1729 |       1729 |             numpy._core._add_newdocs\nimport time:       507 |        507 |             numpy._core._add_newdocs_scalars\nimport time:       168 |        168 |             numpy._core._dtype_ctypes\nimport time:       648 |        648 |                 _ctypes\nimport time:       392 |        392 |                 ctypes._endian\nimport time:      1144 |       2183 |               ctypes\nimport time:      1191 |       3373 |             numpy._core._internal\nimport time:       206 |        206 |             numpy._pytesttester\nimport time:       718 |      46528 |           numpy._core\nimport time:        42 |      46570 |         numpy._core._multiarray_umath\nimport time:       427 |      46997 |       numpy.__config__\nimport time:       367 |        367 |                         numpy._typing._nbit_base\nimport time:       309 |        309 |                         numpy._typing._nested_sequence\nimport time:       142 |        142 |                         numpy._typing._shape\nimport time:      6249 |       7065 |                       numpy._typing._array_like\nimport time:      3131 |       3131 |                       numpy._typing._char_codes\nimport time:      3270 |       3270 |                       numpy._typing._dtype_like\nimport time:       189 |        189 |                       numpy._typing._nbit\nimport time:       159 |        159 |                       numpy._typing._scalars\nimport time:       115 |        115 |                       numpy._typing._ufunc\nimport time:       443 |      14370 |                     numpy._typing\nimport time:       436 |        436 |                       numpy.lib._stride_tricks_impl\nimport time:      2021 |       2457 |                     numpy.lib._twodim_base_impl\nimport time:       213 |        213 |                       numpy.lib._array_utils_impl\nimport time:       181 |        394 |                     numpy.lib.array_utils\nimport time:       527 |        527 |                     numpy.linalg._umath_linalg\nimport time:      3662 |      21407 |                   numpy.linalg._linalg\nimport time:       223 |        223 |                   numpy.linalg.linalg\nimport time:       224 |      21852 |                 numpy.linalg\nimport time:       389 |      22241 |               numpy.matrixlib.defmatrix\nimport time:       206 |      22446 |             numpy.matrixlib\nimport time:       511 |        511 |               numpy.lib._histograms_impl\nimport time:      4594 |       5104 |             numpy.lib._function_base_impl\nimport time:       163 |        163 |             numpy.lib.stride_tricks\nimport time:       564 |      28275 |           numpy.lib._index_tricks_impl\nimport time:       388 |      28663 |         numpy.lib._arraypad_impl\nimport time:      1321 |       1321 |         numpy.lib._arraysetops_impl\nimport time:       234 |        234 |         numpy.lib._arrayterator_impl\nimport time:       640 |        640 |         numpy.lib._nanfunctions_impl\nimport time:       215 |        215 |                   _wmi\nimport time:       879 |       1094 |                 platform\nimport time:      1697 |       1697 |                 textwrap\nimport time:       281 |       3070 |               numpy.lib._utils_impl\nimport time:       257 |       3327 |             numpy.lib._format_impl\nimport time:       188 |       3514 |           numpy.lib.format\nimport time:       331 |        331 |           numpy.lib._datasource\nimport time:       484 |        484 |           numpy.lib._iotools\nimport time:       875 |       5203 |         numpy.lib._npyio_impl\nimport time:       145 |        145 |             numpy.lib._ufunclike_impl\nimport time:       365 |        509 |           numpy.lib._type_check_impl\nimport time:       591 |       1100 |         numpy.lib._polynomial_impl\nimport time:       451 |        451 |         numpy.lib._shape_base_impl\nimport time:       206 |        206 |         numpy.lib._version\nimport time:        96 |         96 |         numpy.lib.introspect\nimport time:       176 |        176 |         numpy.lib.mixins\nimport time:        85 |         85 |         numpy.lib.npyio\nimport time:       295 |        295 |           numpy.lib._scimath_impl\nimport time:        96 |        391 |         numpy.lib.scimath\nimport time:       568 |      39127 |       numpy.lib\nimport time:       191 |        191 |       numpy._array_api_info\nimport time:      2567 |      91651 |     numpy\nimport time:       320 |        320 |         pytz.exceptions\nimport time:      1216 |       1216 |           threading\nimport time:       440 |       1656 |         pytz.lazy\nimport time:       258 |        258 |             _bisect\nimport time:       172 |        430 |           bisect\nimport time:       399 |        828 |         pytz.tzinfo\nimport time:       179 |        179 |         pytz.tzfile\nimport time:       780 |       3760 |       pytz\nimport time:       209 |        209 |         dateutil._version\nimport time:       204 |        413 |       dateutil\nimport time:       373 |        373 |           sysconfig\nimport time:       644 |        644 |           _sysconfigdata__linux_x86_64-linux-gnu\nimport time:       842 |       1858 |         pandas.compat._constants\nimport time:       229 |        229 |             _compression\nimport time:       321 |        321 |             _bz2\nimport time:       303 |        851 |           bz2\nimport time:       318 |        318 |             _lzma\nimport time:       328 |        645 |           lzma\nimport time:       207 |       1703 |         pandas.compat.compressors\nimport time:       108 |        108 |             pandas.util\nimport time:      2519 |       2626 |           pandas.util.version\nimport time:       413 |       3039 |         pandas.compat.numpy\nimport time:        75 |         75 |           pyarrow\nimport time:       171 |        246 |         pandas.compat.pyarrow\nimport time:       304 |       7148 |       pandas.compat\nimport time:       541 |        541 |                   numpy.random._common\nimport time:       222 |        222 |                       binascii\nimport time:       276 |        498 |                     base64\nimport time:      2022 |       2022 |                       _hashlib\nimport time:       205 |        205 |                         _blake2\nimport time:       385 |        589 |                       hashlib\nimport time:       256 |       2865 |                     hmac\nimport time:       188 |        188 |                       _random\nimport time:       527 |        715 |                     random\nimport time:       226 |       4302 |                   secrets\nimport time:       537 |       5379 |                 numpy.random.bit_generator\nimport time:       360 |       5738 |               numpy.random._bounded_integers\nimport time:       253 |        253 |                   numpy.random._pcg64\nimport time:       294 |        294 |                   numpy.random._mt19937\nimport time:       842 |       1389 |                 numpy.random._generator\nimport time:       259 |        259 |                 numpy.random._philox\nimport time:       184 |        184 |                 numpy.random._sfc64\nimport time:       927 |        927 |                 numpy.random.mtrand\nimport time:       211 |       2968 |               numpy.random._pickle\nimport time:       231 |       8937 |             numpy.random\nimport time:      3596 |      12532 |           pandas._typing\nimport time:       220 |        220 |           pandas.util._exceptions\nimport time:      1079 |      13830 |         pandas._config.config\nimport time:       274 |        274 |         pandas._config.dates\nimport time:       107 |        107 |             _locale\nimport time:       983 |       1090 |           locale\nimport time:       199 |       1288 |         pandas._config.display\nimport time:       363 |      15753 |       pandas._config\nimport time:       120 |        120 |         pandas.core\nimport time:       879 |        999 |       pandas.core.config_init\nimport time:       250 |        250 |           pandas._libs.pandas_parser\nimport time:       173 |        173 |           pandas._libs.pandas_datetime\nimport time:       206 |        206 |                       pandas._libs.tslibs.ccalendar\nimport time:       392 |        392 |                       pandas._libs.tslibs.np_datetime\nimport time:      1108 |       1706 |                     pandas._libs.tslibs.dtypes\nimport time:       198 |        198 |                       pandas._libs.tslibs.base\nimport time:       458 |        458 |                           pandas._libs.tslibs.nattype\nimport time:       217 |        217 |                               pandas.compat._optional\nimport time:       248 |        248 |                                 zoneinfo._tzpath\nimport time:       235 |        235 |                                 zoneinfo._common\nimport time:       206 |        206 |                                 _zoneinfo\nimport time:       250 |        938 |                               zoneinfo\nimport time:       299 |        299 |                                       importlib._abc\nimport time:       271 |        570 |                                     importlib.util\nimport time:       968 |       1537 |                                   six\nimport time:        51 |         51 |                                   six.moves\nimport time:       306 |        306 |                                   dateutil.tz._common\nimport time:       235 |        235 |                                   dateutil.tz._factories\nimport time:        29 |         29 |                                     six.moves.winreg\nimport time:       216 |        245 |                                   dateutil.tz.win\nimport time:       928 |       3299 |                                 dateutil.tz.tz\nimport time:       164 |       3463 |                               dateutil.tz\nimport time:       739 |       5356 |                             pandas._libs.tslibs.timezones\nimport time:      1389 |       1389 |                                 calendar\nimport time:      1242 |       2630 |                               _strptime\nimport time:       604 |        604 |                                   signal\nimport time:       287 |        287 |                                   fcntl\nimport time:       143 |        143 |                                   msvcrt\nimport time:       151 |        151 |                                   _posixsubprocess\nimport time:       172 |        172 |                                   select\nimport time:       620 |        620 |                                   selectors\nimport time:       855 |       2830 |                                 subprocess\nimport time:       231 |       3061 |                               pandas._config.localization\nimport time:       474 |       6164 |                             pandas._libs.tslibs.fields\nimport time:       907 |      12425 |                           pandas._libs.tslibs.timedeltas\nimport time:       495 |        495 |                           pandas._libs.tslibs.tzconversion\nimport time:       903 |      14280 |                         pandas._libs.tslibs.timestamps\nimport time:       227 |        227 |                         pandas._libs.properties\nimport time:      1508 |      16014 |                       pandas._libs.tslibs.offsets\nimport time:       865 |        865 |                           _decimal\nimport time:       204 |       1069 |                         decimal\nimport time:        52 |         52 |                               _string\nimport time:       705 |        757 |                             string\nimport time:       182 |        182 |                             dateutil._common\nimport time:      1388 |       2326 |                           dateutil.parser._parser\nimport time:       375 |        375 |                           dateutil.parser.isoparser\nimport time:       419 |       3119 |                         dateutil.parser\nimport time:       892 |        892 |                         pandas._libs.tslibs.strptime\nimport time:       979 |       6056 |                       pandas._libs.tslibs.parsing\nimport time:       583 |      22849 |                     pandas._libs.tslibs.conversion\nimport time:       675 |        675 |                     pandas._libs.tslibs.period\nimport time:       450 |        450 |                     pandas._libs.tslibs.vectorized\nimport time:       271 |      25950 |                   pandas._libs.tslibs\nimport time:        29 |      25978 |                 pandas._libs.tslibs.nattype\nimport time:       208 |        208 |                 pandas._libs.ops_dispatch\nimport time:       759 |      26944 |               pandas._libs.missing\nimport time:      1325 |      28268 |             pandas._libs.hashtable\nimport time:      4095 |       4095 |             pandas._libs.algos\nimport time:       830 |      33192 |           pandas._libs.interval\nimport time:       154 |      33767 |         pandas._libs\nimport time:       190 |        190 |           pandas.core.dtypes\nimport time:       117 |        117 |             pyarrow\nimport time:      1261 |       1377 |           pandas._libs.lib\nimport time:       752 |        752 |           pandas.errors\nimport time:       961 |        961 |             pandas.core.dtypes.generic\nimport time:       452 |       1413 |           pandas.core.dtypes.base\nimport time:       237 |        237 |           pandas.core.dtypes.inference\nimport time:      1677 |       5644 |         pandas.core.dtypes.dtypes\nimport time:       510 |        510 |           pandas.core.dtypes.common\nimport time:       406 |        915 |         pandas.core.dtypes.missing\nimport time:       293 |        293 |           pandas.util._decorators\nimport time:       144 |        144 |               pandas.io\nimport time:       288 |        431 |             pandas.io._util\nimport time:       672 |       1102 |           pandas.core.dtypes.cast\nimport time:       162 |        162 |             pandas.core.dtypes.astype\nimport time:       237 |        399 |           pandas.core.dtypes.concat\nimport time:       104 |        104 |             pandas.core.array_algos\nimport time:      4744 |       4744 |                 numpy.ma.core\nimport time:      1152 |       1152 |                 numpy.ma.extras\nimport time:       227 |       6121 |               numpy.ma\nimport time:       342 |        342 |               pandas.core.common\nimport time:       336 |       6798 |             pandas.core.construction\nimport time:       382 |       7283 |           pandas.core.array_algos.take\nimport time:       211 |        211 |             pandas.core.indexers.utils\nimport time:       157 |        367 |           pandas.core.indexers\nimport time:       663 |      10106 |         pandas.core.algorithms\nimport time:       336 |        336 |             pandas.core.arrays.arrow.accessors\nimport time:       279 |        279 |               unicodedata\nimport time:       340 |        340 |               pandas.util._validators\nimport time:       371 |        371 |               pandas.core.missing\nimport time:       541 |        541 |                   pandas._libs.ops\nimport time:       155 |        155 |                   pandas.core.roperator\nimport time:       146 |        146 |                   pandas.core.computation\nimport time:       215 |        215 |                     pandas.core.computation.check\nimport time:       314 |        528 |                   pandas.core.computation.expressions\nimport time:       135 |        135 |                   pandas.core.ops.missing\nimport time:        98 |         98 |                   pandas.core.ops.dispatch\nimport time:        95 |         95 |                   pandas.core.ops.invalid\nimport time:       396 |       2090 |                 pandas.core.ops.array_ops\nimport time:       120 |        120 |                 pandas.core.ops.common\nimport time:       213 |        213 |                 pandas.core.ops.docstrings\nimport time:       147 |        147 |                 pandas.core.ops.mask_ops\nimport time:       239 |       2807 |               pandas.core.ops\nimport time:       346 |        346 |               pandas.core.arraylike\nimport time:       373 |        373 |               pandas.core.arrays._arrow_string_mixins\nimport time:       180 |        180 |               pandas.core.arrays._utils\nimport time:       342 |        342 |                 pandas.compat.numpy.function\nimport time:       167 |        167 |                 pandas.core.array_algos.quantile\nimport time:       263 |        263 |                 pandas.core.sorting\nimport time:      1197 |       1967 |               pandas.core.arrays.base\nimport time:      1022 |       1022 |                 pandas.core.nanops\nimport time:       192 |        192 |                 pandas.core.array_algos.masked_accumulations\nimport time:       156 |        156 |                 pandas.core.array_algos.masked_reductions\nimport time:       371 |        371 |                   pandas.core.util\nimport time:       368 |        368 |                   pandas._libs.hashing\nimport time:       346 |       1084 |                 pandas.core.util.hashing\nimport time:      1080 |       3532 |               pandas.core.arrays.masked\nimport time:       311 |        311 |                 pandas._libs.arrays\nimport time:       293 |        293 |                   pandas.core.arrays.numeric\nimport time:       281 |        574 |                 pandas.core.arrays.floating\nimport time:       344 |        344 |                 pandas.core.arrays.integer\nimport time:       168 |        168 |                     pandas.core.array_algos.transforms\nimport time:       766 |        933 |                   pandas.core.arrays._mixins\nimport time:       152 |        152 |                     pandas.core.strings\nimport time:       237 |        237 |                     pandas.core.strings.base\nimport time:       513 |        901 |                   pandas.core.strings.object_array\nimport time:       365 |       2199 |                 pandas.core.arrays.numpy_\nimport time:       119 |        119 |                 pandas.io.formats\nimport time:       171 |        171 |                       fnmatch\nimport time:       308 |        308 |                       zlib\nimport time:       794 |       1272 |                     shutil\nimport time:       135 |       1406 |                   pandas.io.formats.console\nimport time:       450 |       1856 |                 pandas.io.formats.printing\nimport time:       829 |       6228 |               pandas.core.arrays.string_\nimport time:       155 |        155 |                 pandas.tseries\nimport time:       463 |        618 |               pandas.tseries.frequencies\nimport time:      2373 |      19410 |             pandas.core.arrays.arrow.array\nimport time:       883 |      20629 |           pandas.core.arrays.arrow\nimport time:       312 |        312 |           pandas.core.arrays.boolean\nimport time:       239 |        239 |               _csv\nimport time:       490 |        728 |             csv\nimport time:       335 |        335 |             pandas.core.accessor\nimport time:       678 |        678 |             pandas.core.base\nimport time:      1304 |       3044 |           pandas.core.arrays.categorical\nimport time:       543 |        543 |             pandas._libs.tslib\nimport time:       194 |        194 |               pandas.core.array_algos.datetimelike_accumulations\nimport time:      1642 |       1835 |             pandas.core.arrays.datetimelike\nimport time:       509 |        509 |             pandas.core.arrays._ranges\nimport time:       201 |        201 |             pandas.tseries.offsets\nimport time:       804 |       3889 |           pandas.core.arrays.datetimes\nimport time:       620 |        620 |             pandas.core.arrays.timedeltas\nimport time:      1529 |       2148 |           pandas.core.arrays.interval\nimport time:       679 |        679 |           pandas.core.arrays.period\nimport time:       590 |        590 |                 pandas._libs.sparse\nimport time:      1067 |       1656 |               pandas.core.arrays.sparse.array\nimport time:       350 |       2006 |             pandas.core.arrays.sparse.accessor\nimport time:       159 |       2165 |           pandas.core.arrays.sparse\nimport time:       546 |        546 |           pandas.core.arrays.string_arrow\nimport time:       274 |      33682 |         pandas.core.arrays\nimport time:       253 |        253 |         pandas.core.flags\nimport time:       553 |        553 |               pandas._libs.internals\nimport time:       136 |        136 |                 pandas.core._numba\nimport time:       372 |        507 |               pandas.core._numba.executor\nimport time:       914 |       1973 |             pandas.core.apply\nimport time:        82 |         82 |                 gc\nimport time:       221 |        221 |                       _json\nimport time:       464 |        684 |                     json.scanner\nimport time:       598 |       1281 |                   json.decoder\nimport time:       466 |        466 |                   json.encoder\nimport time:       284 |       2031 |                 json\nimport time:       304 |        304 |                   pandas._libs.indexing\nimport time:       126 |        126 |                     pandas.core.indexes\nimport time:      1032 |       1032 |                       pandas._libs.index\nimport time:       381 |        381 |                       pandas._libs.writers\nimport time:       640 |        640 |                       pandas._libs.join\nimport time:       220 |        220 |                       pandas.core.array_algos.putmask\nimport time:       218 |        218 |                       pandas.core.indexes.frozen\nimport time:      2683 |       2683 |                       pandas.core.strings.accessor\nimport time:      2892 |       8062 |                     pandas.core.indexes.base\nimport time:       303 |        303 |                       pandas.core.indexes.extension\nimport time:       438 |        741 |                     pandas.core.indexes.category\nimport time:       624 |        624 |                         pandas.core.indexes.range\nimport time:       161 |        161 |                           pandas.core.tools\nimport time:       326 |        487 |                         pandas.core.tools.timedeltas\nimport time:       820 |       1931 |                       pandas.core.indexes.datetimelike\nimport time:       207 |        207 |                       pandas.core.tools.times\nimport time:       765 |       2901 |                     pandas.core.indexes.datetimes\nimport time:      1559 |       1559 |                       pandas.core.indexes.multi\nimport time:       341 |        341 |                       pandas.core.indexes.timedeltas\nimport time:      3873 |       5772 |                     pandas.core.indexes.interval\nimport time:       547 |        547 |                     pandas.core.indexes.period\nimport time:       407 |      18552 |                   pandas.core.indexes.api\nimport time:      1544 |      20399 |                 pandas.core.indexing\nimport time:       233 |        233 |                 pandas.core.sample\nimport time:       261 |        261 |                 pandas.core.array_algos.replace\nimport time:      1484 |       1484 |                     pandas.core.internals.blocks\nimport time:       237 |       1721 |                   pandas.core.internals.api\nimport time:       414 |        414 |                     pandas.core.internals.base\nimport time:       513 |        513 |                       pandas.core.internals.ops\nimport time:      1174 |       1686 |                     pandas.core.internals.managers\nimport time:       826 |       2925 |                   pandas.core.internals.array_manager\nimport time:       315 |        315 |                   pandas.core.internals.concat\nimport time:       196 |       5154 |                 pandas.core.internals\nimport time:       364 |        364 |                 pandas.core.internals.construction\nimport time:       161 |        161 |                   pandas.core.methods\nimport time:       106 |        106 |                     pandas.core.reshape\nimport time:       694 |        799 |                   pandas.core.reshape.concat\nimport time:       440 |        440 |                       gzip\nimport time:       343 |        343 |                       mmap\nimport time:       515 |        515 |                           glob\nimport time:       566 |       1081 |                         pathlib._abc\nimport time:        90 |         90 |                             _winapi\nimport time:        63 |         63 |                             nt\nimport time:        85 |         85 |                             nt\nimport time:        56 |         56 |                             nt\nimport time:        49 |         49 |                             nt\nimport time:        42 |         42 |                             nt\nimport time:        41 |         41 |                             nt\nimport time:        47 |         47 |                             nt\nimport time:       341 |        811 |                           ntpath\nimport time:        84 |         84 |                           pwd\nimport time:       245 |        245 |                           grp\nimport time:       995 |       2133 |                         pathlib._local\nimport time:       247 |       3460 |                       pathlib\nimport time:      1480 |       1480 |                       tarfile\nimport time:       154 |        154 |                         urllib\nimport time:      1641 |       1641 |                         ipaddress\nimport time:      1790 |       3585 |                       urllib.parse\nimport time:       236 |        236 |                           zipfile._path.glob\nimport time:       662 |        897 |                         zipfile._path\nimport time:      1525 |       2422 |                       zipfile\nimport time:       171 |        171 |                       pandas.core.shared_docs\nimport time:      2400 |      14296 |                     pandas.io.common\nimport time:      1040 |      15335 |                   pandas.io.formats.format\nimport time:       499 |      16794 |                 pandas.core.methods.describe\nimport time:       114 |        114 |                       pandas._libs.window\nimport time:       756 |        869 |                     pandas._libs.window.aggregations\nimport time:       451 |        451 |                       pandas._libs.window.indexers\nimport time:       671 |       1121 |                     pandas.core.indexers.objects\nimport time:       264 |        264 |                     pandas.core.util.numba_\nimport time:       252 |        252 |                     pandas.core.window.common\nimport time:       345 |        345 |                     pandas.core.window.doc\nimport time:       680 |        680 |                     pandas.core.window.numba_\nimport time:       317 |        317 |                     pandas.core.window.online\nimport time:      2733 |       2733 |                     pandas.core.window.rolling\nimport time:      1056 |       7634 |                   pandas.core.window.ewm\nimport time:      1519 |       1519 |                   pandas.core.window.expanding\nimport time:       300 |       9452 |                 pandas.core.window\nimport time:      5758 |      60524 |               pandas.core.generic\nimport time:       423 |        423 |               pandas.core.methods.selectn\nimport time:       140 |        140 |                 pandas.core.reshape.util\nimport time:       291 |        291 |                 pandas.core.tools.numeric\nimport time:       420 |        850 |               pandas.core.reshape.melt\nimport time:       539 |        539 |                 pandas._libs.reshape\nimport time:       796 |        796 |                 pandas.core.indexes.accessors\nimport time:       136 |        136 |                   pandas.arrays\nimport time:       919 |       1055 |                 pandas.core.tools.datetimes\nimport time:      1028 |       1028 |                 pandas.io.formats.info\nimport time:      1023 |       1023 |                   pandas.plotting._core\nimport time:       285 |        285 |                   pandas.plotting._misc\nimport time:       189 |       1496 |                 pandas.plotting\nimport time:      4251 |       9162 |               pandas.core.series\nimport time:      7300 |      78258 |             pandas.core.frame\nimport time:      1289 |       1289 |             pandas.core.groupby.base\nimport time:       876 |        876 |               pandas._libs.groupby\nimport time:       124 |        124 |                 pandas.core.groupby.categorical\nimport time:       495 |        618 |               pandas.core.groupby.grouper\nimport time:      1026 |       2519 |             pandas.core.groupby.ops\nimport time:       190 |        190 |               pandas.core.groupby.numba_\nimport time:       370 |        370 |               pandas.core.groupby.indexing\nimport time:      2198 |       2757 |             pandas.core.groupby.groupby\nimport time:      2053 |      88846 |           pandas.core.groupby.generic\nimport time:       152 |      88997 |         pandas.core.groupby\nimport time:       264 |     173623 |       pandas.core.api\nimport time:       190 |        190 |       pandas.tseries.api\nimport time:       107 |        107 |               pandas.core.computation.common\nimport time:       198 |        304 |             pandas.core.computation.align\nimport time:       327 |        327 |                 pprint\nimport time:       293 |        620 |               pandas.core.computation.scope\nimport time:       502 |       1121 |             pandas.core.computation.ops\nimport time:       207 |       1631 |           pandas.core.computation.engines\nimport time:       477 |        477 |             pandas.core.computation.parsing\nimport time:      1224 |       1701 |           pandas.core.computation.expr\nimport time:       216 |       3548 |         pandas.core.computation.eval\nimport time:       116 |       3664 |       pandas.core.computation.api\nimport time:       266 |        266 |         pandas.core.reshape.encoding\nimport time:       326 |        326 |             _uuid\nimport time:       574 |        899 |           uuid\nimport time:       953 |       1852 |         pandas.core.reshape.merge\nimport time:       753 |        753 |         pandas.core.reshape.pivot\nimport time:       291 |        291 |         pandas.core.reshape.tile\nimport time:       231 |       3391 |       pandas.core.reshape.api\nimport time:       168 |        168 |         pandas.api.extensions\nimport time:        96 |         96 |         pandas.api.indexers\nimport time:       120 |        120 |             pandas.core.interchange\nimport time:      1104 |       1224 |           pandas.core.interchange.dataframe_protocol\nimport time:       478 |        478 |             pandas.core.interchange.utils\nimport time:       356 |        834 |           pandas.core.interchange.from_dataframe\nimport time:       129 |       2185 |         pandas.api.interchange\nimport time:       118 |        118 |           pandas.core.dtypes.api\nimport time:       201 |        319 |         pandas.api.types\nimport time:      1337 |       1337 |           pandas.core.resample\nimport time:       256 |        256 |                 pandas._libs.json\nimport time:       252 |        252 |                 pandas.io.json._normalize\nimport time:       210 |        210 |                 pandas.io.json._table_schema\nimport time:       646 |        646 |                       pandas._libs.parsers\nimport time:       697 |        697 |                         pandas.io.parsers.base_parser\nimport time:       273 |        969 |                       pandas.io.parsers.arrow_parser_wrapper\nimport time:       292 |        292 |                       pandas.io.parsers.c_parser_wrapper\nimport time:       530 |        530 |                       pandas.io.parsers.python_parser\nimport time:      2356 |       4792 |                     pandas.io.parsers.readers\nimport time:       151 |       4943 |                   pandas.io.parsers\nimport time:        31 |       4973 |                 pandas.io.parsers.readers\nimport time:      1005 |       6694 |               pandas.io.json._json\nimport time:       172 |       6865 |             pandas.io.json\nimport time:        34 |       6899 |           pandas.io.json._json\nimport time:      1643 |       1643 |           pandas.io.stata\nimport time:       184 |      10061 |         pandas.api.typing\nimport time:       235 |      13061 |       pandas.api\nimport time:       432 |        432 |               tempfile\nimport time:       219 |        651 |             pandas._testing.contexts\nimport time:       218 |        868 |           pandas._testing._io\nimport time:       228 |        228 |           pandas._testing._warnings\nimport time:       285 |        285 |               cmath\nimport time:       308 |        593 |             pandas._libs.testing\nimport time:       363 |        955 |           pandas._testing.asserters\nimport time:       131 |        131 |           pandas._testing.compat\nimport time:       550 |       2730 |         pandas._testing\nimport time:       138 |       2867 |       pandas.testing\nimport time:       210 |        210 |       pandas.util._print_versions\nimport time:       232 |        232 |         pandas.io.clipboards\nimport time:      3210 |       3210 |             pandas.io.excel._util\nimport time:       502 |        502 |             pandas.io.excel._calamine\nimport time:       392 |        392 |             pandas.io.excel._odfreader\nimport time:       514 |        514 |             pandas.io.excel._openpyxl\nimport time:       282 |        282 |             pandas.io.excel._pyxlsb\nimport time:       309 |        309 |             pandas.io.excel._xlrd\nimport time:      1479 |       6686 |           pandas.io.excel._base\nimport time:       331 |        331 |           pandas.io.excel._odswriter\nimport time:       251 |        251 |           pandas.io.excel._xlsxwriter\nimport time:       144 |       7411 |         pandas.io.excel\nimport time:       207 |        207 |         pandas.io.feather_format\nimport time:       231 |        231 |         pandas.io.gbq\nimport time:       761 |        761 |         pandas.io.html\nimport time:       169 |        169 |         pandas.io.orc\nimport time:       534 |        534 |         pandas.io.parquet\nimport time:       277 |        277 |           pandas.compat.pickle_compat\nimport time:       287 |        564 |         pandas.io.pickle\nimport time:       612 |        612 |           pandas.core.computation.pytables\nimport time:      2449 |       3060 |         pandas.io.pytables\nimport time:       377 |        377 |           pandas.io.sas.sasreader\nimport time:       205 |        582 |         pandas.io.sas\nimport time:       209 |        209 |         pandas.io.spss\nimport time:       945 |        945 |         pandas.io.sql\nimport time:       693 |        693 |         pandas.io.xml\nimport time:       277 |      15869 |       pandas.io.api\nimport time:       162 |        162 |       pandas.util._tester\nimport time:       102 |        102 |       pandas._version_meson\nimport time:       493 |     241697 |     pandas\nimport time:       473 |        473 |       numpy._typing._add_docstring\nimport time:       221 |        693 |     numpy.typing\nimport time:       229 |        229 |         _colorize\nimport time:      1233 |       1462 |       traceback\nimport time:       333 |       1795 |     optimagic.exceptions\nimport time:       140 |        140 |       optimagic.optimization\nimport time:       237 |        377 |     optimagic.optimization.algo_options\nimport time:       523 |        523 |           _socket\nimport time:      1308 |       1830 |         typing_extensions\nimport time:      7809 |       9639 |       annotated_types\nimport time:      3679 |      13317 |     optimagic.typing\nimport time:      6112 |     378101 |   optimagic.constraints\nimport time:       266 |        266 |                 sqlalchemy.util.preloaded\nimport time:        88 |         88 |                     sqlalchemy.cyextension\nimport time:       505 |        505 |                     sqlalchemy.cyextension.collections\nimport time:       285 |        285 |                     sqlalchemy.cyextension.immutabledict\nimport time:       256 |        256 |                     sqlalchemy.cyextension.processors\nimport time:       239 |        239 |                     sqlalchemy.cyextension.resultproxy\nimport time:       150 |        150 |                             email\nimport time:       441 |        441 |                             importlib.metadata._meta\nimport time:       307 |        307 |                             importlib.metadata._collections\nimport time:       139 |        139 |                             importlib.metadata._functools\nimport time:       108 |        108 |                             importlib.metadata._itertools\nimport time:       392 |        392 |                                   importlib.resources.abc\nimport time:       587 |        978 |                                 importlib.resources._common\nimport time:       238 |        238 |                                 importlib.resources._functional\nimport time:       238 |       1453 |                               importlib.resources\nimport time:       428 |       1881 |                             importlib.abc\nimport time:      1504 |       4527 |                           importlib.metadata\nimport time:       597 |       5124 |                         sqlalchemy.util.compat\nimport time:      1441 |       6564 |                       sqlalchemy.exc\nimport time:       594 |       7158 |                     sqlalchemy.cyextension.util\nimport time:       276 |       8804 |                   sqlalchemy.util._has_cy\nimport time:       992 |        992 |                   sqlalchemy.util.typing\nimport time:      1128 |      10923 |                 sqlalchemy.util._collections\nimport time:       134 |        134 |                         concurrent\nimport time:       128 |        128 |                             atexit\nimport time:      1899 |       2026 |                           logging\nimport time:       940 |       2966 |                         concurrent.futures._base\nimport time:       191 |       3290 |                       concurrent.futures\nimport time:       209 |        209 |                         _heapq\nimport time:       338 |        546 |                       heapq\nimport time:       307 |        307 |                         array\nimport time:      1753 |       2059 |                       socket\nimport time:      1436 |       1436 |                         _ssl\nimport time:      2532 |       3967 |                       ssl\nimport time:       337 |        337 |                       asyncio.constants\nimport time:       215 |        215 |                       asyncio.coroutines\nimport time:       132 |        132 |                         asyncio.format_helpers\nimport time:       159 |        159 |                           asyncio.base_futures\nimport time:       253 |        253 |                           asyncio.exceptions\nimport time:       152 |        152 |                           asyncio.base_tasks\nimport time:       431 |        994 |                         _asyncio\nimport time:       649 |       1773 |                       asyncio.events\nimport time:       273 |        273 |                       asyncio.futures\nimport time:       228 |        228 |                       asyncio.protocols\nimport time:       317 |        317 |                         asyncio.transports\nimport time:       143 |        143 |                         asyncio.log\nimport time:      1067 |       1526 |                       asyncio.sslproto\nimport time:       122 |        122 |                           asyncio.mixins\nimport time:       575 |        697 |                         asyncio.locks\nimport time:       307 |        307 |                           asyncio.queues\nimport time:       531 |        531 |                           asyncio.timeouts\nimport time:       645 |       1482 |                         asyncio.tasks\nimport time:       266 |       2445 |                       asyncio.staggered\nimport time:       233 |        233 |                       asyncio.trsock\nimport time:      1149 |      18035 |                     asyncio.base_events\nimport time:       352 |        352 |                     asyncio.runners\nimport time:       565 |        565 |                     asyncio.streams\nimport time:       355 |        355 |                     asyncio.subprocess\nimport time:       193 |        193 |                     asyncio.taskgroups\nimport time:       103 |        103 |                     asyncio.threads\nimport time:       299 |        299 |                       asyncio.base_subprocess\nimport time:       980 |        980 |                       asyncio.selector_events\nimport time:       975 |       2253 |                     asyncio.unix_events\nimport time:       260 |      22113 |                   asyncio\nimport time:       376 |        376 |                     greenlet._greenlet\nimport time:       255 |        631 |                   greenlet\nimport time:      2209 |       2209 |                     sqlalchemy.util.langhelpers\nimport time:       385 |       2594 |                   sqlalchemy.util._concurrency_py3k\nimport time:       228 |      25564 |                 sqlalchemy.util.concurrency\nimport time:       423 |        423 |                 sqlalchemy.util.deprecations\nimport time:       372 |      37546 |               sqlalchemy.util\nimport time:      1043 |       1043 |                                 sqlalchemy.event.registry\nimport time:       633 |       1676 |                               sqlalchemy.event.legacy\nimport time:      1139 |       2814 |                             sqlalchemy.event.attr\nimport time:       891 |       3705 |                           sqlalchemy.event.base\nimport time:       229 |       3934 |                         sqlalchemy.event.api\nimport time:       224 |       4158 |                       sqlalchemy.event\nimport time:       487 |        487 |                             sqlalchemy.log\nimport time:      2670 |       3157 |                           sqlalchemy.pool.base\nimport time:      1338 |       4494 |                         sqlalchemy.pool.events\nimport time:       431 |        431 |                           sqlalchemy.util.queue\nimport time:       575 |       1006 |                         sqlalchemy.pool.impl\nimport time:       270 |       5768 |                       sqlalchemy.pool\nimport time:       869 |        869 |                             sqlalchemy.sql.roles\nimport time:       441 |        441 |                             sqlalchemy.inspection\nimport time:      1922 |       3231 |                           sqlalchemy.sql._typing\nimport time:      4752 |       4752 |                             sqlalchemy.sql.visitors\nimport time:      1345 |       1345 |                             sqlalchemy.sql.cache_key\nimport time:      1049 |       1049 |                               sqlalchemy.sql.operators\nimport time:       752 |       1800 |                             sqlalchemy.sql.traversals\nimport time:      3486 |      11381 |                           sqlalchemy.sql.base\nimport time:      1615 |       1615 |                             sqlalchemy.sql.coercions\nimport time:       524 |        524 |                                   sqlalchemy.sql.annotation\nimport time:      2209 |       2209 |                                       sqlalchemy.sql.type_api\nimport time:      7194 |       9402 |                                     sqlalchemy.sql.elements\nimport time:       267 |        267 |                                     sqlalchemy.util.topological\nimport time:      2191 |      11859 |                                   sqlalchemy.sql.ddl\nimport time:       206 |        206 |                                           sqlalchemy.engine._py_processors\nimport time:       218 |        424 |                                         sqlalchemy.engine.processors\nimport time:      3275 |       3698 |                                       sqlalchemy.sql.sqltypes\nimport time:      9989 |      13686 |                                     sqlalchemy.sql.selectable\nimport time:      5182 |      18868 |                                   sqlalchemy.sql.schema\nimport time:      1133 |      32382 |                                 sqlalchemy.sql.util\nimport time:      4185 |      36567 |                               sqlalchemy.sql.dml\nimport time:      1175 |      37741 |                             sqlalchemy.sql.crud\nimport time:      3706 |       3706 |                             sqlalchemy.sql.functions\nimport time:      7097 |      50157 |                           sqlalchemy.sql.compiler\nimport time:       128 |        128 |                             sqlalchemy.sql._dml_constructors\nimport time:       373 |        373 |                             sqlalchemy.sql._elements_constructors\nimport time:       399 |        399 |                             sqlalchemy.sql._selectable_constructors\nimport time:      1075 |       1075 |                             sqlalchemy.sql.lambdas\nimport time:       439 |       2413 |                           sqlalchemy.sql.expression\nimport time:       394 |        394 |                           sqlalchemy.sql.default_comparator\nimport time:       832 |        832 |                             sqlalchemy.sql.events\nimport time:       570 |       1402 |                           sqlalchemy.sql.naming\nimport time:      7128 |      76103 |                         sqlalchemy.sql\nimport time:        26 |      76128 |                       sqlalchemy.sql.compiler\nimport time:      3429 |      89481 |                     sqlalchemy.engine.interfaces\nimport time:       390 |        390 |                     sqlalchemy.engine.util\nimport time:      4782 |      94652 |                   sqlalchemy.engine.base\nimport time:      2004 |      96655 |                 sqlalchemy.engine.events\nimport time:       143 |        143 |                     sqlalchemy.dialects\nimport time:       992 |       1135 |                   sqlalchemy.engine.url\nimport time:       241 |        241 |                   sqlalchemy.engine.mock\nimport time:       811 |       2186 |                 sqlalchemy.engine.create\nimport time:      1295 |       1295 |                     sqlalchemy.engine.row\nimport time:      2347 |       3642 |                   sqlalchemy.engine.result\nimport time:      1197 |       4838 |                 sqlalchemy.engine.cursor\nimport time:      2189 |       2189 |                 sqlalchemy.engine.reflection\nimport time:       297 |     106164 |               sqlalchemy.engine\nimport time:       240 |        240 |               sqlalchemy.schema\nimport time:       221 |        221 |               sqlalchemy.types\nimport time:       265 |        265 |                 sqlalchemy.engine.characteristics\nimport time:      1502 |       1767 |               sqlalchemy.engine.default\nimport time:       638 |     146573 |             sqlalchemy\nimport time:      1027 |       1027 |                 cloudpickle.cloudpickle\nimport time:       240 |       1266 |               cloudpickle\nimport time:       600 |       1865 |             optimagic.logging.base\nimport time:        90 |         90 |                           jax\nimport time:       155 |        245 |                         pybaum.config\nimport time:       180 |        424 |                       pybaum.registry_entries\nimport time:       134 |        558 |                     pybaum.registry\nimport time:        92 |         92 |                       pybaum.equality\nimport time:       110 |        110 |                       pybaum.typecheck\nimport time:       165 |        365 |                     pybaum.tree_util\nimport time:       139 |       1061 |                   pybaum\nimport time:       105 |        105 |                     optimagic.parameters\nimport time:       192 |        296 |                   optimagic.parameters.tree_registry\nimport time:       675 |        675 |                     difflib\nimport time:       390 |        390 |                         scipy.__config__\nimport time:       109 |        109 |                         scipy.version\nimport time:        34 |         34 |                           scipy._distributor_init_local\nimport time:       130 |        163 |                         scipy._distributor_init\nimport time:        73 |         73 |                             cython\nimport time:       396 |        468 |                           scipy._lib._testutils\nimport time:        96 |        564 |                         scipy._lib\nimport time:       494 |        494 |                         scipy._lib._pep440\nimport time:       420 |        420 |                           scipy._lib._ccallback_c\nimport time:       297 |        716 |                         scipy._lib._ccallback\nimport time:       449 |       2883 |                       scipy\nimport time:      2377 |       2377 |                           scipy.linalg._fblas\nimport time:        51 |         51 |                           scipy.linalg._cblas\nimport time:        29 |         29 |                           scipy.linalg._fblas_64\nimport time:       337 |       2792 |                         scipy.linalg.blas\nimport time:      1575 |       1575 |                           scipy.linalg._flapack\nimport time:       116 |        116 |                           scipy.linalg._clapack\nimport time:        31 |         31 |                           scipy.linalg._flapack_64\nimport time:       830 |       2550 |                         scipy.linalg.lapack\nimport time:       263 |       5604 |                       scipy.linalg._misc\nimport time:       436 |        436 |                         scipy._cyutility\nimport time:      1449 |       1449 |                         scipy.linalg.cython_lapack\nimport time:      1323 |       1323 |                                   scipy._lib.array_api_compat.common._typing\nimport time:       597 |       1920 |                                 scipy._lib.array_api_compat.common._helpers\nimport time:       148 |       2067 |                               scipy._lib.array_api_compat.common\nimport time:       163 |       2230 |                             scipy._lib.array_api_compat\nimport time:       470 |        470 |                                 numpy.core._utils\nimport time:       250 |        719 |                               numpy.core\nimport time:       245 |        245 |                                   numpy.polynomial.polyutils\nimport time:       533 |        533 |                                   numpy.polynomial._polybase\nimport time:       969 |       1746 |                                 numpy.polynomial.chebyshev\nimport time:       778 |        778 |                                 numpy.polynomial.hermite\nimport time:     32707 |      32707 |                                 numpy.polynomial.hermite_e\nimport time:     20752 |      20752 |                                 numpy.polynomial.laguerre\nimport time:     15485 |      15485 |                                 numpy.polynomial.legendre\nimport time:      2137 |       2137 |                                 numpy.polynomial.polynomial\nimport time:       252 |      73855 |                               numpy.polynomial\nimport time:       287 |        287 |                                     unittest.util\nimport time:       433 |        719 |                                   unittest.result\nimport time:       979 |        979 |                                   unittest.case\nimport time:       377 |        377 |                                   unittest.suite\nimport time:       732 |        732 |                                   unittest.loader\nimport time:      1072 |       1072 |                                       gettext\nimport time:      1711 |       2783 |                                     argparse\nimport time:       165 |        165 |                                       unittest.signals\nimport time:       354 |        519 |                                     unittest.runner\nimport time:       513 |       3814 |                                   unittest.main\nimport time:       928 |       7548 |                                 unittest\nimport time:       375 |        375 |                                 numpy.testing._private\nimport time:       290 |        290 |                                 numpy.testing.overrides\nimport time:       588 |        588 |                                 numpy.testing._private.extbuild\nimport time:      3712 |       3712 |                                 numpy.testing._private.utils\nimport time:       361 |      12871 |                               numpy.testing\nimport time:       423 |        423 |                                 numpy.fft._helper\nimport time:      1153 |       1153 |                                   numpy.fft._pocketfft_umath\nimport time:       568 |       1720 |                                 numpy.fft._pocketfft\nimport time:       337 |        337 |                                 numpy.fft.helper\nimport time:       271 |       2750 |                               numpy.fft\nimport time:       851 |        851 |                                   numpy._core.strings\nimport time:       212 |        212 |                                   numpy.strings\nimport time:      2173 |       3235 |                                 numpy._core.defchararray\nimport time:       208 |       3442 |                               numpy.char\nimport time:       212 |        212 |                               numpy.rec\nimport time:      1718 |       1718 |                                 numpy.ctypeslib._ctypeslib\nimport time:       266 |       1983 |                               numpy.ctypeslib\nimport time:       391 |        391 |                                 numpy.f2py.diagnose\nimport time:       156 |        156 |                                   numpy.f2py._backends\nimport time:        94 |         94 |                                   numpy.f2py.__version__\nimport time:       349 |        349 |                                     numpy.f2py.cfuncs\nimport time:       859 |       1207 |                                   numpy.f2py.auxfuncs\nimport time:      2044 |       2044 |                                     numpy.f2py.cb_rules\nimport time:       248 |        248 |                                     numpy.f2py._isocbind\nimport time:       277 |        277 |                                       fileinput\nimport time:      1125 |       1125 |                                             charset_normalizer.constant\nimport time:       319 |        319 |                                                 _multibytecodec\nimport time:       721 |       1039 |                                               charset_normalizer.utils\nimport time:       492 |       1530 |                                             charset_normalizer.md\nimport time:       495 |        495 |                                             charset_normalizer.models\nimport time:       294 |       3443 |                                           charset_normalizer.cd\nimport time:       460 |       3902 |                                         charset_normalizer.api\nimport time:       267 |        267 |                                         charset_normalizer.legacy\nimport time:       106 |        106 |                                         charset_normalizer.version\nimport time:       251 |       4524 |                                       charset_normalizer\nimport time:      1346 |       1346 |                                       numpy.f2py.symbolic\nimport time:     10967 |      17113 |                                     numpy.f2py.crackfortran\nimport time:       688 |      20092 |                                   numpy.f2py.capi_maps\nimport time:       196 |        196 |                                     numpy.f2py.func2subr\nimport time:       301 |        497 |                                   numpy.f2py.f90mod_rules\nimport time:       152 |        152 |                                     numpy.f2py.common_rules\nimport time:       108 |        108 |                                     numpy.f2py.use_rules\nimport time:      4928 |       5188 |                                   numpy.f2py.rules\nimport time:       738 |      27969 |                                 numpy.f2py.f2py2e\nimport time:       321 |      28680 |                               numpy.f2py\nimport time:       261 |        261 |                                 scipy._lib.array_api_compat._internal\nimport time:      1343 |       1343 |                                 scipy._lib.array_api_compat.common._aliases\nimport time:       442 |        442 |                                   scipy._lib.array_api_compat.numpy._typing\nimport time:       263 |        704 |                                 scipy._lib.array_api_compat.numpy._info\nimport time:      6310 |       8617 |                               scipy._lib.array_api_compat.numpy._aliases\nimport time:      1518 |       1518 |                                 scipy._lib.array_api_compat.common._linalg\nimport time:      2125 |       3642 |                               scipy._lib.array_api_compat.numpy.linalg\nimport time:       569 |        569 |                                 scipy._lib.array_api_compat.common._fft\nimport time:      2831 |       3399 |                               scipy._lib.array_api_compat.numpy.fft\nimport time:       572 |     140735 |                             scipy._lib.array_api_compat.numpy\nimport time:       180 |        180 |                             scipy._lib._sparse\nimport time:       404 |        404 |                                 pkgutil\nimport time:       316 |        316 |                                   _pyrepl\nimport time:       292 |        607 |                                 _pyrepl.pager\nimport time:      1674 |       2684 |                               pydoc\nimport time:      2579 |       5262 |                             scipy._lib._docscrape\nimport time:      4349 |     152755 |                           scipy._lib._array_api\nimport time:      1135 |     153889 |                         scipy._lib._util\nimport time:      1818 |     157591 |                       scipy.linalg._cythonized_array_utils\nimport time:      4799 |       4799 |                         scipy.linalg._decomp\nimport time:      1891 |       1891 |                         scipy.linalg._decomp_svd\nimport time:       402 |        402 |                         scipy.linalg._solve_toeplitz\nimport time:      3823 |      10914 |                       scipy.linalg._basic\nimport time:       262 |        262 |                         scipy.linalg._decomp_lu_cython\nimport time:       754 |       1016 |                       scipy.linalg._decomp_lu\nimport time:       747 |        747 |                       scipy.linalg._decomp_ldl\nimport time:      1286 |       1286 |                       scipy.linalg._decomp_cholesky\nimport time:      1264 |       1264 |                       scipy.linalg._decomp_qr\nimport time:      1304 |       1304 |                       scipy.linalg._decomp_qz\nimport time:      1004 |       1004 |                       scipy.linalg._decomp_schur\nimport time:       511 |        511 |                       scipy.linalg._decomp_polar\nimport time:       205 |        205 |                         scipy._lib.deprecation\nimport time:       826 |        826 |                         scipy.linalg._expm_frechet\nimport time:       567 |        567 |                         scipy.linalg._matfuncs_schur_sqrtm\nimport time:       552 |        552 |                         scipy.linalg._matfuncs_expm\nimport time:       278 |        278 |                         scipy.linalg._linalg_pythran\nimport time:      2528 |       4954 |                       scipy.linalg._matfuncs\nimport time:       632 |        632 |                       scipy.linalg._special_matrices\nimport time:      2022 |       2022 |                       scipy.linalg._solvers\nimport time:       486 |        486 |                       scipy.linalg._procrustes\nimport time:       418 |        418 |                         scipy.linalg.cython_blas\nimport time:      2000 |       2418 |                       scipy.linalg._decomp_update\nimport time:       457 |        457 |                             scipy.sparse._sputils\nimport time:       251 |        251 |                             scipy.sparse._matrix\nimport time:      1009 |       1715 |                           scipy.sparse._base\nimport time:       299 |        299 |                             scipy.sparse._sparsetools\nimport time:       313 |        313 |                               scipy.sparse._data\nimport time:       314 |        314 |                               scipy.sparse._index\nimport time:       600 |       1226 |                             scipy.sparse._compressed\nimport time:       341 |       1865 |                           scipy.sparse._csr\nimport time:       274 |        274 |                           scipy.sparse._csc\nimport time:       740 |        740 |                             scipy.sparse._csparsetools\nimport time:       442 |       1181 |                           scipy.sparse._lil\nimport time:       506 |        506 |                           scipy.sparse._dok\nimport time:       999 |        999 |                           scipy.sparse._coo\nimport time:       357 |        357 |                           scipy.sparse._dia\nimport time:       489 |        489 |                           scipy.sparse._bsr\nimport time:      1614 |       1614 |                           scipy.sparse._construct\nimport time:       147 |        147 |                           scipy.sparse._extract\nimport time:       160 |        160 |                           scipy.sparse._matrix_io\nimport time:        90 |         90 |                           scipy.sparse.base\nimport time:        80 |         80 |                           scipy.sparse.bsr\nimport time:        74 |         74 |                           scipy.sparse.compressed\nimport time:        77 |         77 |                           scipy.sparse.construct\nimport time:       118 |        118 |                           scipy.sparse.coo\nimport time:        81 |         81 |                           scipy.sparse.csc\nimport time:        81 |         81 |                           scipy.sparse.csr\nimport time:       108 |        108 |                           scipy.sparse.data\nimport time:        75 |         75 |                           scipy.sparse.dia\nimport time:       159 |        159 |                           scipy.sparse.dok\nimport time:        77 |         77 |                           scipy.sparse.extract\nimport time:        69 |         69 |                           scipy.sparse.lil\nimport time:        73 |         73 |                           scipy.sparse.sparsetools\nimport time:        75 |         75 |                           scipy.sparse.sputils\nimport time:       634 |      11167 |                         scipy.sparse\nimport time:       760 |      11926 |                       scipy.linalg._sketches\nimport time:       297 |        297 |                       scipy.linalg._decomp_cossin\nimport time:       105 |        105 |                       scipy.linalg.decomp\nimport time:        86 |         86 |                       scipy.linalg.decomp_cholesky\nimport time:       132 |        132 |                       scipy.linalg.decomp_lu\nimport time:       399 |        399 |                       scipy.linalg.decomp_qr\nimport time:       176 |        176 |                       scipy.linalg.decomp_svd\nimport time:        99 |         99 |                       scipy.linalg.decomp_schur\nimport time:       127 |        127 |                       scipy.linalg.basic\nimport time:        82 |         82 |                       scipy.linalg.misc\nimport time:       136 |        136 |                       scipy.linalg.special_matrices\nimport time:       135 |        135 |                       scipy.linalg.matfuncs\nimport time:      1859 |     210180 |                     scipy.linalg\nimport time:       271 |     211125 |                   optimagic.utilities\nimport time:      3561 |     216042 |                 optimagic.optimization.fun_value\nimport time:      4885 |     220926 |               optimagic.logging.types\nimport time:      1157 |     222082 |             optimagic.logging.sqlalchemy\nimport time:       650 |     371169 |           optimagic.logging.logger\nimport time:       141 |     371310 |         optimagic.logging\nimport time:        34 |     371344 |       optimagic.logging.types\nimport time:      1369 |       1369 |         optimagic.timing\nimport time:      1367 |       2735 |       optimagic.optimization.history\nimport time:       238 |        238 |                 _multiprocessing\nimport time:       392 |        392 |                     multiprocessing.process\nimport time:       442 |        442 |                     multiprocessing.reduction\nimport time:       616 |       1448 |                   multiprocessing.context\nimport time:       355 |       1802 |                 multiprocessing\nimport time:       303 |       2342 |               joblib._multiprocessing_helpers\nimport time:       138 |        138 |                 joblib.externals\nimport time:       175 |        175 |                 joblib.externals.loky._base\nimport time:       429 |        429 |                       multiprocessing.util\nimport time:       458 |        887 |                     multiprocessing.synchronize\nimport time:       876 |        876 |                           _queue\nimport time:       437 |       1312 |                         queue\nimport time:       125 |        125 |                           _winapi\nimport time:       865 |        989 |                         multiprocessing.connection\nimport time:       431 |        431 |                         multiprocessing.queues\nimport time:       686 |       3418 |                       concurrent.futures.process\nimport time:       291 |        291 |                       joblib.externals.loky.backend.process\nimport time:       389 |       4096 |                     joblib.externals.loky.backend.context\nimport time:       140 |       5123 |                   joblib.externals.loky.backend\nimport time:        35 |       5157 |                 joblib.externals.loky.backend.context\nimport time:       179 |        179 |                   joblib.externals.loky.backend._posix_reduction\nimport time:       759 |        759 |                     joblib.externals.cloudpickle.cloudpickle\nimport time:       300 |       1059 |                   joblib.externals.cloudpickle\nimport time:       379 |       1616 |                 joblib.externals.loky.backend.reduction\nimport time:       121 |        121 |                     faulthandler\nimport time:       298 |        298 |                     joblib.externals.loky.backend.queues\nimport time:       117 |        117 |                       psutil\nimport time:       238 |        355 |                     joblib.externals.loky.backend.utils\nimport time:       179 |        179 |                     joblib.externals.loky.initializers\nimport time:       147 |        147 |                     psutil\nimport time:      1030 |       2126 |                   joblib.externals.loky.process_executor\nimport time:       307 |       2433 |                 joblib.externals.loky.reusable_executor\nimport time:       274 |        274 |                 joblib.externals.loky.cloudpickle_wrapper\nimport time:       456 |      10246 |               joblib.externals.loky\nimport time:       201 |      12788 |             joblib._cloudpickle_wrapper\nimport time:      5509 |       5509 |               joblib._utils\nimport time:      1009 |       1009 |               multiprocessing.pool\nimport time:       526 |        526 |                   joblib.backports\nimport time:       252 |        252 |                   joblib.disk\nimport time:       291 |        291 |                         runpy\nimport time:       320 |        610 |                       multiprocessing.spawn\nimport time:       448 |        448 |                       _posixshmem\nimport time:       317 |       1373 |                     multiprocessing.resource_tracker\nimport time:       287 |        287 |                     joblib.externals.loky.backend.spawn\nimport time:       313 |       1972 |                   joblib.externals.loky.backend.resource_tracker\nimport time:       113 |        113 |                       lz4\nimport time:       630 |        742 |                     joblib.compressor\nimport time:       186 |        186 |                       joblib.numpy_pickle_utils\nimport time:       289 |        475 |                     joblib.numpy_pickle_compat\nimport time:       478 |       1694 |                   joblib.numpy_pickle\nimport time:       576 |       5017 |                 joblib._memmapping_reducer\nimport time:       385 |       5402 |               joblib.executor\nimport time:       350 |        350 |               joblib.pool\nimport time:       736 |      13004 |             joblib._parallel_backends\nimport time:       211 |        211 |               joblib.logger\nimport time:       692 |        903 |             joblib._store_backends\nimport time:       398 |        398 |             joblib.hashing\nimport time:       404 |        404 |               joblib.func_inspect\nimport time:      1061 |       1464 |             joblib.memory\nimport time:       789 |        789 |             joblib.parallel\nimport time:       310 |      29653 |           joblib\nimport time:       101 |        101 |             pathos\nimport time:        37 |        137 |           pathos.pools\nimport time:       620 |        620 |                         scipy.sparse.linalg._interface\nimport time:       238 |        238 |                         scipy.sparse.linalg._isolve.utils\nimport time:       313 |       1170 |                       scipy.sparse.linalg._isolve.iterative\nimport time:       192 |        192 |                       scipy.sparse.linalg._isolve.minres\nimport time:       197 |        197 |                         scipy.sparse.linalg._isolve._gcrotmk\nimport time:       260 |        456 |                       scipy.sparse.linalg._isolve.lgmres\nimport time:       227 |        227 |                       scipy.sparse.linalg._isolve.lsqr\nimport time:       168 |        168 |                       scipy.sparse.linalg._isolve.lsmr\nimport time:       137 |        137 |                       scipy.sparse.linalg._isolve.tfqmr\nimport time:       256 |       2603 |                     scipy.sparse.linalg._isolve\nimport time:       822 |        822 |                         scipy.sparse.linalg._dsolve._superlu\nimport time:       109 |        109 |                           scikits\nimport time:        82 |        190 |                         scikits.umfpack\nimport time:       515 |       1526 |                       scipy.sparse.linalg._dsolve.linsolve\nimport time:       175 |        175 |                       scipy.sparse.linalg._dsolve._add_newdocs\nimport time:       260 |       1961 |                     scipy.sparse.linalg._dsolve\nimport time:      1134 |       1134 |                             scipy._lib.decorator\nimport time:       702 |       1836 |                           scipy._lib._threadsafety\nimport time:      1205 |       1205 |                           scipy.sparse.linalg._eigen.arpack._arpack\nimport time:       965 |       4004 |                         scipy.sparse.linalg._eigen.arpack.arpack\nimport time:       230 |       4233 |                       scipy.sparse.linalg._eigen.arpack\nimport time:       386 |        386 |                         scipy.sparse.linalg._eigen.lobpcg.lobpcg\nimport time:       230 |        616 |                       scipy.sparse.linalg._eigen.lobpcg\nimport time:        99 |         99 |                           scipy.sparse.linalg._propack\nimport time:       898 |        898 |                           scipy.sparse.linalg._propack._spropack\nimport time:       967 |        967 |                           scipy.sparse.linalg._propack._dpropack\nimport time:       843 |        843 |                           scipy.sparse.linalg._propack._cpropack\nimport time:       706 |        706 |                           scipy.sparse.linalg._propack._zpropack\nimport time:      2695 |       6207 |                         scipy.sparse.linalg._svdp\nimport time:      1889 |       8095 |                       scipy.sparse.linalg._eigen._svds\nimport time:       211 |      13154 |                     scipy.sparse.linalg._eigen\nimport time:       329 |        329 |                         scipy.sparse.linalg._onenormest\nimport time:       454 |        783 |                       scipy.sparse.linalg._expm_multiply\nimport time:       605 |       1387 |                     scipy.sparse.linalg._matfuncs\nimport time:       230 |        230 |                     scipy.sparse.linalg._norm\nimport time:       559 |        559 |                     scipy.sparse.linalg._special_sparse_arrays\nimport time:       157 |        157 |                     scipy.sparse.linalg.isolve\nimport time:        99 |         99 |                     scipy.sparse.linalg.dsolve\nimport time:        89 |         89 |                     scipy.sparse.linalg.interface\nimport time:        86 |         86 |                     scipy.sparse.linalg.eigen\nimport time:        92 |         92 |                     scipy.sparse.linalg.matfuncs\nimport time:       428 |      20840 |                   scipy.sparse.linalg\nimport time:       216 |        216 |                     scipy.optimize._dcsrch\nimport time:       347 |        563 |                   scipy.optimize._linesearch\nimport time:       277 |        277 |                     scipy.optimize._group_columns\nimport time:       108 |        108 |                         scipy._lib.array_api_extra._lib\nimport time:       119 |        119 |                             scipy._lib.array_api_extra._lib._utils\nimport time:       195 |        195 |                               scipy._lib._array_api_compat_vendor\nimport time:       214 |        409 |                             scipy._lib.array_api_extra._lib._utils._compat\nimport time:       281 |        281 |                               scipy._lib.array_api_extra._lib._utils._typing\nimport time:       371 |        651 |                             scipy._lib.array_api_extra._lib._utils._helpers\nimport time:       553 |       1731 |                           scipy._lib.array_api_extra._lib._at\nimport time:       696 |       2426 |                         scipy._lib.array_api_extra._lib._funcs\nimport time:       259 |       2792 |                       scipy._lib.array_api_extra._delegation\nimport time:       242 |        242 |                       scipy._lib.array_api_extra._lib._lazy\nimport time:       302 |       3335 |                     scipy._lib.array_api_extra\nimport time:       355 |       3966 |                   scipy.optimize._numdiff\nimport time:       386 |        386 |                     scipy.optimize._hessian_update_strategy\nimport time:       456 |        841 |                   scipy.optimize._differentiable_functions\nimport time:      5024 |      31232 |                 scipy.optimize._optimize\nimport time:       223 |        223 |                     scipy.optimize._trustregion\nimport time:       172 |        394 |                   scipy.optimize._trustregion_dogleg\nimport time:       151 |        151 |                   scipy.optimize._trustregion_ncg\nimport time:       328 |        328 |                         scipy._lib.messagestream\nimport time:       453 |        781 |                       scipy.optimize._trlib._trlib\nimport time:       107 |        888 |                     scipy.optimize._trlib\nimport time:        95 |        982 |                   scipy.optimize._trustregion_krylov\nimport time:       248 |        248 |                   scipy.optimize._trustregion_exact\nimport time:       331 |        331 |                       scipy.optimize._constraints\nimport time:        82 |         82 |                             sksparse\nimport time:        34 |        115 |                           sksparse.cholmod\nimport time:       198 |        313 |                         scipy.optimize._trustregion_constr.projections\nimport time:       225 |        225 |                         scipy.optimize._trustregion_constr.qp_subproblem\nimport time:       218 |        755 |                       scipy.optimize._trustregion_constr.equality_constrained_sqp\nimport time:       237 |        237 |                       scipy.optimize._trustregion_constr.canonical_constraint\nimport time:       284 |        284 |                       scipy.optimize._trustregion_constr.tr_interior_point\nimport time:       183 |        183 |                       scipy.optimize._trustregion_constr.report\nimport time:       346 |       2134 |                     scipy.optimize._trustregion_constr.minimize_trustregion_constr\nimport time:       151 |       2285 |                   scipy.optimize._trustregion_constr\nimport time:       550 |        550 |                     scipy.optimize._lbfgsb\nimport time:       306 |        855 |                   scipy.optimize._lbfgsb_py\nimport time:       237 |        237 |                     scipy.optimize._moduleTNC\nimport time:       322 |        558 |                   scipy.optimize._tnc\nimport time:       215 |        215 |                   scipy.optimize._cobyla_py\nimport time:       139 |        139 |                   scipy.optimize._cobyqa_py\nimport time:       550 |        550 |                     scipy.optimize._slsqplib\nimport time:       274 |        824 |                   scipy.optimize._slsqp_py\nimport time:       540 |       7186 |                 scipy.optimize._minimize\nimport time:       220 |        220 |                     scipy.optimize._minpack\nimport time:       579 |        579 |                           scipy.optimize._lsq.common\nimport time:       287 |        865 |                         scipy.optimize._lsq.trf\nimport time:       263 |        263 |                         scipy.optimize._lsq.dogbox\nimport time:       365 |       1492 |                       scipy.optimize._lsq.least_squares\nimport time:       247 |        247 |                           scipy.optimize._lsq.givens_elimination\nimport time:       179 |        426 |                         scipy.optimize._lsq.trf_linear\nimport time:       203 |        203 |                         scipy.optimize._lsq.bvls\nimport time:       192 |        820 |                       scipy.optimize._lsq.lsq_linear\nimport time:       179 |       2490 |                     scipy.optimize._lsq\nimport time:       405 |       3114 |                   scipy.optimize._minpack_py\nimport time:       191 |        191 |                   scipy.optimize._spectral\nimport time:      2166 |       2166 |                   scipy.optimize._nonlin\nimport time:       270 |       5738 |                 scipy.optimize._root\nimport time:       187 |        187 |                     scipy.optimize._zeros\nimport time:       489 |        676 |                   scipy.optimize._zeros_py\nimport time:       260 |        935 |                 scipy.optimize._root_scalar\nimport time:       602 |        602 |                 scipy.optimize._nnls\nimport time:      1666 |       1666 |                 scipy.optimize._basinhopping\nimport time:       104 |        104 |                       scipy.optimize._highspy\nimport time:      5012 |       5012 |                       scipy.optimize._highspy._core\nimport time:       464 |        464 |                       scipy.optimize._highspy._highs_options\nimport time:       423 |       6002 |                     scipy.optimize._highspy._highs_wrapper\nimport time:       191 |       6193 |                   scipy.optimize._linprog_highs\nimport time:       140 |        140 |                                   uarray\nimport time:       499 |        499 |                                       scipy._lib._uarray._uarray\nimport time:       563 |       1061 |                                     scipy._lib._uarray._backend\nimport time:       269 |       1330 |                                   scipy._lib._uarray\nimport time:       200 |       1669 |                                 scipy._lib.uarray\nimport time:      1005 |       2674 |                               scipy.fft._basic\nimport time:       540 |        540 |                               scipy.fft._realtransforms\nimport time:       239 |        239 |                                     scipy.special._sf_error\nimport time:       399 |        399 |                                       scipy.special._ufuncs_cxx\nimport time:       407 |        407 |                                       scipy.special._ellip_harm_2\nimport time:       828 |        828 |                                       scipy.special._special_ufuncs\nimport time:       479 |        479 |                                       scipy.special._gufuncs\nimport time:      1179 |       3290 |                                     scipy.special._ufuncs\nimport time:      9827 |       9827 |                                     scipy.special._support_alternative_backends\nimport time:       161 |        161 |                                       scipy.special._input_validation\nimport time:       913 |        913 |                                       scipy.special._specfun\nimport time:       282 |        282 |                                       scipy.special._comb\nimport time:       511 |        511 |                                       scipy.special._multiufuncs\nimport time:       852 |       2717 |                                     scipy.special._basic\nimport time:      1543 |       1543 |                                     scipy.special._logsumexp\nimport time:       649 |        649 |                                     scipy.special._orthogonal\nimport time:       226 |        226 |                                     scipy.special._spfun_stats\nimport time:       206 |        206 |                                     scipy.special._ellip_harm\nimport time:       147 |        147 |                                     scipy.special._lambertw\nimport time:       179 |        179 |                                     scipy.special._spherical_bessel\nimport time:        97 |         97 |                                     scipy.special.add_newdocs\nimport time:       124 |        124 |                                     scipy.special.basic\nimport time:       150 |        150 |                                     scipy.special.orthogonal\nimport time:       117 |        117 |                                     scipy.special.specfun\nimport time:        82 |         82 |                                     scipy.special.sf_error\nimport time:        75 |         75 |                                     scipy.special.spfun_stats\nimport time:       796 |      20456 |                                   scipy.special\nimport time:       287 |      20742 |                                 scipy.fft._fftlog_backend\nimport time:       281 |      21023 |                               scipy.fft._fftlog\nimport time:       477 |        477 |                                     scipy.fft._pocketfft.pypocketfft\nimport time:       235 |        235 |                                     scipy.fft._pocketfft.helper\nimport time:       416 |       1128 |                                   scipy.fft._pocketfft.basic\nimport time:       258 |        258 |                                   scipy.fft._pocketfft.realtransforms\nimport time:       228 |       1613 |                                 scipy.fft._pocketfft\nimport time:       341 |       1953 |                               scipy.fft._helper\nimport time:       347 |        347 |                                 scipy.fft._basic_backend\nimport time:       288 |        288 |                                 scipy.fft._realtransforms_backend\nimport time:       292 |        926 |                               scipy.fft._backend\nimport time:       268 |      27381 |                             scipy.fft\nimport time:       576 |      27957 |                           scipy.linalg._decomp_interpolative\nimport time:       185 |      28142 |                         scipy.linalg.interpolative\nimport time:       184 |      28325 |                       scipy.optimize._remove_redundancy\nimport time:       621 |      28945 |                     scipy.optimize._linprog_util\nimport time:        99 |         99 |                     sksparse\nimport time:        61 |         61 |                       scikits\nimport time:        24 |         85 |                     scikits.umfpack\nimport time:       361 |      29490 |                   scipy.optimize._linprog_ip\nimport time:       210 |        210 |                   scipy.optimize._linprog_simplex\nimport time:       384 |        384 |                     scipy.optimize._bglu_dense\nimport time:       329 |        712 |                   scipy.optimize._linprog_rs\nimport time:       260 |        260 |                   scipy.optimize._linprog_doc\nimport time:       331 |      37193 |                 scipy.optimize._linprog\nimport time:       225 |        225 |                 scipy.optimize._lsap\nimport time:      2736 |       2736 |                 scipy.optimize._differentialevolution\nimport time:       286 |        286 |                   scipy.optimize._pava_pybind\nimport time:       226 |        511 |                 scipy.optimize._isotonic\nimport time:       523 |        523 |                     scipy.spatial._ckdtree\nimport time:       811 |       1333 |                   scipy.spatial._kdtree\nimport time:       950 |        950 |                   scipy.spatial._qhull\nimport time:       254 |        254 |                     scipy.spatial._voronoi\nimport time:       462 |        715 |                   scipy.spatial._spherical_voronoi\nimport time:       770 |        770 |                   scipy.spatial._plotutils\nimport time:       207 |        207 |                   scipy.spatial._procrustes\nimport time:       397 |        397 |                       scipy.spatial._hausdorff\nimport time:       625 |        625 |                       scipy.spatial._distance_pybind\nimport time:       272 |        272 |                       scipy.spatial._distance_wrap\nimport time:      3163 |       4455 |                     scipy.spatial.distance\nimport time:       261 |       4716 |                   scipy.spatial._geometric_slerp\nimport time:       325 |        325 |                   scipy.spatial.ckdtree\nimport time:       101 |        101 |                   scipy.spatial.kdtree\nimport time:        97 |         97 |                   scipy.spatial.qhull\nimport time:      4884 |       4884 |                             scipy.constants._codata\nimport time:      1524 |       1524 |                             scipy.constants._constants\nimport time:       336 |        336 |                             scipy.constants.codata\nimport time:       447 |        447 |                             scipy.constants.constants\nimport time:      1006 |       8195 |                           scipy.constants\nimport time:       183 |       8377 |                         scipy.spatial.transform._rotation_groups\nimport time:      1185 |       9561 |                       scipy.spatial.transform._rotation\nimport time:       540 |      10101 |                     scipy.spatial.transform._rigid_transform\nimport time:       251 |        251 |                     scipy.spatial.transform._rotation_spline\nimport time:       109 |        109 |                     scipy.spatial.transform.rotation\nimport time:       157 |      10617 |                   scipy.spatial.transform\nimport time:       118 |        118 |                     scipy.optimize._shgo_lib\nimport time:       449 |        449 |                     scipy.optimize._shgo_lib._vertex\nimport time:       536 |       1102 |                   scipy.optimize._shgo_lib._complex\nimport time:      1490 |      22417 |                 scipy.optimize._shgo\nimport time:      1525 |       1525 |                 scipy.optimize._dual_annealing\nimport time:       320 |        320 |                 scipy.optimize._qap\nimport time:       271 |        271 |                   scipy.optimize._direct\nimport time:       418 |        688 |                 scipy.optimize._direct_py\nimport time:       237 |        237 |                 scipy.optimize._milp\nimport time:       111 |        111 |                 scipy.optimize.cobyla\nimport time:        82 |         82 |                 scipy.optimize.lbfgsb\nimport time:        97 |         97 |                 scipy.optimize.linesearch\nimport time:        89 |         89 |                 scipy.optimize.minpack\nimport time:        88 |         88 |                 scipy.optimize.minpack2\nimport time:        78 |         78 |                 scipy.optimize.moduleTNC\nimport time:        96 |         96 |                 scipy.optimize.nonlin\nimport time:        85 |         85 |                 scipy.optimize.optimize\nimport time:        78 |         78 |                 scipy.optimize.slsqp\nimport time:        80 |         80 |                 scipy.optimize.tnc\nimport time:       130 |        130 |                 scipy.optimize.zeros\nimport time:       585 |     114799 |               scipy.optimize\nimport time:      1495 |     116294 |             optimagic.parameters.bounds\nimport time:       500 |     116794 |           optimagic.deprecations\nimport time:       123 |        123 |                   _plotly_utils\nimport time:       207 |        330 |                 _plotly_utils.importers\nimport time:       266 |        596 |               plotly\nimport time:       108 |        108 |                 _plotly_utils.optional_imports\nimport time:        95 |        202 |               plotly.optional_imports\nimport time:       321 |        321 |                 plotly.graph_objs\nimport time:       250 |        250 |                   PIL._version\nimport time:      1675 |       1924 |                 _plotly_utils.basevalidators\nimport time:       551 |        551 |                   plotly.io\nimport time:       191 |        191 |                   plotly.express._special_inputs\nimport time:       270 |        270 |                   plotly.express.trendline_functions\nimport time:       385 |        385 |                       _plotly_utils.exceptions\nimport time:       153 |        153 |                         _plotly_utils.colors._swatches\nimport time:       158 |        158 |                         _plotly_utils.colors.colorbrewer\nimport time:       157 |        157 |                         _plotly_utils.colors.carto\nimport time:       319 |        786 |                       _plotly_utils.colors.qualitative\nimport time:       105 |        105 |                         _plotly_utils.colors.plotlyjs\nimport time:       214 |        214 |                         _plotly_utils.colors.cmocean\nimport time:       205 |        522 |                       _plotly_utils.colors.sequential\nimport time:       132 |        132 |                       _plotly_utils.colors.diverging\nimport time:       121 |        121 |                       _plotly_utils.colors.cyclical\nimport time:      3160 |       5104 |                     _plotly_utils.colors\nimport time:       150 |       5254 |                   plotly.colors\nimport time:       135 |        135 |                   packaging\nimport time:       206 |        206 |                     packaging._structures\nimport time:      2204 |       2409 |                   packaging.version\nimport time:       649 |        649 |                   plotly._subplots\nimport time:       663 |        663 |                     _plotly_utils.utils\nimport time:       192 |        192 |                     plotly.shapeannotation\nimport time:      1504 |       2359 |                   plotly.basedatatypes\nimport time:      5857 |      17671 |                 plotly.express._core\nimport time:       292 |        292 |                 plotly.express.imshow_utils\nimport time:      1149 |       1149 |                     _plotly_utils.png\nimport time:      3786 |       3786 |                       PIL.ExifTags\nimport time:       182 |        182 |                         PIL._deprecate\nimport time:       559 |        741 |                       PIL.ImageMode\nimport time:      1050 |       1050 |                       PIL.TiffTags\nimport time:       154 |        154 |                       PIL._binary\nimport time:       364 |        364 |                         PIL._typing\nimport time:       193 |        557 |                       PIL._util\nimport time:        96 |         96 |                       defusedxml\nimport time:      1268 |       1268 |                       PIL._imaging\nimport time:      2463 |      10111 |                     PIL.Image\nimport time:       190 |      11449 |                   _plotly_utils.data_utils\nimport time:       261 |      11709 |                 plotly.utils\nimport time:        99 |         99 |                 xarray\nimport time:      2149 |      34162 |               plotly.express._imshow\nimport time:       246 |        246 |                 plotly.express._doc\nimport time:     55349 |      55594 |               plotly.express._chart_types\nimport time:       218 |        218 |                 plotly.data\nimport time:       221 |        439 |               plotly.express.data\nimport time:       136 |        136 |               plotly.express.colors\nimport time:       335 |      91462 |             plotly.express\nimport time:        83 |         83 |             petsc4py\nimport time:        60 |         60 |             nlopt\nimport time:        84 |         84 |             pybobyqa\nimport time:        55 |         55 |             dfols\nimport time:        50 |         50 |             pygmo\nimport time:        50 |         50 |             cyipopt\nimport time:        43 |         43 |             fides\nimport time:        46 |         46 |             jax\nimport time:        51 |         51 |             tranquilo\nimport time:       113 |        113 |             numba\nimport time:       151 |        151 |                       iminuit.pdg_format\nimport time:       176 |        176 |                         iminuit.warnings\nimport time:       213 |        389 |                       iminuit._optional_dependencies\nimport time:       223 |        762 |                     iminuit._repr_text\nimport time:       306 |       1067 |                   iminuit._repr_html\nimport time:       152 |        152 |                     iminuit._parse_version\nimport time:       187 |        187 |                         quopri\nimport time:       266 |        266 |                           email._parseaddr\nimport time:       807 |       1072 |                         email.utils\nimport time:       586 |        586 |                         email.errors\nimport time:       288 |        288 |                             email.quoprimime\nimport time:       177 |        177 |                             email.base64mime\nimport time:       152 |        152 |                               email.encoders\nimport time:       384 |        535 |                             email.charset\nimport time:      4052 |       5051 |                           email.header\nimport time:       660 |       5710 |                         email._policybase\nimport time:       464 |        464 |                         email._encoded_words\nimport time:       152 |        152 |                         email.iterators\nimport time:       736 |       8905 |                       email.message\nimport time:       187 |        187 |                       importlib.metadata._text\nimport time:       257 |       9348 |                     importlib.metadata._adapters\nimport time:       752 |        752 |                       email.feedparser\nimport time:       221 |        973 |                     email.parser\nimport time:      1116 |      11587 |                   iminuit._deprecated\nimport time:      2147 |       2147 |                   iminuit.typing\nimport time:      1052 |      15851 |                 iminuit.util\nimport time:      1657 |       1657 |                 iminuit._core\nimport time:      1234 |      18740 |               iminuit.minuit\nimport time:       189 |        189 |               iminuit.minimize\nimport time:       827 |      19756 |             iminuit\nimport time:       322 |        322 |             nevergrad\nimport time:       308 |        308 |                   scipy.stats._warnings_errors\nimport time:       290 |        290 |                         scipy._lib.doccer\nimport time:       291 |        291 |                         scipy.stats._distr_params\nimport time:       871 |        871 |                         scipy.integrate._quadrature\nimport time:       738 |        738 |                           scipy.integrate._odepack\nimport time:       245 |        983 |                         scipy.integrate._odepack_py\nimport time:       266 |        266 |                           scipy.integrate._quadpack\nimport time:       433 |        698 |                         scipy.integrate._quadpack_py\nimport time:       702 |        702 |                           scipy.integrate._vode\nimport time:       309 |        309 |                           scipy.integrate._dop\nimport time:       691 |        691 |                           scipy.integrate._lsoda\nimport time:       639 |       2339 |                         scipy.integrate._ode\nimport time:       356 |        356 |                         scipy.integrate._bvp\nimport time:       285 |        285 |                               scipy.integrate._ivp.common\nimport time:       260 |        260 |                               scipy.integrate._ivp.base\nimport time:       544 |       1088 |                             scipy.integrate._ivp.bdf\nimport time:       543 |        543 |                             scipy.integrate._ivp.radau\nimport time:       181 |        181 |                               scipy.integrate._ivp.dop853_coefficients\nimport time:       702 |        883 |                             scipy.integrate._ivp.rk\nimport time:       422 |        422 |                             scipy.integrate._ivp.lsoda\nimport time:       368 |       3302 |                           scipy.integrate._ivp.ivp\nimport time:       215 |       3517 |                         scipy.integrate._ivp\nimport time:       337 |        337 |                         scipy.integrate._quad_vec\nimport time:       223 |        223 |                           scipy._lib._elementwise_iterative_method\nimport time:       480 |        702 |                         scipy.integrate._tanhsinh\nimport time:       460 |        460 |                             scipy.integrate._rules._base\nimport time:       255 |        255 |                             scipy.integrate._rules._genz_malik\nimport time:       179 |        179 |                               scipy.integrate._rules._gauss_legendre\nimport time:       228 |        406 |                             scipy.integrate._rules._gauss_kronrod\nimport time:       184 |       1305 |                           scipy.integrate._rules\nimport time:      1749 |       3053 |                         scipy.integrate._cubature\nimport time:       596 |        596 |                         scipy.integrate._lebedev\nimport time:       162 |        162 |                         scipy.integrate.dop\nimport time:       106 |        106 |                         scipy.integrate.lsoda\nimport time:       560 |        560 |                         scipy.integrate.vode\nimport time:       101 |        101 |                         scipy.integrate.odepack\nimport time:        85 |         85 |                         scipy.integrate.quadpack\nimport time:       118 |        118 |                         scipy.stats._finite_differences\nimport time:       143 |        143 |                         scipy.stats._constants\nimport time:       254 |        254 |                         scipy.stats._censored_data\nimport time:      1919 |      17469 |                       scipy.stats._distn_infrastructure\nimport time:       264 |        264 |                                 scipy.interpolate._fitpack\nimport time:       385 |        385 |                                 scipy.interpolate._dfitpack\nimport time:       516 |       1164 |                               scipy.interpolate._fitpack_impl\nimport time:       321 |        321 |                                 scipy.interpolate._dierckx\nimport time:       802 |       1123 |                               scipy.interpolate._bsplines\nimport time:       216 |       2502 |                             scipy.interpolate._fitpack_py\nimport time:       480 |        480 |                             scipy.interpolate._polyint\nimport time:       358 |        358 |                             scipy.interpolate._ppoly\nimport time:       484 |        484 |                             scipy.interpolate._interpnd\nimport time:       719 |       4541 |                           scipy.interpolate._interpolate\nimport time:       634 |        634 |                           scipy.interpolate._fitpack2\nimport time:       291 |        291 |                           scipy.interpolate._rbf\nimport time:       260 |        260 |                             scipy.interpolate._rbfinterp_pythran\nimport time:       306 |        566 |                           scipy.interpolate._rbfinterp\nimport time:       469 |        469 |                           scipy.interpolate._cubic\nimport time:       235 |        235 |                           scipy.interpolate._ndgriddata\nimport time:       307 |        307 |                           scipy.interpolate._fitpack_repro\nimport time:       140 |        140 |                           scipy.interpolate._pade\nimport time:       313 |        313 |                             scipy.interpolate._rgi_cython\nimport time:       647 |        647 |                             scipy.interpolate._ndbspline\nimport time:       386 |       1345 |                           scipy.interpolate._rgi\nimport time:       381 |        381 |                           scipy.interpolate._bary_rational\nimport time:       121 |        121 |                           scipy.interpolate.fitpack\nimport time:       122 |        122 |                           scipy.interpolate.fitpack2\nimport time:       107 |        107 |                           scipy.interpolate.interpolate\nimport time:        94 |         94 |                           scipy.interpolate.ndgriddata\nimport time:        83 |         83 |                           scipy.interpolate.polyint\nimport time:        82 |         82 |                           scipy.interpolate.rbf\nimport time:        88 |         88 |                           scipy.interpolate.interpnd\nimport time:       455 |      10054 |                         scipy.interpolate\nimport time:      1333 |       1333 |                           scipy.special.cython_special\nimport time:      3053 |       4385 |                         scipy.stats._stats\nimport time:       574 |        574 |                         scipy.stats._tukeylambda_stats\nimport time:       387 |        387 |                         scipy.stats._ksstats\nimport time:     59262 |      74660 |                       scipy.stats._continuous_distns\nimport time:       476 |        476 |                         scipy.stats._biasedurn\nimport time:       359 |        359 |                         scipy.stats._stats_pythran\nimport time:     11591 |      12425 |                       scipy.stats._discrete_distns\nimport time:       361 |        361 |                         scipy.stats._levy_stable.levyst\nimport time:      2089 |       2449 |                       scipy.stats._levy_stable\nimport time:       467 |        467 |                         scipy.stats._axis_nan_policy\nimport time:      1846 |       2313 |                       scipy.stats._entropy\nimport time:       543 |     109856 |                     scipy.stats.distributions\nimport time:       198 |        198 |                       scipy._lib._bunch\nimport time:      2190 |       2190 |                       scipy.stats._stats_mstats_common\nimport time:      2500 |       4887 |                     scipy.stats._mstats_basic\nimport time:       539 |        539 |                       scipy.stats._common\nimport time:      4456 |       4994 |                     scipy.stats._hypotests\nimport time:      6823 |       6823 |                     scipy.stats._resampling\nimport time:       288 |        288 |                     scipy.stats._binomtest\nimport time:     44135 |     170980 |                   scipy.stats._stats_py\nimport time:       676 |        676 |                   scipy.stats._variation\nimport time:       477 |        477 |                     scipy.stats._ansari_swilk_statistics\nimport time:       326 |        326 |                     scipy.stats._wilcoxon\nimport time:      2028 |       2028 |                     scipy.stats._fit\nimport time:      1004 |       1004 |                       scipy.stats._relative_risk\nimport time:       533 |        533 |                       scipy.stats._crosstab\nimport time:       243 |        243 |                       scipy.stats._odds_ratio\nimport time:       782 |       2560 |                     scipy.stats.contingency\nimport time:     10998 |      16387 |                   scipy.stats._morestats\nimport time:       376 |        376 |                         scipy.sparse.csgraph._laplacian\nimport time:       387 |        387 |                             scipy.sparse.csgraph._tools\nimport time:       164 |        550 |                           scipy.sparse.csgraph._validation\nimport time:       506 |       1056 |                         scipy.sparse.csgraph._shortest_path\nimport time:       342 |        342 |                         scipy.sparse.csgraph._traversal\nimport time:       451 |        451 |                         scipy.sparse.csgraph._min_spanning_tree\nimport time:       411 |        411 |                         scipy.sparse.csgraph._flow\nimport time:       315 |        315 |                         scipy.sparse.csgraph._matching\nimport time:       385 |        385 |                         scipy.sparse.csgraph._reordering\nimport time:       346 |       3679 |                       scipy.sparse.csgraph\nimport time:       370 |        370 |                       scipy.stats._sobol\nimport time:       464 |        464 |                       scipy.stats._qmc_cy\nimport time:      1519 |       6031 |                     scipy.stats._qmc\nimport time:      1596 |       7627 |                   scipy.stats._multicomp\nimport time:       552 |        552 |                   scipy.stats._binned_statistic\nimport time:       495 |        495 |                       scipy.stats._covariance\nimport time:       319 |        319 |                         scipy.stats._rcont.rcont\nimport time:       193 |        512 |                       scipy.stats._rcont\nimport time:       336 |        336 |                         scipy.stats._qmvnt_cy\nimport time:       236 |        572 |                       scipy.stats._qmvnt\nimport time:      3905 |       5482 |                     scipy.stats._multivariate\nimport time:       487 |       5969 |                   scipy.stats._kde\nimport time:       562 |        562 |                     scipy.stats._mstats_extras\nimport time:       268 |        830 |                   scipy.stats.mstats\nimport time:       129 |        129 |                   scipy.stats.qmc\nimport time:       807 |        807 |                   scipy.stats._page_trend_test\nimport time:      1525 |       1525 |                   scipy.stats._mannwhitneyu\nimport time:       206 |        206 |                   scipy.stats._bws_test\nimport time:      1966 |       1966 |                   scipy.stats._sensitivity_analysis\nimport time:      1841 |       1841 |                   scipy.stats._survival\nimport time:       265 |        265 |                     scipy.optimize._bracket\nimport time:       222 |        222 |                     scipy.optimize._chandrupatla\nimport time:       293 |        293 |                     scipy.stats._probability_distribution\nimport time:      3371 |       4150 |                   scipy.stats._distribution_infrastructure\nimport time:     41235 |      41235 |                   scipy.stats._new_distributions\nimport time:       140 |        140 |                             scipy.ndimage._ni_support\nimport time:       304 |        304 |                             scipy.ndimage._nd_image\nimport time:       215 |        215 |                             scipy.ndimage._ni_docstrings\nimport time:       255 |        255 |                             scipy.ndimage._rank_filter_1d\nimport time:      1798 |       2710 |                           scipy.ndimage._filters\nimport time:       223 |        223 |                           scipy.ndimage._fourier\nimport time:       760 |        760 |                           scipy.ndimage._interpolation\nimport time:       371 |        371 |                             scipy.ndimage._ni_label\nimport time:       416 |        416 |                             scipy.ndimage._morphology\nimport time:       426 |       1212 |                           scipy.ndimage._measurements\nimport time:       183 |       5085 |                         scipy.ndimage._ndimage_api\nimport time:       217 |        217 |                         scipy.ndimage._delegators\nimport time:       373 |       5674 |                       scipy.ndimage._support_alternative_backends\nimport time:       147 |        147 |                       scipy.ndimage.filters\nimport time:        97 |         97 |                       scipy.ndimage.fourier\nimport time:        87 |         87 |                       scipy.ndimage.interpolation\nimport time:        89 |         89 |                       scipy.ndimage.measurements\nimport time:        88 |         88 |                       scipy.ndimage.morphology\nimport time:       412 |       6592 |                     scipy.ndimage\nimport time:       720 |       7311 |                   scipy.stats._mgc\nimport time:       857 |        857 |                   scipy.stats._correlation\nimport time:       271 |        271 |                   scipy.stats._quantile\nimport time:       117 |        117 |                   scipy.stats.biasedurn\nimport time:        95 |         95 |                   scipy.stats.kde\nimport time:       107 |        107 |                   scipy.stats.morestats\nimport time:       109 |        109 |                   scipy.stats.mstats_basic\nimport time:        89 |         89 |                   scipy.stats.mstats_extras\nimport time:        83 |         83 |                   scipy.stats.mvn\nimport time:       105 |        105 |                   scipy.stats.stats\nimport time:       984 |     265303 |                 scipy.stats\nimport time:       219 |        219 |                     sklearn._config\nimport time:       283 |        283 |                       sklearn.__check_build._check_build\nimport time:       131 |        413 |                     sklearn.__check_build\nimport time:       153 |        153 |                     sklearn._distributor_init\nimport time:       320 |        320 |                       sklearn.exceptions\nimport time:       153 |        153 |                               sklearn.utils._bunch\nimport time:       796 |        948 |                             sklearn.utils._metadata_requests\nimport time:       334 |       1282 |                           sklearn.utils.metadata_routing\nimport time:       264 |        264 |                                   sklearn.externals\nimport time:       330 |        330 |                                       sklearn.externals.array_api_compat.common._helpers\nimport time:       157 |        487 |                                     sklearn.externals.array_api_compat.common\nimport time:       454 |        940 |                                   sklearn.externals.array_api_compat\nimport time:        85 |         85 |                                           sklearn.externals.array_api_extra._lib._utils\nimport time:        96 |         96 |                                             sklearn.externals._array_api_compat_vendor\nimport time:       198 |        294 |                                           sklearn.externals.array_api_extra._lib._utils._compat\nimport time:       429 |        807 |                                         sklearn.externals.array_api_extra._lib._backends\nimport time:       125 |        931 |                                       sklearn.externals.array_api_extra._lib\nimport time:        93 |         93 |                                             sklearn.externals.array_api_extra._lib._utils._typing\nimport time:       167 |        259 |                                           sklearn.externals.array_api_extra._lib._utils._helpers\nimport time:       484 |        743 |                                         sklearn.externals.array_api_extra._lib._at\nimport time:       308 |       1050 |                                       sklearn.externals.array_api_extra._lib._funcs\nimport time:       236 |       2216 |                                     sklearn.externals.array_api_extra._delegation\nimport time:       198 |        198 |                                     sklearn.externals.array_api_extra._lib._lazy\nimport time:       327 |       2740 |                                   sklearn.externals.array_api_extra\nimport time:       868 |        868 |                                       sklearn.externals.array_api_compat.common._aliases\nimport time:       172 |        172 |                                       sklearn.externals.array_api_compat._internal\nimport time:       155 |        155 |                                       sklearn.externals.array_api_compat.numpy._info\nimport time:      1334 |       2527 |                                     sklearn.externals.array_api_compat.numpy._aliases\nimport time:      1153 |       1153 |                                       sklearn.externals.array_api_compat.common._linalg\nimport time:       646 |       1798 |                                     sklearn.externals.array_api_compat.numpy.linalg\nimport time:       136 |        136 |                                       sklearn.externals.array_api_compat.common._fft\nimport time:       518 |        654 |                                     sklearn.externals.array_api_compat.numpy.fft\nimport time:       653 |       5631 |                                   sklearn.externals.array_api_compat.numpy\nimport time:       156 |        156 |                                       sklearn.externals._packaging\nimport time:       167 |        167 |                                       sklearn.externals._packaging._structures\nimport time:      1216 |       1538 |                                     sklearn.externals._packaging.version\nimport time:       288 |        288 |                                         ctypes.util\nimport time:       842 |       1129 |                                       threadpoolctl\nimport time:       285 |       1413 |                                     sklearn.utils.parallel\nimport time:       752 |       3702 |                                   sklearn.utils.fixes\nimport time:       544 |      13819 |                                 sklearn.utils._array_api\nimport time:       223 |        223 |                                 sklearn.utils.deprecation\nimport time:       787 |        787 |                                 sklearn.utils._isfinite\nimport time:      4912 |       4912 |                                 sklearn.utils._tags\nimport time:      6606 |      26344 |                               sklearn.utils.validation\nimport time:       737 |      27081 |                             sklearn.utils._param_validation\nimport time:       218 |      27299 |                           sklearn.utils._chunking\nimport time:       536 |        536 |                               sklearn.utils.sparsefuncs_fast\nimport time:       417 |        953 |                             sklearn.utils.extmath\nimport time:       358 |       1311 |                           sklearn.utils._indexing\nimport time:        89 |         89 |                             sklearn.utils._missing\nimport time:       181 |        269 |                           sklearn.utils._mask\nimport time:        91 |         91 |                             sklearn.utils._repr_html\nimport time:       245 |        335 |                           sklearn.utils._repr_html.base\nimport time:      1246 |       1246 |                               html.entities\nimport time:       418 |       1663 |                             html\nimport time:       492 |       2154 |                           sklearn.utils._repr_html.estimator\nimport time:       235 |        235 |                           sklearn.utils.class_weight\nimport time:       201 |        201 |                           sklearn.utils.discovery\nimport time:       414 |        414 |                           sklearn.utils.murmurhash\nimport time:       385 |      33882 |                         sklearn.utils\nimport time:        33 |      33914 |                       sklearn.utils._metadata_requests\nimport time:       247 |        247 |                       sklearn.utils._repr_html.params\nimport time:       139 |        139 |                         sklearn.utils._available_if\nimport time:       386 |        524 |                       sklearn.utils._set_output\nimport time:       719 |      35721 |                     sklearn.base\nimport time:       458 |        458 |                       sklearn.utils._openmp_helpers\nimport time:       237 |        694 |                     sklearn.utils._show_versions\nimport time:       166 |        166 |                     sklearn._built_with_meson\nimport time:       329 |      37693 |                   sklearn\nimport time:       687 |        687 |                           sklearn.metrics.cluster._bicluster\nimport time:       112 |        112 |                               sklearn.utils._unique\nimport time:       235 |        346 |                             sklearn.utils.multiclass\nimport time:       418 |        418 |                             sklearn.metrics.cluster._expected_mutual_info_fast\nimport time:       403 |       1166 |                           sklearn.metrics.cluster._supervised\nimport time:       380 |        380 |                                 sklearn.utils.sparsefuncs\nimport time:       601 |        601 |                                   sklearn.utils._encode\nimport time:       976 |       1576 |                                 sklearn.preprocessing._encoders\nimport time:      4462 |       6418 |                               sklearn.preprocessing._data\nimport time:       254 |        254 |                                 sklearn.utils.stats\nimport time:      1066 |       1320 |                               sklearn.preprocessing._discretization\nimport time:       427 |        427 |                                 sklearn.utils.metaestimators\nimport time:       756 |       1182 |                               sklearn.preprocessing._function_transformer\nimport time:      1499 |       1499 |                               sklearn.preprocessing._label\nimport time:       503 |        503 |                                 sklearn.preprocessing._csr_polynomial_expansion\nimport time:      1201 |       1704 |                               sklearn.preprocessing._polynomial\nimport time:       539 |        539 |                                 sklearn.preprocessing._target_encoder_fast\nimport time:       729 |       1267 |                               sklearn.preprocessing._target_encoder\nimport time:       326 |      13713 |                             sklearn.preprocessing\nimport time:       763 |        763 |                                   sklearn.metrics._dist_metrics\nimport time:       651 |        651 |                                       sklearn.metrics._pairwise_distances_reduction._datasets_pair\nimport time:       878 |        878 |                                       sklearn.utils._cython_blas\nimport time:       483 |       2011 |                                     sklearn.metrics._pairwise_distances_reduction._base\nimport time:       518 |        518 |                                     sklearn.metrics._pairwise_distances_reduction._middle_term_computer\nimport time:       273 |        273 |                                     sklearn.utils._heap\nimport time:       177 |        177 |                                     sklearn.utils._sorting\nimport time:       730 |       3707 |                                   sklearn.metrics._pairwise_distances_reduction._argkmin\nimport time:       447 |        447 |                                   sklearn.metrics._pairwise_distances_reduction._argkmin_classmode\nimport time:       294 |        294 |                                     sklearn.utils._vector_sentinel\nimport time:       601 |        895 |                                   sklearn.metrics._pairwise_distances_reduction._radius_neighbors\nimport time:       424 |        424 |                                   sklearn.metrics._pairwise_distances_reduction._radius_neighbors_classmode\nimport time:       579 |       6813 |                                 sklearn.metrics._pairwise_distances_reduction._dispatcher\nimport time:       176 |       6989 |                               sklearn.metrics._pairwise_distances_reduction\nimport time:       431 |        431 |                               sklearn.metrics._pairwise_fast\nimport time:       937 |       8357 |                             sklearn.metrics.pairwise\nimport time:       331 |      22400 |                           sklearn.metrics.cluster._unsupervised\nimport time:       182 |      24434 |                         sklearn.metrics.cluster\nimport time:       999 |        999 |                         sklearn.metrics._classification\nimport time:       136 |        136 |                           sklearn.metrics._plot\nimport time:       102 |        102 |                           sklearn.utils._optional_dependencies\nimport time:       226 |        226 |                             sklearn.utils._response\nimport time:       305 |        531 |                           sklearn.utils._plotting\nimport time:       290 |       1058 |                         sklearn.metrics._plot.confusion_matrix\nimport time:       131 |        131 |                             sklearn.metrics._base\nimport time:       406 |        537 |                           sklearn.metrics._ranking\nimport time:       198 |        734 |                         sklearn.metrics._plot.det_curve\nimport time:       184 |        184 |                         sklearn.metrics._plot.precision_recall_curve\nimport time:       154 |        154 |                         sklearn.metrics._plot.regression\nimport time:       202 |        202 |                         sklearn.metrics._plot.roc_curve\nimport time:       440 |        440 |                         sklearn.metrics._regression\nimport time:      3251 |       3251 |                         sklearn.metrics._scorer\nimport time:       298 |      31749 |                       sklearn.metrics\nimport time:        24 |      31773 |                     sklearn.metrics.pairwise\nimport time:      1172 |      32944 |                   sklearn.gaussian_process.kernels\nimport time:      1131 |       1131 |                     sklearn.multiclass\nimport time:       321 |        321 |                     sklearn.utils.optimize\nimport time:       970 |       2421 |                   sklearn.gaussian_process._gpc\nimport time:       415 |        415 |                   sklearn.gaussian_process._gpr\nimport time:       269 |      73742 |                 sklearn.gaussian_process\nimport time:       261 |        261 |                 bayes_opt.exception\nimport time:       305 |        305 |                         colorama.ansi\nimport time:        75 |         75 |                           msvcrt\nimport time:       148 |        148 |                           colorama.win32\nimport time:       251 |        473 |                         colorama.winterm\nimport time:       445 |       1223 |                       colorama.ansitowin32\nimport time:       185 |       1407 |                     colorama.initialise\nimport time:       155 |       1562 |                   colorama\nimport time:        99 |         99 |                       bayes_opt.util\nimport time:       350 |        449 |                     bayes_opt.parameter\nimport time:       260 |        708 |                   bayes_opt.constraint\nimport time:       326 |       2596 |                 bayes_opt.target_space\nimport time:       529 |     342428 |               bayes_opt.acquisition\nimport time:       247 |        247 |                 bayes_opt.domain_reduction\nimport time:       204 |        204 |                 bayes_opt.logger\nimport time:       376 |        826 |               bayes_opt.bayesian_optimization\nimport time:      1009 |     344262 |             bayes_opt\nimport time:       408 |     456837 |           optimagic.config\nimport time:       200 |        200 |           optimagic.decorators\nimport time:       492 |     604111 |         optimagic.batch_evaluators\nimport time:       169 |        169 |           optimagic.differentiation\nimport time:       323 |        323 |           optimagic.differentiation.finite_differences\nimport time:       328 |        328 |           optimagic.differentiation.generate_steps\nimport time:       202 |        202 |           optimagic.differentiation.richardson_extrapolation\nimport time:       519 |        519 |           optimagic.parameters.block_trees\nimport time:      1716 |       3256 |         optimagic.differentiation.derivatives\nimport time:      1412 |       1412 |         optimagic.differentiation.numdiff_options\nimport time:       165 |        165 |           optimagic.parameters.process_selectors\nimport time:       848 |        848 |             optimagic.parameters.scaling\nimport time:       167 |        167 |               optimagic.parameters.kernel_transformations\nimport time:       137 |        137 |                 optimagic.parameters.check_constraints\nimport time:       276 |        276 |                 optimagic.parameters.consolidate_constraints\nimport time:       168 |        579 |               optimagic.parameters.process_constraints\nimport time:      2078 |       2824 |             optimagic.parameters.space_conversion\nimport time:       877 |       4547 |           optimagic.parameters.scale_conversion\nimport time:       619 |        619 |           optimagic.parameters.tree_conversion\nimport time:       958 |       6288 |         optimagic.parameters.conversion\nimport time:      1646 |     616711 |       optimagic.optimization.internal_optimization_problem\nimport time:       194 |        194 |       optimagic.type_conversion\nimport time:      3183 |     994165 |     optimagic.optimization.algorithm\nimport time:       284 |     994449 |   optimagic.mark\nimport time:       124 |        124 |       optimagic.optimizers\nimport time:      2188 |       2311 |     optimagic.optimizers.bayesian_optimizer\nimport time:      1069 |       1069 |     optimagic.optimizers.bhhh\nimport time:      1829 |       1829 |     optimagic.optimizers.fides\nimport time:      1010 |       1010 |     optimagic.optimizers.iminuit_migrad\nimport time:       253 |        253 |         optimagic.parameters.nonlinear_constraints\nimport time:     19325 |      19578 |       optimagic.optimizers.scipy_optimizers\nimport time:     11358 |      30936 |     optimagic.optimizers.ipopt\nimport time:      4363 |       4363 |     optimagic.optimizers.nag_optimizers\nimport time:      1256 |       1256 |     optimagic.optimizers.neldermead\nimport time:      1433 |       1433 |     optimagic.optimizers.nevergrad_optimizers\nimport time:     13975 |      13975 |     optimagic.optimizers.nlopt_optimizers\nimport time:       121 |        121 |         optimagic.optimizers._pounders\nimport time:       100 |        100 |           optimagic.optimizers._pounders._conjugate_gradient\nimport time:       110 |        110 |           optimagic.optimizers._pounders._steihaug_toint\nimport time:       219 |        219 |           optimagic.optimizers._pounders._trsbox\nimport time:       492 |        920 |         optimagic.optimizers._pounders.bntr\nimport time:       629 |        629 |         optimagic.optimizers._pounders.gqtpar\nimport time:       634 |       2302 |       optimagic.optimizers._pounders.pounders_auxiliary\nimport time:       257 |        257 |       optimagic.optimizers._pounders.pounders_history\nimport time:      2249 |       4807 |     optimagic.optimizers.pounders\nimport time:        88 |         88 |       pygmo\nimport time:     20508 |      20596 |     optimagic.optimizers.pygmo_optimizers\nimport time:        85 |         85 |       petsc4py\nimport time:      1119 |       1204 |     optimagic.optimizers.tao_optimizers\nimport time:      6025 |       6025 |     optimagic.optimizers.tranquilo\nimport time:    181644 |     272452 |   optimagic.algorithms\nimport time:       247 |        247 |     optimagic.benchmarking\nimport time:       146 |        146 |     optimagic.benchmarking.process_benchmark_results\nimport time:       128 |        128 |       optimagic.visualization\nimport time:       231 |        359 |     optimagic.visualization.profile_plot\nimport time:       305 |       1055 |   optimagic.benchmarking.benchmark_reports\nimport time:      3207 |       3207 |       optimagic.benchmarking.more_wild\nimport time:      1742 |       4949 |     optimagic.benchmarking.cartis_roberts\nimport time:       164 |        164 |     optimagic.benchmarking.noise_distributions\nimport time:       108 |        108 |       optimagic.shared\nimport time:       239 |        346 |     optimagic.shared.process_user_function\nimport time:       296 |       5753 |   optimagic.benchmarking.get_benchmark_problems\nimport time:      3002 |       3002 |         optimagic.optimization.multistart_options\nimport time:       174 |        174 |         optimagic.optimization.scipy_aliases\nimport time:      1847 |       5022 |       optimagic.optimization.create_optimization_problem\nimport time:       359 |        359 |       optimagic.optimization.error_penalty\nimport time:       203 |        203 |         optimagic.optimization.optimization_logging\nimport time:       310 |        512 |       optimagic.optimization.multistart\nimport time:        98 |         98 |         optimagic.shared.compat\nimport time:      1959 |       2056 |       optimagic.optimization.optimize_result\nimport time:       157 |        157 |         optimagic.optimization.convergence_report\nimport time:       918 |       1075 |       optimagic.optimization.process_results\nimport time:       448 |       9469 |     optimagic.optimization.optimize\nimport time:       165 |       9633 |   optimagic.benchmarking.run_benchmark\nimport time:       557 |        557 |   optimagic.logging.read_log\nimport time:       192 |        192 |   optimagic.parameters.constraint_tools\nimport time:       235 |        235 |     plotly.graph_objects\nimport time:       170 |        170 |       plotly.subplots\nimport time:       169 |        339 |     optimagic.visualization.plotting_utilities\nimport time:       180 |        752 |   optimagic.visualization.convergence_plot\nimport time:       236 |        236 |   optimagic.visualization.history_plots\nimport time:       169 |        169 |   optimagic.visualization.slice_plot\nimport time:        97 |         97 |   optimagic._version\nimport time:      2315 |    1665977 | optimagic\n"}}, "modified": {"path": "/home/<USER>/learnings/gsoc/pygad_new/new_code/optimagic", "functionality": "passed", "timing": {"times": [1.780118, 1.783846, 1.80503, 1.752426, 1.759363], "mean": 1.7761566000000002, "min": 1.752426, "max": 1.80503, "std": 0.018727835791676486, "sample_output": "import time: self [us] | cumulative | imported package\nimport time:       297 |        297 |   _io\nimport time:        55 |         55 |   marshal\nimport time:       442 |        442 |   posix\nimport time:       961 |       1754 | _frozen_importlib_external\nimport time:       105 |        105 |   time\nimport time:       268 |        372 | zipimport\nimport time:        62 |         62 |     _codecs\nimport time:       757 |        818 |   codecs\nimport time:       495 |        495 |   encodings.aliases\nimport time:       668 |       1980 | encodings\nimport time:       215 |        215 | encodings.utf_8\nimport time:       104 |        104 | _signal\nimport time:        42 |         42 |     _abc\nimport time:       220 |        262 |   abc\nimport time:       188 |        450 | io\nimport time:        72 |         72 |       _stat\nimport time:       177 |        249 |     stat\nimport time:      1225 |       1225 |     _collections_abc\nimport time:        71 |         71 |       errno\nimport time:       110 |        110 |       genericpath\nimport time:       166 |        346 |     posixpath\nimport time:       655 |       2473 |   os\nimport time:       120 |        120 |   _sitebuiltins\nimport time:       560 |        560 |   encodings.utf_8_sig\nimport time:      3935 |       3935 |   _distutils_hack\nimport time:       153 |        153 |   sitecustomize\nimport time:       970 |       8208 | site\nimport time:       146 |        146 | linecache\nimport time:       157 |        157 |   __future__\nimport time:       163 |        163 |               itertools\nimport time:       181 |        181 |               keyword\nimport time:       109 |        109 |                 _operator\nimport time:       544 |        652 |               operator\nimport time:       254 |        254 |               reprlib\nimport time:        82 |         82 |               _collections\nimport time:       936 |       2266 |             collections\nimport time:        65 |         65 |             _functools\nimport time:       965 |       3295 |           functools\nimport time:       337 |        337 |           types\nimport time:      1456 |       5087 |         enum\nimport time:        94 |         94 |           _sre\nimport time:       305 |        305 |             re._constants\nimport time:       498 |        802 |           re._parser\nimport time:       115 |        115 |           re._casefix\nimport time:       382 |       1391 |         re._compiler\nimport time:       216 |        216 |         copyreg\nimport time:       530 |       7223 |       re\nimport time:       288 |        288 |           _weakrefset\nimport time:       724 |       1011 |         weakref\nimport time:       191 |       1202 |       copy\nimport time:      1348 |       1348 |           _ast\nimport time:       732 |        732 |           contextlib\nimport time:      1318 |       3397 |         ast\nimport time:       175 |        175 |             _opcode\nimport time:       199 |        199 |             _opcode_metadata\nimport time:      7754 |       8126 |           opcode\nimport time:      1041 |       9166 |         dis\nimport time:       236 |        236 |           importlib\nimport time:        93 |        329 |         importlib.machinery\nimport time:       170 |        170 |           token\nimport time:        47 |         47 |           _tokenize\nimport time:      1039 |       1255 |         tokenize\nimport time:      2687 |      16832 |       inspect\nimport time:       695 |      25950 |     dataclasses\nimport time:        68 |         68 |       _typing\nimport time:      2837 |       2904 |     typing\nimport time:       414 |        414 |       warnings\nimport time:       194 |        194 |       numpy.version\nimport time:       126 |        126 |       numpy._expired_attrs_2_0\nimport time:       113 |        113 |           numpy._utils._convertions\nimport time:       167 |        279 |         numpy._utils\nimport time:       616 |        895 |       numpy._globals\nimport time:        36 |         36 |         numpy._distributor_init_local\nimport time:       140 |        175 |       numpy._distributor_init\nimport time:       342 |        342 |                   _datetime\nimport time:       225 |        567 |                 datetime\nimport time:       244 |        244 |                 math\nimport time:       305 |        305 |                 numpy.exceptions\nimport time:       392 |        392 |                 numpy._core._exceptions\nimport time:       209 |        209 |                     _contextvars\nimport time:       175 |        383 |                   contextvars\nimport time:       171 |        553 |                 numpy._core.printoptions\nimport time:      4892 |       4892 |                 numpy.dtypes\nimport time:     14212 |      21161 |               numpy._core._multiarray_umath\nimport time:       172 |        172 |                 numpy._utils._inspect\nimport time:      2792 |       2964 |               numpy._core.overrides\nimport time:      1032 |      25155 |             numpy._core.multiarray\nimport time:       245 |        245 |             numpy._core.umath\nimport time:      4457 |       4457 |               numbers\nimport time:       246 |        246 |               numpy._core._dtype\nimport time:       127 |        127 |               numpy._core._string_helpers\nimport time:       374 |        374 |               numpy._core._type_aliases\nimport time:      1113 |       6315 |             numpy._core.numerictypes\nimport time:       357 |        357 |               numpy._core._ufunc_config\nimport time:      1292 |       1292 |                       _struct\nimport time:       345 |       1637 |                     struct\nimport time:       648 |        648 |                     _compat_pickle\nimport time:       627 |        627 |                     _pickle\nimport time:      1982 |       4892 |                   pickle\nimport time:       567 |       5459 |                 numpy._core._methods\nimport time:      6662 |      12120 |               numpy._core.fromnumeric\nimport time:       456 |      12932 |             numpy._core._machar\nimport time:      7662 |       7662 |                 numpy._core.shape_base\nimport time:       493 |        493 |                 numpy._core._asarray\nimport time:       768 |        768 |                 numpy._core.arrayprint\nimport time:      2468 |      11389 |               numpy._core.numeric\nimport time:       942 |      12331 |             numpy._core.einsumfunc\nimport time:       708 |        708 |             numpy._core.function_base\nimport time:       649 |        649 |             numpy._core.getlimits\nimport time:       329 |        329 |             numpy._core.memmap\nimport time:       652 |        652 |             numpy._core.records\nimport time:      2414 |       2414 |             numpy._core._add_newdocs\nimport time:       764 |        764 |             numpy._core._add_newdocs_scalars\nimport time:       250 |        250 |             numpy._core._dtype_ctypes\nimport time:       878 |        878 |                 _ctypes\nimport time:       531 |        531 |                 ctypes._endian\nimport time:      1718 |       3126 |               ctypes\nimport time:      2046 |       5172 |             numpy._core._internal\nimport time:       242 |        242 |             numpy._pytesttester\nimport time:      3207 |      71359 |           numpy._core\nimport time:        51 |      71409 |         numpy._core._multiarray_umath\nimport time:       458 |      71866 |       numpy.__config__\nimport time:       609 |        609 |                         numpy._typing._nbit_base\nimport time:       312 |        312 |                         numpy._typing._nested_sequence\nimport time:       190 |        190 |                         numpy._typing._shape\nimport time:      2754 |       3864 |                       numpy._typing._array_like\nimport time:      3712 |       3712 |                       numpy._typing._char_codes\nimport time:      3233 |       3233 |                       numpy._typing._dtype_like\nimport time:       204 |        204 |                       numpy._typing._nbit\nimport time:       132 |        132 |                       numpy._typing._scalars\nimport time:        84 |         84 |                       numpy._typing._ufunc\nimport time:       481 |      11708 |                     numpy._typing\nimport time:       488 |        488 |                       numpy.lib._stride_tricks_impl\nimport time:       792 |       1279 |                     numpy.lib._twodim_base_impl\nimport time:       213 |        213 |                       numpy.lib._array_utils_impl\nimport time:       240 |        453 |                     numpy.lib.array_utils\nimport time:       676 |        676 |                     numpy.linalg._umath_linalg\nimport time:      3802 |      17916 |                   numpy.linalg._linalg\nimport time:       144 |        144 |                   numpy.linalg.linalg\nimport time:       289 |      18348 |                 numpy.linalg\nimport time:       764 |      19112 |               numpy.matrixlib.defmatrix\nimport time:      4206 |      23317 |             numpy.matrixlib\nimport time:       331 |        331 |               numpy.lib._histograms_impl\nimport time:      1195 |       1525 |             numpy.lib._function_base_impl\nimport time:       128 |        128 |             numpy.lib.stride_tricks\nimport time:       536 |      25505 |           numpy.lib._index_tricks_impl\nimport time:       395 |      25899 |         numpy.lib._arraypad_impl\nimport time:       868 |        868 |         numpy.lib._arraysetops_impl\nimport time:       234 |        234 |         numpy.lib._arrayterator_impl\nimport time:       445 |        445 |         numpy.lib._nanfunctions_impl\nimport time:       160 |        160 |                   _wmi\nimport time:       837 |        997 |                 platform\nimport time:      1480 |       1480 |                 textwrap\nimport time:       344 |       2820 |               numpy.lib._utils_impl\nimport time:       295 |       3115 |             numpy.lib._format_impl\nimport time:       113 |       3227 |           numpy.lib.format\nimport time:       337 |        337 |           numpy.lib._datasource\nimport time:       522 |        522 |           numpy.lib._iotools\nimport time:       730 |       4816 |         numpy.lib._npyio_impl\nimport time:       133 |        133 |             numpy.lib._ufunclike_impl\nimport time:       384 |        517 |           numpy.lib._type_check_impl\nimport time:       609 |       1126 |         numpy.lib._polynomial_impl\nimport time:       378 |        378 |         numpy.lib._shape_base_impl\nimport time:       196 |        196 |         numpy.lib._version\nimport time:        92 |         92 |         numpy.lib.introspect\nimport time:       175 |        175 |         numpy.lib.mixins\nimport time:        82 |         82 |         numpy.lib.npyio\nimport time:       263 |        263 |           numpy.lib._scimath_impl\nimport time:       111 |        373 |         numpy.lib.scimath\nimport time:       574 |      35252 |       numpy.lib\nimport time:       163 |        163 |       numpy._array_api_info\nimport time:      2516 |     111598 |     numpy\nimport time:       263 |        263 |         pytz.exceptions\nimport time:       988 |        988 |           threading\nimport time:       363 |       1351 |         pytz.lazy\nimport time:       184 |        184 |             _bisect\nimport time:       136 |        320 |           bisect\nimport time:       289 |        608 |         pytz.tzinfo\nimport time:       151 |        151 |         pytz.tzfile\nimport time:       680 |       3050 |       pytz\nimport time:       141 |        141 |         dateutil._version\nimport time:       176 |        317 |       dateutil\nimport time:       439 |        439 |           sysconfig\nimport time:       646 |        646 |           _sysconfigdata__linux_x86_64-linux-gnu\nimport time:       917 |       2000 |         pandas.compat._constants\nimport time:       213 |        213 |             _compression\nimport time:       261 |        261 |             _bz2\nimport time:       253 |        726 |           bz2\nimport time:       350 |        350 |             _lzma\nimport time:       294 |        644 |           lzma\nimport time:       285 |       1654 |         pandas.compat.compressors\nimport time:       111 |        111 |             pandas.util\nimport time:      2592 |       2702 |           pandas.util.version\nimport time:       430 |       3132 |         pandas.compat.numpy\nimport time:        87 |         87 |           pyarrow\nimport time:       217 |        303 |         pandas.compat.pyarrow\nimport time:       304 |       7391 |       pandas.compat\nimport time:       524 |        524 |                   numpy.random._common\nimport time:       224 |        224 |                       binascii\nimport time:       273 |        496 |                     base64\nimport time:      2058 |       2058 |                       _hashlib\nimport time:       201 |        201 |                         _blake2\nimport time:       331 |        532 |                       hashlib\nimport time:       247 |       2836 |                     hmac\nimport time:       193 |        193 |                       _random\nimport time:       568 |        760 |                     random\nimport time:       242 |       4333 |                   secrets\nimport time:       544 |       5400 |                 numpy.random.bit_generator\nimport time:       307 |       5706 |               numpy.random._bounded_integers\nimport time:       275 |        275 |                   numpy.random._pcg64\nimport time:       290 |        290 |                   numpy.random._mt19937\nimport time:       845 |       1409 |                 numpy.random._generator\nimport time:       245 |        245 |                 numpy.random._philox\nimport time:       186 |        186 |                 numpy.random._sfc64\nimport time:       906 |        906 |                 numpy.random.mtrand\nimport time:       233 |       2976 |               numpy.random._pickle\nimport time:       267 |       8948 |             numpy.random\nimport time:      3784 |      12732 |           pandas._typing\nimport time:       251 |        251 |           pandas.util._exceptions\nimport time:      1020 |      14002 |         pandas._config.config\nimport time:       332 |        332 |         pandas._config.dates\nimport time:       178 |        178 |             _locale\nimport time:       936 |       1114 |           locale\nimport time:       288 |       1401 |         pandas._config.display\nimport time:       238 |      15971 |       pandas._config\nimport time:       135 |        135 |         pandas.core\nimport time:      1036 |       1170 |       pandas.core.config_init\nimport time:       291 |        291 |           pandas._libs.pandas_parser\nimport time:       146 |        146 |           pandas._libs.pandas_datetime\nimport time:       231 |        231 |                       pandas._libs.tslibs.ccalendar\nimport time:       377 |        377 |                       pandas._libs.tslibs.np_datetime\nimport time:      1193 |       1801 |                     pandas._libs.tslibs.dtypes\nimport time:       236 |        236 |                       pandas._libs.tslibs.base\nimport time:       556 |        556 |                           pandas._libs.tslibs.nattype\nimport time:       217 |        217 |                               pandas.compat._optional\nimport time:       294 |        294 |                                 zoneinfo._tzpath\nimport time:       230 |        230 |                                 zoneinfo._common\nimport time:       219 |        219 |                                 _zoneinfo\nimport time:       264 |       1005 |                               zoneinfo\nimport time:       438 |        438 |                                       importlib._abc\nimport time:       235 |        673 |                                     importlib.util\nimport time:      1014 |       1686 |                                   six\nimport time:        60 |         60 |                                   six.moves\nimport time:       256 |        256 |                                   dateutil.tz._common\nimport time:       242 |        242 |                                   dateutil.tz._factories\nimport time:        34 |         34 |                                     six.moves.winreg\nimport time:       227 |        260 |                                   dateutil.tz.win\nimport time:       933 |       3434 |                                 dateutil.tz.tz\nimport time:       201 |       3635 |                               dateutil.tz\nimport time:       679 |       5534 |                             pandas._libs.tslibs.timezones\nimport time:      1364 |       1364 |                                 calendar\nimport time:      1296 |       2660 |                               _strptime\nimport time:       732 |        732 |                                   signal\nimport time:       246 |        246 |                                   fcntl\nimport time:        84 |         84 |                                   msvcrt\nimport time:       203 |        203 |                                   _posixsubprocess\nimport time:       247 |        247 |                                   select\nimport time:       640 |        640 |                                   selectors\nimport time:      1020 |       3170 |                                 subprocess\nimport time:       241 |       3411 |                               pandas._config.localization\nimport time:       528 |       6598 |                             pandas._libs.tslibs.fields\nimport time:       939 |      13070 |                           pandas._libs.tslibs.timedeltas\nimport time:       626 |        626 |                           pandas._libs.tslibs.tzconversion\nimport time:       879 |      15129 |                         pandas._libs.tslibs.timestamps\nimport time:       306 |        306 |                         pandas._libs.properties\nimport time:      1586 |      17021 |                       pandas._libs.tslibs.offsets\nimport time:       975 |        975 |                           _decimal\nimport time:       215 |       1190 |                         decimal\nimport time:        58 |         58 |                               _string\nimport time:       727 |        784 |                             string\nimport time:       160 |        160 |                             dateutil._common\nimport time:      1545 |       2488 |                           dateutil.parser._parser\nimport time:       454 |        454 |                           dateutil.parser.isoparser\nimport time:       328 |       3270 |                         dateutil.parser\nimport time:      1006 |       1006 |                         pandas._libs.tslibs.strptime\nimport time:       796 |       6260 |                       pandas._libs.tslibs.parsing\nimport time:       678 |      24193 |                     pandas._libs.tslibs.conversion\nimport time:       783 |        783 |                     pandas._libs.tslibs.period\nimport time:       473 |        473 |                     pandas._libs.tslibs.vectorized\nimport time:       310 |      27558 |                   pandas._libs.tslibs\nimport time:        34 |      27591 |                 pandas._libs.tslibs.nattype\nimport time:       238 |        238 |                 pandas._libs.ops_dispatch\nimport time:       718 |      28545 |               pandas._libs.missing\nimport time:      1315 |      29860 |             pandas._libs.hashtable\nimport time:      4297 |       4297 |             pandas._libs.algos\nimport time:       859 |      35015 |           pandas._libs.interval\nimport time:       152 |      35603 |         pandas._libs\nimport time:       206 |        206 |           pandas.core.dtypes\nimport time:       107 |        107 |             pyarrow\nimport time:      1321 |       1428 |           pandas._libs.lib\nimport time:       666 |        666 |           pandas.errors\nimport time:       724 |        724 |             pandas.core.dtypes.generic\nimport time:       406 |       1130 |           pandas.core.dtypes.base\nimport time:       222 |        222 |           pandas.core.dtypes.inference\nimport time:      1676 |       5326 |         pandas.core.dtypes.dtypes\nimport time:       360 |        360 |           pandas.core.dtypes.common\nimport time:       504 |        863 |         pandas.core.dtypes.missing\nimport time:       450 |        450 |           pandas.util._decorators\nimport time:       170 |        170 |               pandas.io\nimport time:       281 |        450 |             pandas.io._util\nimport time:       684 |       1133 |           pandas.core.dtypes.cast\nimport time:       191 |        191 |             pandas.core.dtypes.astype\nimport time:       201 |        392 |           pandas.core.dtypes.concat\nimport time:       141 |        141 |             pandas.core.array_algos\nimport time:      4575 |       4575 |                 numpy.ma.core\nimport time:      1195 |       1195 |                 numpy.ma.extras\nimport time:       226 |       5996 |               numpy.ma\nimport time:       323 |        323 |               pandas.core.common\nimport time:       297 |       6615 |             pandas.core.construction\nimport time:       347 |       7101 |           pandas.core.array_algos.take\nimport time:       275 |        275 |             pandas.core.indexers.utils\nimport time:       154 |        429 |           pandas.core.indexers\nimport time:       684 |      10187 |         pandas.core.algorithms\nimport time:       321 |        321 |             pandas.core.arrays.arrow.accessors\nimport time:       274 |        274 |               unicodedata\nimport time:       275 |        275 |               pandas.util._validators\nimport time:       393 |        393 |               pandas.core.missing\nimport time:       480 |        480 |                   pandas._libs.ops\nimport time:       136 |        136 |                   pandas.core.roperator\nimport time:        97 |         97 |                   pandas.core.computation\nimport time:       203 |        203 |                     pandas.core.computation.check\nimport time:       348 |        551 |                   pandas.core.computation.expressions\nimport time:       129 |        129 |                   pandas.core.ops.missing\nimport time:        94 |         94 |                   pandas.core.ops.dispatch\nimport time:        95 |         95 |                   pandas.core.ops.invalid\nimport time:       366 |       1945 |                 pandas.core.ops.array_ops\nimport time:       112 |        112 |                 pandas.core.ops.common\nimport time:       188 |        188 |                 pandas.core.ops.docstrings\nimport time:       128 |        128 |                 pandas.core.ops.mask_ops\nimport time:       179 |       2549 |               pandas.core.ops\nimport time:       358 |        358 |               pandas.core.arraylike\nimport time:       277 |        277 |               pandas.core.arrays._arrow_string_mixins\nimport time:       164 |        164 |               pandas.core.arrays._utils\nimport time:       427 |        427 |                 pandas.compat.numpy.function\nimport time:       210 |        210 |                 pandas.core.array_algos.quantile\nimport time:       304 |        304 |                 pandas.core.sorting\nimport time:      1211 |       2150 |               pandas.core.arrays.base\nimport time:      1649 |       1649 |                 pandas.core.nanops\nimport time:       244 |        244 |                 pandas.core.array_algos.masked_accumulations\nimport time:       240 |        240 |                 pandas.core.array_algos.masked_reductions\nimport time:       150 |        150 |                   pandas.core.util\nimport time:       661 |        661 |                   pandas._libs.hashing\nimport time:       378 |       1189 |                 pandas.core.util.hashing\nimport time:      1698 |       5017 |               pandas.core.arrays.masked\nimport time:       678 |        678 |                 pandas._libs.arrays\nimport time:       435 |        435 |                   pandas.core.arrays.numeric\nimport time:       428 |        863 |                 pandas.core.arrays.floating\nimport time:       519 |        519 |                 pandas.core.arrays.integer\nimport time:       224 |        224 |                     pandas.core.array_algos.transforms\nimport time:      1182 |       1406 |                   pandas.core.arrays._mixins\nimport time:       262 |        262 |                     pandas.core.strings\nimport time:       426 |        426 |                     pandas.core.strings.base\nimport time:       869 |       1556 |                   pandas.core.strings.object_array\nimport time:       746 |       3707 |                 pandas.core.arrays.numpy_\nimport time:       257 |        257 |                 pandas.io.formats\nimport time:       240 |        240 |                       fnmatch\nimport time:       506 |        506 |                       zlib\nimport time:      1231 |       1976 |                     shutil\nimport time:       194 |       2170 |                   pandas.io.formats.console\nimport time:       730 |       2899 |                 pandas.io.formats.printing\nimport time:      1205 |      10125 |               pandas.core.arrays.string_\nimport time:       228 |        228 |                 pandas.tseries\nimport time:       687 |        914 |               pandas.tseries.frequencies\nimport time:      2008 |      24499 |             pandas.core.arrays.arrow.array\nimport time:       226 |      25045 |           pandas.core.arrays.arrow\nimport time:       449 |        449 |           pandas.core.arrays.boolean\nimport time:       377 |        377 |               _csv\nimport time:       821 |       1197 |             csv\nimport time:       568 |        568 |             pandas.core.accessor\nimport time:       975 |        975 |             pandas.core.base\nimport time:      2008 |       4748 |           pandas.core.arrays.categorical\nimport time:       812 |        812 |             pandas._libs.tslib\nimport time:       210 |        210 |               pandas.core.array_algos.datetimelike_accumulations\nimport time:      2176 |       2386 |             pandas.core.arrays.datetimelike\nimport time:       682 |        682 |             pandas.core.arrays._ranges\nimport time:       321 |        321 |             pandas.tseries.offsets\nimport time:      1283 |       5482 |           pandas.core.arrays.datetimes\nimport time:      1023 |       1023 |             pandas.core.arrays.timedeltas\nimport time:      2047 |       3069 |           pandas.core.arrays.interval\nimport time:       996 |        996 |           pandas.core.arrays.period\nimport time:       755 |        755 |                 pandas._libs.sparse\nimport time:      1327 |       2082 |               pandas.core.arrays.sparse.array\nimport time:       586 |       2667 |             pandas.core.arrays.sparse.accessor\nimport time:       219 |       2885 |           pandas.core.arrays.sparse\nimport time:       792 |        792 |           pandas.core.arrays.string_arrow\nimport time:       308 |      43771 |         pandas.core.arrays\nimport time:       273 |        273 |         pandas.core.flags\nimport time:       665 |        665 |               pandas._libs.internals\nimport time:       187 |        187 |                 pandas.core._numba\nimport time:       425 |        612 |               pandas.core._numba.executor\nimport time:      1339 |       2615 |             pandas.core.apply\nimport time:       138 |        138 |                 gc\nimport time:       643 |        643 |                       _json\nimport time:       605 |       1248 |                     json.scanner\nimport time:       759 |       2006 |                   json.decoder\nimport time:       738 |        738 |                   json.encoder\nimport time:       451 |       3193 |                 json\nimport time:       420 |        420 |                   pandas._libs.indexing\nimport time:       225 |        225 |                     pandas.core.indexes\nimport time:      1464 |       1464 |                       pandas._libs.index\nimport time:       644 |        644 |                       pandas._libs.writers\nimport time:       884 |        884 |                       pandas._libs.join\nimport time:       249 |        249 |                       pandas.core.array_algos.putmask\nimport time:       298 |        298 |                       pandas.core.indexes.frozen\nimport time:      3652 |       3652 |                       pandas.core.strings.accessor\nimport time:      4108 |      11296 |                     pandas.core.indexes.base\nimport time:       375 |        375 |                       pandas.core.indexes.extension\nimport time:       618 |        993 |                     pandas.core.indexes.category\nimport time:       864 |        864 |                         pandas.core.indexes.range\nimport time:       196 |        196 |                           pandas.core.tools\nimport time:       312 |        507 |                         pandas.core.tools.timedeltas\nimport time:      1015 |       2385 |                       pandas.core.indexes.datetimelike\nimport time:       235 |        235 |                       pandas.core.tools.times\nimport time:      1032 |       3652 |                     pandas.core.indexes.datetimes\nimport time:      1531 |       1531 |                       pandas.core.indexes.multi\nimport time:       639 |        639 |                       pandas.core.indexes.timedeltas\nimport time:      4142 |       6310 |                     pandas.core.indexes.interval\nimport time:       609 |        609 |                     pandas.core.indexes.period\nimport time:       516 |      23598 |                   pandas.core.indexes.api\nimport time:      1585 |      25602 |                 pandas.core.indexing\nimport time:       181 |        181 |                 pandas.core.sample\nimport time:       177 |        177 |                 pandas.core.array_algos.replace\nimport time:      1386 |       1386 |                     pandas.core.internals.blocks\nimport time:       205 |       1590 |                   pandas.core.internals.api\nimport time:       437 |        437 |                     pandas.core.internals.base\nimport time:       637 |        637 |                       pandas.core.internals.ops\nimport time:      1098 |       1735 |                     pandas.core.internals.managers\nimport time:       757 |       2928 |                   pandas.core.internals.array_manager\nimport time:       343 |        343 |                   pandas.core.internals.concat\nimport time:       170 |       5030 |                 pandas.core.internals\nimport time:       430 |        430 |                 pandas.core.internals.construction\nimport time:       208 |        208 |                   pandas.core.methods\nimport time:       135 |        135 |                     pandas.core.reshape\nimport time:       868 |       1002 |                   pandas.core.reshape.concat\nimport time:       734 |        734 |                       gzip\nimport time:       422 |        422 |                       mmap\nimport time:       806 |        806 |                           glob\nimport time:       830 |       1635 |                         pathlib._abc\nimport time:       112 |        112 |                             _winapi\nimport time:        83 |         83 |                             nt\nimport time:       122 |        122 |                             nt\nimport time:        77 |         77 |                             nt\nimport time:        68 |         68 |                             nt\nimport time:       168 |        168 |                             nt\nimport time:        70 |         70 |                             nt\nimport time:        62 |         62 |                             nt\nimport time:       407 |       1164 |                           ntpath\nimport time:        95 |         95 |                           pwd\nimport time:       287 |        287 |                           grp\nimport time:      1415 |       2961 |                         pathlib._local\nimport time:       250 |       4845 |                       pathlib\nimport time:      2097 |       2097 |                       tarfile\nimport time:       193 |        193 |                         urllib\nimport time:      1943 |       1943 |                         ipaddress\nimport time:      1949 |       4084 |                       urllib.parse\nimport time:       232 |        232 |                           zipfile._path.glob\nimport time:       590 |        822 |                         zipfile._path\nimport time:      1351 |       2173 |                       zipfile\nimport time:       147 |        147 |                       pandas.core.shared_docs\nimport time:      2480 |      16979 |                     pandas.io.common\nimport time:      1257 |      18236 |                   pandas.io.formats.format\nimport time:       540 |      19985 |                 pandas.core.methods.describe\nimport time:       121 |        121 |                       pandas._libs.window\nimport time:       620 |        741 |                     pandas._libs.window.aggregations\nimport time:       450 |        450 |                       pandas._libs.window.indexers\nimport time:       517 |        967 |                     pandas.core.indexers.objects\nimport time:       213 |        213 |                     pandas.core.util.numba_\nimport time:       227 |        227 |                     pandas.core.window.common\nimport time:       250 |        250 |                     pandas.core.window.doc\nimport time:       526 |        526 |                     pandas.core.window.numba_\nimport time:       226 |        226 |                     pandas.core.window.online\nimport time:      2202 |       2202 |                     pandas.core.window.rolling\nimport time:       939 |       6287 |                   pandas.core.window.ewm\nimport time:      1151 |       1151 |                   pandas.core.window.expanding\nimport time:       197 |       7634 |                 pandas.core.window\nimport time:      5819 |      68184 |               pandas.core.generic\nimport time:       319 |        319 |               pandas.core.methods.selectn\nimport time:       110 |        110 |                 pandas.core.reshape.util\nimport time:       185 |        185 |                 pandas.core.tools.numeric\nimport time:       346 |        641 |               pandas.core.reshape.melt\nimport time:       462 |        462 |                 pandas._libs.reshape\nimport time:       786 |        786 |                 pandas.core.indexes.accessors\nimport time:       161 |        161 |                   pandas.arrays\nimport time:       936 |       1097 |                 pandas.core.tools.datetimes\nimport time:      1123 |       1123 |                 pandas.io.formats.info\nimport time:       973 |        973 |                   pandas.plotting._core\nimport time:       274 |        274 |                   pandas.plotting._misc\nimport time:       238 |       1484 |                 pandas.plotting\nimport time:      4303 |       9253 |               pandas.core.series\nimport time:      8778 |      87172 |             pandas.core.frame\nimport time:      1241 |       1241 |             pandas.core.groupby.base\nimport time:       970 |        970 |               pandas._libs.groupby\nimport time:       131 |        131 |                 pandas.core.groupby.categorical\nimport time:       562 |        692 |               pandas.core.groupby.grouper\nimport time:      1104 |       2765 |             pandas.core.groupby.ops\nimport time:       189 |        189 |               pandas.core.groupby.numba_\nimport time:       351 |        351 |               pandas.core.groupby.indexing\nimport time:      2496 |       3035 |             pandas.core.groupby.groupby\nimport time:      2279 |      99104 |           pandas.core.groupby.generic\nimport time:       223 |      99327 |         pandas.core.groupby\nimport time:       299 |     195645 |       pandas.core.api\nimport time:       186 |        186 |       pandas.tseries.api\nimport time:       104 |        104 |               pandas.core.computation.common\nimport time:       201 |        305 |             pandas.core.computation.align\nimport time:       347 |        347 |                 pprint\nimport time:       298 |        644 |               pandas.core.computation.scope\nimport time:       676 |       1320 |             pandas.core.computation.ops\nimport time:       260 |       1885 |           pandas.core.computation.engines\nimport time:       567 |        567 |             pandas.core.computation.parsing\nimport time:      1195 |       1761 |           pandas.core.computation.expr\nimport time:       235 |       3880 |         pandas.core.computation.eval\nimport time:       104 |       3984 |       pandas.core.computation.api\nimport time:       233 |        233 |         pandas.core.reshape.encoding\nimport time:       324 |        324 |             _uuid\nimport time:       640 |        963 |           uuid\nimport time:      1002 |       1965 |         pandas.core.reshape.merge\nimport time:       812 |        812 |         pandas.core.reshape.pivot\nimport time:       315 |        315 |         pandas.core.reshape.tile\nimport time:       225 |       3548 |       pandas.core.reshape.api\nimport time:       211 |        211 |         pandas.api.extensions\nimport time:        97 |         97 |         pandas.api.indexers\nimport time:        77 |         77 |             pandas.core.interchange\nimport time:      1056 |       1132 |           pandas.core.interchange.dataframe_protocol\nimport time:       565 |        565 |             pandas.core.interchange.utils\nimport time:       436 |       1001 |           pandas.core.interchange.from_dataframe\nimport time:       137 |       2269 |         pandas.api.interchange\nimport time:       118 |        118 |           pandas.core.dtypes.api\nimport time:       161 |        278 |         pandas.api.types\nimport time:      1451 |       1451 |           pandas.core.resample\nimport time:       307 |        307 |                 pandas._libs.json\nimport time:       291 |        291 |                 pandas.io.json._normalize\nimport time:       210 |        210 |                 pandas.io.json._table_schema\nimport time:       678 |        678 |                       pandas._libs.parsers\nimport time:       767 |        767 |                         pandas.io.parsers.base_parser\nimport time:       372 |       1138 |                       pandas.io.parsers.arrow_parser_wrapper\nimport time:       298 |        298 |                       pandas.io.parsers.c_parser_wrapper\nimport time:       593 |        593 |                       pandas.io.parsers.python_parser\nimport time:      2626 |       5331 |                     pandas.io.parsers.readers\nimport time:       219 |       5549 |                   pandas.io.parsers\nimport time:        59 |       5608 |                 pandas.io.parsers.readers\nimport time:      1992 |       8405 |               pandas.io.json._json\nimport time:       158 |       8563 |             pandas.io.json\nimport time:        67 |       8629 |           pandas.io.json._json\nimport time:      1871 |       1871 |           pandas.io.stata\nimport time:       184 |      12133 |         pandas.api.typing\nimport time:       300 |      15286 |       pandas.api\nimport time:       617 |        617 |               tempfile\nimport time:       228 |        844 |             pandas._testing.contexts\nimport time:       268 |       1112 |           pandas._testing._io\nimport time:       257 |        257 |           pandas._testing._warnings\nimport time:       210 |        210 |               cmath\nimport time:       326 |        535 |             pandas._libs.testing\nimport time:       458 |        992 |           pandas._testing.asserters\nimport time:       234 |        234 |           pandas._testing.compat\nimport time:       574 |       3167 |         pandas._testing\nimport time:       187 |       3353 |       pandas.testing\nimport time:       201 |        201 |       pandas.util._print_versions\nimport time:       142 |        142 |         pandas.io.clipboards\nimport time:      3554 |       3554 |             pandas.io.excel._util\nimport time:       445 |        445 |             pandas.io.excel._calamine\nimport time:       325 |        325 |             pandas.io.excel._odfreader\nimport time:       500 |        500 |             pandas.io.excel._openpyxl\nimport time:       227 |        227 |             pandas.io.excel._pyxlsb\nimport time:       298 |        298 |             pandas.io.excel._xlrd\nimport time:      1579 |       6925 |           pandas.io.excel._base\nimport time:       305 |        305 |           pandas.io.excel._odswriter\nimport time:       258 |        258 |           pandas.io.excel._xlsxwriter\nimport time:       190 |       7676 |         pandas.io.excel\nimport time:       202 |        202 |         pandas.io.feather_format\nimport time:       172 |        172 |         pandas.io.gbq\nimport time:       743 |        743 |         pandas.io.html\nimport time:       157 |        157 |         pandas.io.orc\nimport time:       731 |        731 |         pandas.io.parquet\nimport time:       249 |        249 |           pandas.compat.pickle_compat\nimport time:       664 |        913 |         pandas.io.pickle\nimport time:       704 |        704 |           pandas.core.computation.pytables\nimport time:      2452 |       3155 |         pandas.io.pytables\nimport time:       370 |        370 |           pandas.io.sas.sasreader\nimport time:       183 |        552 |         pandas.io.sas\nimport time:       145 |        145 |         pandas.io.spss\nimport time:       952 |        952 |         pandas.io.sql\nimport time:       607 |        607 |         pandas.io.xml\nimport time:       304 |      16444 |       pandas.io.api\nimport time:       196 |        196 |       pandas.util._tester\nimport time:       109 |        109 |       pandas._version_meson\nimport time:       488 |     267332 |     pandas\nimport time:       477 |        477 |       numpy._typing._add_docstring\nimport time:       161 |        638 |     numpy.typing\nimport time:       221 |        221 |         _colorize\nimport time:      1167 |       1388 |       traceback\nimport time:       411 |       1799 |     optimagic.exceptions\nimport time:       161 |        161 |       optimagic.optimization\nimport time:       216 |        376 |     optimagic.optimization.algo_options\nimport time:       683 |        683 |           _socket\nimport time:      1627 |       2309 |         typing_extensions\nimport time:      9139 |      11448 |       annotated_types\nimport time:      3473 |      14920 |     optimagic.typing\nimport time:      6801 |     432314 |   optimagic.constraints\nimport time:       281 |        281 |                 sqlalchemy.util.preloaded\nimport time:       102 |        102 |                     sqlalchemy.cyextension\nimport time:       558 |        558 |                     sqlalchemy.cyextension.collections\nimport time:       355 |        355 |                     sqlalchemy.cyextension.immutabledict\nimport time:       287 |        287 |                     sqlalchemy.cyextension.processors\nimport time:       377 |        377 |                     sqlalchemy.cyextension.resultproxy\nimport time:       421 |        421 |                             email\nimport time:       502 |        502 |                             importlib.metadata._meta\nimport time:       387 |        387 |                             importlib.metadata._collections\nimport time:       230 |        230 |                             importlib.metadata._functools\nimport time:       149 |        149 |                             importlib.metadata._itertools\nimport time:       690 |        690 |                                   importlib.resources.abc\nimport time:       758 |       1448 |                                 importlib.resources._common\nimport time:       369 |        369 |                                 importlib.resources._functional\nimport time:       721 |       2537 |                               importlib.resources\nimport time:       653 |       3189 |                             importlib.abc\nimport time:      2661 |       7536 |                           importlib.metadata\nimport time:       870 |       8405 |                         sqlalchemy.util.compat\nimport time:      2326 |      10730 |                       sqlalchemy.exc\nimport time:       942 |      11671 |                     sqlalchemy.cyextension.util\nimport time:       568 |      13915 |                   sqlalchemy.util._has_cy\nimport time:      1821 |       1821 |                   sqlalchemy.util.typing\nimport time:      1742 |      17477 |                 sqlalchemy.util._collections\nimport time:       183 |        183 |                         concurrent\nimport time:       106 |        106 |                             atexit\nimport time:      3203 |       3309 |                           logging\nimport time:      1440 |       4748 |                         concurrent.futures._base\nimport time:       268 |       5198 |                       concurrent.futures\nimport time:       351 |        351 |                         _heapq\nimport time:       425 |        776 |                       heapq\nimport time:       448 |        448 |                         array\nimport time:      2269 |       2717 |                       socket\nimport time:      2312 |       2312 |                         _ssl\nimport time:      3974 |       6285 |                       ssl\nimport time:       491 |        491 |                       asyncio.constants\nimport time:       225 |        225 |                       asyncio.coroutines\nimport time:       293 |        293 |                         asyncio.format_helpers\nimport time:       249 |        249 |                           asyncio.base_futures\nimport time:       361 |        361 |                           asyncio.exceptions\nimport time:       235 |        235 |                           asyncio.base_tasks\nimport time:       781 |       1624 |                         _asyncio\nimport time:       983 |       2899 |                       asyncio.events\nimport time:       501 |        501 |                       asyncio.futures\nimport time:       371 |        371 |                       asyncio.protocols\nimport time:       542 |        542 |                         asyncio.transports\nimport time:       320 |        320 |                         asyncio.log\nimport time:      2067 |       2929 |                       asyncio.sslproto\nimport time:       261 |        261 |                           asyncio.mixins\nimport time:      1100 |       1361 |                         asyncio.locks\nimport time:       609 |        609 |                           asyncio.queues\nimport time:       874 |        874 |                           asyncio.timeouts\nimport time:      1145 |       2628 |                         asyncio.tasks\nimport time:       464 |       4451 |                       asyncio.staggered\nimport time:       482 |        482 |                       asyncio.trsock\nimport time:      2035 |      29354 |                     asyncio.base_events\nimport time:       657 |        657 |                     asyncio.runners\nimport time:       912 |        912 |                     asyncio.streams\nimport time:       502 |        502 |                     asyncio.subprocess\nimport time:       389 |        389 |                     asyncio.taskgroups\nimport time:       189 |        189 |                     asyncio.threads\nimport time:       573 |        573 |                       asyncio.base_subprocess\nimport time:      1562 |       1562 |                       asyncio.selector_events\nimport time:      1389 |       3523 |                     asyncio.unix_events\nimport time:       491 |      36012 |                   asyncio\nimport time:       725 |        725 |                     greenlet._greenlet\nimport time:       464 |       1188 |                   greenlet\nimport time:      3224 |       3224 |                     sqlalchemy.util.langhelpers\nimport time:       729 |       3952 |                   sqlalchemy.util._concurrency_py3k\nimport time:       424 |      41575 |                 sqlalchemy.util.concurrency\nimport time:       425 |        425 |                 sqlalchemy.util.deprecations\nimport time:       520 |      60275 |               sqlalchemy.util\nimport time:       981 |        981 |                                 sqlalchemy.event.registry\nimport time:       270 |       1250 |                               sqlalchemy.event.legacy\nimport time:      1004 |       2254 |                             sqlalchemy.event.attr\nimport time:       750 |       3003 |                           sqlalchemy.event.base\nimport time:       224 |       3227 |                         sqlalchemy.event.api\nimport time:       152 |       3378 |                       sqlalchemy.event\nimport time:       505 |        505 |                             sqlalchemy.log\nimport time:      2843 |       3347 |                           sqlalchemy.pool.base\nimport time:      1408 |       4755 |                         sqlalchemy.pool.events\nimport time:       564 |        564 |                           sqlalchemy.util.queue\nimport time:       550 |       1114 |                         sqlalchemy.pool.impl\nimport time:       298 |       6165 |                       sqlalchemy.pool\nimport time:      1071 |       1071 |                             sqlalchemy.sql.roles\nimport time:       530 |        530 |                             sqlalchemy.inspection\nimport time:      2198 |       3798 |                           sqlalchemy.sql._typing\nimport time:      5219 |       5219 |                             sqlalchemy.sql.visitors\nimport time:      1505 |       1505 |                             sqlalchemy.sql.cache_key\nimport time:      1131 |       1131 |                               sqlalchemy.sql.operators\nimport time:       877 |       2008 |                             sqlalchemy.sql.traversals\nimport time:      4057 |      12787 |                           sqlalchemy.sql.base\nimport time:      1876 |       1876 |                             sqlalchemy.sql.coercions\nimport time:       891 |        891 |                                   sqlalchemy.sql.annotation\nimport time:      2619 |       2619 |                                       sqlalchemy.sql.type_api\nimport time:     10071 |      12689 |                                     sqlalchemy.sql.elements\nimport time:       323 |        323 |                                     sqlalchemy.util.topological\nimport time:      2781 |      15792 |                                   sqlalchemy.sql.ddl\nimport time:       185 |        185 |                                           sqlalchemy.engine._py_processors\nimport time:       160 |        344 |                                         sqlalchemy.engine.processors\nimport time:      3607 |       3951 |                                       sqlalchemy.sql.sqltypes\nimport time:     11255 |      15205 |                                     sqlalchemy.sql.selectable\nimport time:      5482 |      20686 |                                   sqlalchemy.sql.schema\nimport time:      1629 |      38997 |                                 sqlalchemy.sql.util\nimport time:      4515 |      43511 |                               sqlalchemy.sql.dml\nimport time:      1313 |      44824 |                             sqlalchemy.sql.crud\nimport time:      4295 |       4295 |                             sqlalchemy.sql.functions\nimport time:      8116 |      59109 |                           sqlalchemy.sql.compiler\nimport time:       127 |        127 |                             sqlalchemy.sql._dml_constructors\nimport time:       657 |        657 |                             sqlalchemy.sql._elements_constructors\nimport time:       409 |        409 |                             sqlalchemy.sql._selectable_constructors\nimport time:      1242 |       1242 |                             sqlalchemy.sql.lambdas\nimport time:       468 |       2900 |                           sqlalchemy.sql.expression\nimport time:       589 |        589 |                           sqlalchemy.sql.default_comparator\nimport time:       938 |        938 |                             sqlalchemy.sql.events\nimport time:       695 |       1633 |                           sqlalchemy.sql.naming\nimport time:      8011 |      88823 |                         sqlalchemy.sql\nimport time:        37 |      88860 |                       sqlalchemy.sql.compiler\nimport time:      4016 |     102418 |                     sqlalchemy.engine.interfaces\nimport time:       465 |        465 |                     sqlalchemy.engine.util\nimport time:      3223 |     106105 |                   sqlalchemy.engine.base\nimport time:      2396 |     108501 |                 sqlalchemy.engine.events\nimport time:       165 |        165 |                     sqlalchemy.dialects\nimport time:      1129 |       1293 |                   sqlalchemy.engine.url\nimport time:       253 |        253 |                   sqlalchemy.engine.mock\nimport time:       937 |       2482 |                 sqlalchemy.engine.create\nimport time:      1577 |       1577 |                     sqlalchemy.engine.row\nimport time:      2759 |       4335 |                   sqlalchemy.engine.result\nimport time:      1429 |       5763 |                 sqlalchemy.engine.cursor\nimport time:      2641 |       2641 |                 sqlalchemy.engine.reflection\nimport time:       388 |     119773 |               sqlalchemy.engine\nimport time:       430 |        430 |               sqlalchemy.schema\nimport time:       241 |        241 |               sqlalchemy.types\nimport time:       291 |        291 |                 sqlalchemy.engine.characteristics\nimport time:      1880 |       2170 |               sqlalchemy.engine.default\nimport time:       802 |     183689 |             sqlalchemy\nimport time:      1130 |       1130 |                 cloudpickle.cloudpickle\nimport time:       242 |       1371 |               cloudpickle\nimport time:       674 |       2045 |             optimagic.logging.base\nimport time:        80 |         80 |                           jax\nimport time:       153 |        233 |                         pybaum.config\nimport time:       181 |        414 |                       pybaum.registry_entries\nimport time:       138 |        552 |                     pybaum.registry\nimport time:        98 |         98 |                       pybaum.equality\nimport time:        86 |         86 |                       pybaum.typecheck\nimport time:       205 |        389 |                     pybaum.tree_util\nimport time:       159 |       1098 |                   pybaum\nimport time:        92 |         92 |                     optimagic.parameters\nimport time:       187 |        279 |                   optimagic.parameters.tree_registry\nimport time:       743 |        743 |                     difflib\nimport time:       467 |        467 |                         scipy.__config__\nimport time:       111 |        111 |                         scipy.version\nimport time:        39 |         39 |                           scipy._distributor_init_local\nimport time:       154 |        193 |                         scipy._distributor_init\nimport time:        78 |         78 |                             cython\nimport time:       396 |        473 |                           scipy._lib._testutils\nimport time:       103 |        575 |                         scipy._lib\nimport time:       471 |        471 |                         scipy._lib._pep440\nimport time:       452 |        452 |                           scipy._lib._ccallback_c\nimport time:       368 |        820 |                         scipy._lib._ccallback\nimport time:       468 |       3103 |                       scipy\nimport time:      2385 |       2385 |                           scipy.linalg._fblas\nimport time:        54 |         54 |                           scipy.linalg._cblas\nimport time:        29 |         29 |                           scipy.linalg._fblas_64\nimport time:       333 |       2801 |                         scipy.linalg.blas\nimport time:      1916 |       1916 |                           scipy.linalg._flapack\nimport time:        67 |         67 |                           scipy.linalg._clapack\nimport time:        36 |         36 |                           scipy.linalg._flapack_64\nimport time:      1049 |       3066 |                         scipy.linalg.lapack\nimport time:       260 |       6126 |                       scipy.linalg._misc\nimport time:       456 |        456 |                         scipy._cyutility\nimport time:      2983 |       2983 |                         scipy.linalg.cython_lapack\nimport time:      6661 |       6661 |                                   scipy._lib.array_api_compat.common._typing\nimport time:      3158 |       9818 |                                 scipy._lib.array_api_compat.common._helpers\nimport time:       239 |      10056 |                               scipy._lib.array_api_compat.common\nimport time:      1346 |      11402 |                             scipy._lib.array_api_compat\nimport time:      1613 |       1613 |                                 numpy.f2py.diagnose\nimport time:      1723 |       1723 |                                     gettext\nimport time:      8977 |      10700 |                                   argparse\nimport time:       318 |        318 |                                   numpy.f2py._backends\nimport time:       260 |        260 |                                   numpy.f2py.__version__\nimport time:     11931 |      11931 |                                     numpy.f2py.cfuncs\nimport time:      1230 |      13161 |                                   numpy.f2py.auxfuncs\nimport time:      2525 |       2525 |                                     numpy.f2py.cb_rules\nimport time:       245 |        245 |                                     numpy.f2py._isocbind\nimport time:       438 |        438 |                                       fileinput\nimport time:      2286 |       2286 |                                             charset_normalizer.constant\nimport time:       509 |        509 |                                                 _multibytecodec\nimport time:       620 |       1128 |                                               charset_normalizer.utils\nimport time:       949 |       2077 |                                             charset_normalizer.md\nimport time:      1337 |       1337 |                                             charset_normalizer.models\nimport time:       553 |       6252 |                                           charset_normalizer.cd\nimport time:       654 |       6906 |                                         charset_normalizer.api\nimport time:       315 |        315 |                                         charset_normalizer.legacy\nimport time:       181 |        181 |                                         charset_normalizer.version\nimport time:       463 |       7863 |                                       charset_normalizer\nimport time:      2562 |       2562 |                                       numpy.f2py.symbolic\nimport time:     14846 |      25708 |                                     numpy.f2py.crackfortran\nimport time:       614 |      29090 |                                   numpy.f2py.capi_maps\nimport time:       224 |        224 |                                     numpy.f2py.func2subr\nimport time:       339 |        562 |                                   numpy.f2py.f90mod_rules\nimport time:       165 |        165 |                                     numpy.f2py.common_rules\nimport time:       157 |        157 |                                     numpy.f2py.use_rules\nimport time:      4920 |       5241 |                                   numpy.f2py.rules\nimport time:      1360 |      60688 |                                 numpy.f2py.f2py2e\nimport time:       816 |      63116 |                               numpy.f2py\nimport time:       502 |        502 |                                 numpy.core._utils\nimport time:       241 |        742 |                               numpy.core\nimport time:       167 |        167 |                               numpy.rec\nimport time:       579 |        579 |                                   numpy._core.strings\nimport time:       204 |        204 |                                   numpy.strings\nimport time:      1679 |       2461 |                                 numpy._core.defchararray\nimport time:       159 |       2619 |                               numpy.char\nimport time:       293 |        293 |                                     unittest.util\nimport time:       399 |        691 |                                   unittest.result\nimport time:       932 |        932 |                                   unittest.case\nimport time:       357 |        357 |                                   unittest.suite\nimport time:       673 |        673 |                                   unittest.loader\nimport time:       148 |        148 |                                       unittest.signals\nimport time:       308 |        456 |                                     unittest.runner\nimport time:       321 |        776 |                                   unittest.main\nimport time:       679 |       4106 |                                 unittest\nimport time:       296 |        296 |                                 numpy.testing._private\nimport time:       239 |        239 |                                 numpy.testing.overrides\nimport time:       364 |        364 |                                 numpy.testing._private.extbuild\nimport time:      3426 |       3426 |                                 numpy.testing._private.utils\nimport time:       286 |       8714 |                               numpy.testing\nimport time:       366 |        366 |                                 numpy.fft._helper\nimport time:       366 |        366 |                                   numpy.fft._pocketfft_umath\nimport time:       554 |        919 |                                 numpy.fft._pocketfft\nimport time:       245 |        245 |                                 numpy.fft.helper\nimport time:       299 |       1828 |                               numpy.fft\nimport time:      1090 |       1090 |                                 numpy.ctypeslib._ctypeslib\nimport time:       181 |       1271 |                               numpy.ctypeslib\nimport time:       269 |        269 |                                   numpy.polynomial.polyutils\nimport time:       466 |        466 |                                   numpy.polynomial._polybase\nimport time:       950 |       1684 |                                 numpy.polynomial.chebyshev\nimport time:       785 |        785 |                                 numpy.polynomial.hermite\nimport time:       819 |        819 |                                 numpy.polynomial.hermite_e\nimport time:      1019 |       1019 |                                 numpy.polynomial.laguerre\nimport time:       756 |        756 |                                 numpy.polynomial.legendre\nimport time:       770 |        770 |                                 numpy.polynomial.polynomial\nimport time:       285 |       6114 |                               numpy.polynomial\nimport time:       226 |        226 |                                 scipy._lib.array_api_compat._internal\nimport time:      1522 |       1522 |                                 scipy._lib.array_api_compat.common._aliases\nimport time:       424 |        424 |                                   scipy._lib.array_api_compat.numpy._typing\nimport time:       307 |        731 |                                 scipy._lib.array_api_compat.numpy._info\nimport time:      4912 |       7389 |                               scipy._lib.array_api_compat.numpy._aliases\nimport time:      1235 |       1235 |                                 scipy._lib.array_api_compat.common._linalg\nimport time:      1458 |       2693 |                               scipy._lib.array_api_compat.numpy.linalg\nimport time:       367 |        367 |                                 scipy._lib.array_api_compat.common._fft\nimport time:      1758 |       2124 |                               scipy._lib.array_api_compat.numpy.fft\nimport time:       601 |      97371 |                             scipy._lib.array_api_compat.numpy\nimport time:       199 |        199 |                             scipy._lib._sparse\nimport time:       470 |        470 |                                 pkgutil\nimport time:       239 |        239 |                                   _pyrepl\nimport time:       472 |        710 |                                 _pyrepl.pager\nimport time:      1964 |       3143 |                               pydoc\nimport time:      2119 |       5262 |                             scipy._lib._docscrape\nimport time:      5231 |     119462 |                           scipy._lib._array_api\nimport time:      1500 |     120962 |                         scipy._lib._util\nimport time:      9334 |     133734 |                       scipy.linalg._cythonized_array_utils\nimport time:      6405 |       6405 |                         scipy.linalg._decomp\nimport time:      2479 |       2479 |                         scipy.linalg._decomp_svd\nimport time:       458 |        458 |                         scipy.linalg._solve_toeplitz\nimport time:      4101 |      13441 |                       scipy.linalg._basic\nimport time:       356 |        356 |                         scipy.linalg._decomp_lu_cython\nimport time:      1094 |       1450 |                       scipy.linalg._decomp_lu\nimport time:       788 |        788 |                       scipy.linalg._decomp_ldl\nimport time:      1278 |       1278 |                       scipy.linalg._decomp_cholesky\nimport time:      1671 |       1671 |                       scipy.linalg._decomp_qr\nimport time:      1469 |       1469 |                       scipy.linalg._decomp_qz\nimport time:      1301 |       1301 |                       scipy.linalg._decomp_schur\nimport time:       522 |        522 |                       scipy.linalg._decomp_polar\nimport time:       245 |        245 |                         scipy._lib.deprecation\nimport time:       884 |        884 |                         scipy.linalg._expm_frechet\nimport time:       657 |        657 |                         scipy.linalg._matfuncs_schur_sqrtm\nimport time:       602 |        602 |                         scipy.linalg._matfuncs_expm\nimport time:       317 |        317 |                         scipy.linalg._linalg_pythran\nimport time:      2602 |       5304 |                       scipy.linalg._matfuncs\nimport time:       603 |        603 |                       scipy.linalg._special_matrices\nimport time:      2462 |       2462 |                       scipy.linalg._solvers\nimport time:       635 |        635 |                       scipy.linalg._procrustes\nimport time:       534 |        534 |                         scipy.linalg.cython_blas\nimport time:      2197 |       2731 |                       scipy.linalg._decomp_update\nimport time:       847 |        847 |                             scipy.sparse._sputils\nimport time:       352 |        352 |                             scipy.sparse._matrix\nimport time:       966 |       2164 |                           scipy.sparse._base\nimport time:       359 |        359 |                             scipy.sparse._sparsetools\nimport time:       382 |        382 |                               scipy.sparse._data\nimport time:       237 |        237 |                               scipy.sparse._index\nimport time:       617 |       1235 |                             scipy.sparse._compressed\nimport time:       454 |       2046 |                           scipy.sparse._csr\nimport time:       361 |        361 |                           scipy.sparse._csc\nimport time:       790 |        790 |                             scipy.sparse._csparsetools\nimport time:       438 |       1228 |                           scipy.sparse._lil\nimport time:       553 |        553 |                           scipy.sparse._dok\nimport time:      1038 |       1038 |                           scipy.sparse._coo\nimport time:       462 |        462 |                           scipy.sparse._dia\nimport time:       461 |        461 |                           scipy.sparse._bsr\nimport time:      1600 |       1600 |                           scipy.sparse._construct\nimport time:       210 |        210 |                           scipy.sparse._extract\nimport time:       159 |        159 |                           scipy.sparse._matrix_io\nimport time:       106 |        106 |                           scipy.sparse.base\nimport time:        82 |         82 |                           scipy.sparse.bsr\nimport time:        75 |         75 |                           scipy.sparse.compressed\nimport time:        79 |         79 |                           scipy.sparse.construct\nimport time:        84 |         84 |                           scipy.sparse.coo\nimport time:        74 |         74 |                           scipy.sparse.csc\nimport time:       111 |        111 |                           scipy.sparse.csr\nimport time:       112 |        112 |                           scipy.sparse.data\nimport time:        78 |         78 |                           scipy.sparse.dia\nimport time:       135 |        135 |                           scipy.sparse.dok\nimport time:       117 |        117 |                           scipy.sparse.extract\nimport time:        75 |         75 |                           scipy.sparse.lil\nimport time:        85 |         85 |                           scipy.sparse.sparsetools\nimport time:        82 |         82 |                           scipy.sparse.sputils\nimport time:       746 |      12311 |                         scipy.sparse\nimport time:       846 |      13157 |                       scipy.linalg._sketches\nimport time:       334 |        334 |                       scipy.linalg._decomp_cossin\nimport time:       119 |        119 |                       scipy.linalg.decomp\nimport time:        88 |         88 |                       scipy.linalg.decomp_cholesky\nimport time:       124 |        124 |                       scipy.linalg.decomp_lu\nimport time:       128 |        128 |                       scipy.linalg.decomp_qr\nimport time:        88 |         88 |                       scipy.linalg.decomp_svd\nimport time:        80 |         80 |                       scipy.linalg.decomp_schur\nimport time:        80 |         80 |                       scipy.linalg.basic\nimport time:        75 |         75 |                       scipy.linalg.misc\nimport time:        85 |         85 |                       scipy.linalg.special_matrices\nimport time:        93 |         93 |                       scipy.linalg.matfuncs\nimport time:       941 |     191996 |                     scipy.linalg\nimport time:       416 |     193154 |                   optimagic.utilities\nimport time:      3373 |     197902 |                 optimagic.optimization.fun_value\nimport time:      4724 |     202625 |               optimagic.logging.types\nimport time:      1040 |     203665 |             optimagic.logging.sqlalchemy\nimport time:       753 |     390151 |           optimagic.logging.logger\nimport time:       142 |     390292 |         optimagic.logging\nimport time:        96 |     390387 |       optimagic.logging.types\nimport time:      1399 |       1399 |         optimagic.timing\nimport time:      1440 |       2839 |       optimagic.optimization.history\nimport time:       267 |        267 |                 _multiprocessing\nimport time:       525 |        525 |                     multiprocessing.process\nimport time:       387 |        387 |                     multiprocessing.reduction\nimport time:       687 |       1598 |                   multiprocessing.context\nimport time:       338 |       1936 |                 multiprocessing\nimport time:       337 |       2538 |               joblib._multiprocessing_helpers\nimport time:       153 |        153 |                 joblib.externals\nimport time:       195 |        195 |                 joblib.externals.loky._base\nimport time:       399 |        399 |                       multiprocessing.util\nimport time:       508 |        906 |                     multiprocessing.synchronize\nimport time:       902 |        902 |                           _queue\nimport time:       545 |       1447 |                         queue\nimport time:       131 |        131 |                           _winapi\nimport time:       968 |       1099 |                         multiprocessing.connection\nimport time:       445 |        445 |                         multiprocessing.queues\nimport time:       808 |       3797 |                       concurrent.futures.process\nimport time:       280 |        280 |                       joblib.externals.loky.backend.process\nimport time:       455 |       4531 |                     joblib.externals.loky.backend.context\nimport time:       198 |       5633 |                   joblib.externals.loky.backend\nimport time:        35 |       5668 |                 joblib.externals.loky.backend.context\nimport time:       176 |        176 |                   joblib.externals.loky.backend._posix_reduction\nimport time:       758 |        758 |                     joblib.externals.cloudpickle.cloudpickle\nimport time:       306 |       1064 |                   joblib.externals.cloudpickle\nimport time:       370 |       1609 |                 joblib.externals.loky.backend.reduction\nimport time:       107 |        107 |                     faulthandler\nimport time:       336 |        336 |                     joblib.externals.loky.backend.queues\nimport time:        88 |         88 |                       psutil\nimport time:       204 |        292 |                     joblib.externals.loky.backend.utils\nimport time:       157 |        157 |                     joblib.externals.loky.initializers\nimport time:       115 |        115 |                     psutil\nimport time:       937 |       1940 |                   joblib.externals.loky.process_executor\nimport time:       279 |       2219 |                 joblib.externals.loky.reusable_executor\nimport time:       215 |        215 |                 joblib.externals.loky.cloudpickle_wrapper\nimport time:       353 |      10410 |               joblib.externals.loky\nimport time:       187 |      13135 |             joblib._cloudpickle_wrapper\nimport time:      4313 |       4313 |               joblib._utils\nimport time:       741 |        741 |               multiprocessing.pool\nimport time:       394 |        394 |                   joblib.backports\nimport time:       191 |        191 |                   joblib.disk\nimport time:       191 |        191 |                         runpy\nimport time:       200 |        390 |                       multiprocessing.spawn\nimport time:       403 |        403 |                       _posixshmem\nimport time:       252 |       1044 |                     multiprocessing.resource_tracker\nimport time:       181 |        181 |                     joblib.externals.loky.backend.spawn\nimport time:       236 |       1460 |                   joblib.externals.loky.backend.resource_tracker\nimport time:        91 |         91 |                       lz4\nimport time:       493 |        583 |                     joblib.compressor\nimport time:       169 |        169 |                       joblib.numpy_pickle_utils\nimport time:       300 |        468 |                     joblib.numpy_pickle_compat\nimport time:       389 |       1440 |                   joblib.numpy_pickle\nimport time:       417 |       3900 |                 joblib._memmapping_reducer\nimport time:       235 |       4134 |               joblib.executor\nimport time:       318 |        318 |               joblib.pool\nimport time:       593 |      10098 |             joblib._parallel_backends\nimport time:       190 |        190 |               joblib.logger\nimport time:       618 |        807 |             joblib._store_backends\nimport time:       244 |        244 |             joblib.hashing\nimport time:       433 |        433 |               joblib.func_inspect\nimport time:       968 |       1400 |             joblib.memory\nimport time:       628 |        628 |             joblib.parallel\nimport time:       262 |      26571 |           joblib\nimport time:       104 |        104 |             pathos\nimport time:        33 |        137 |           pathos.pools\nimport time:       513 |        513 |                         scipy.sparse.linalg._interface\nimport time:       162 |        162 |                         scipy.sparse.linalg._isolve.utils\nimport time:       322 |        996 |                       scipy.sparse.linalg._isolve.iterative\nimport time:       240 |        240 |                       scipy.sparse.linalg._isolve.minres\nimport time:       160 |        160 |                         scipy.sparse.linalg._isolve._gcrotmk\nimport time:       153 |        312 |                       scipy.sparse.linalg._isolve.lgmres\nimport time:       139 |        139 |                       scipy.sparse.linalg._isolve.lsqr\nimport time:       175 |        175 |                       scipy.sparse.linalg._isolve.lsmr\nimport time:       105 |        105 |                       scipy.sparse.linalg._isolve.tfqmr\nimport time:       207 |       2171 |                     scipy.sparse.linalg._isolve\nimport time:       698 |        698 |                         scipy.sparse.linalg._dsolve._superlu\nimport time:        85 |         85 |                           scikits\nimport time:        63 |        147 |                         scikits.umfpack\nimport time:       385 |       1229 |                       scipy.sparse.linalg._dsolve.linsolve\nimport time:       138 |        138 |                       scipy.sparse.linalg._dsolve._add_newdocs\nimport time:       195 |       1561 |                     scipy.sparse.linalg._dsolve\nimport time:       908 |        908 |                             scipy._lib.decorator\nimport time:       608 |       1516 |                           scipy._lib._threadsafety\nimport time:      1124 |       1124 |                           scipy.sparse.linalg._eigen.arpack._arpack\nimport time:       779 |       3417 |                         scipy.sparse.linalg._eigen.arpack.arpack\nimport time:       144 |       3561 |                       scipy.sparse.linalg._eigen.arpack\nimport time:       321 |        321 |                         scipy.sparse.linalg._eigen.lobpcg.lobpcg\nimport time:       218 |        538 |                       scipy.sparse.linalg._eigen.lobpcg\nimport time:        82 |         82 |                           scipy.sparse.linalg._propack\nimport time:       807 |        807 |                           scipy.sparse.linalg._propack._spropack\nimport time:       626 |        626 |                           scipy.sparse.linalg._propack._dpropack\nimport time:       661 |        661 |                           scipy.sparse.linalg._propack._cpropack\nimport time:       603 |        603 |                           scipy.sparse.linalg._propack._zpropack\nimport time:       332 |       3109 |                         scipy.sparse.linalg._svdp\nimport time:      1525 |       4633 |                       scipy.sparse.linalg._eigen._svds\nimport time:       164 |       8894 |                     scipy.sparse.linalg._eigen\nimport time:       206 |        206 |                         scipy.sparse.linalg._onenormest\nimport time:       341 |        547 |                       scipy.sparse.linalg._expm_multiply\nimport time:       465 |       1011 |                     scipy.sparse.linalg._matfuncs\nimport time:       246 |        246 |                     scipy.sparse.linalg._norm\nimport time:       405 |        405 |                     scipy.sparse.linalg._special_sparse_arrays\nimport time:       119 |        119 |                     scipy.sparse.linalg.isolve\nimport time:        82 |         82 |                     scipy.sparse.linalg.dsolve\nimport time:        78 |         78 |                     scipy.sparse.linalg.interface\nimport time:        73 |         73 |                     scipy.sparse.linalg.eigen\nimport time:        77 |         77 |                     scipy.sparse.linalg.matfuncs\nimport time:       293 |      15004 |                   scipy.sparse.linalg\nimport time:       223 |        223 |                     scipy.optimize._dcsrch\nimport time:       279 |        501 |                   scipy.optimize._linesearch\nimport time:       263 |        263 |                     scipy.optimize._group_columns\nimport time:       103 |        103 |                         scipy._lib.array_api_extra._lib\nimport time:       104 |        104 |                             scipy._lib.array_api_extra._lib._utils\nimport time:       234 |        234 |                               scipy._lib._array_api_compat_vendor\nimport time:       272 |        506 |                             scipy._lib.array_api_extra._lib._utils._compat\nimport time:       252 |        252 |                               scipy._lib.array_api_extra._lib._utils._typing\nimport time:       477 |        728 |                             scipy._lib.array_api_extra._lib._utils._helpers\nimport time:       556 |       1892 |                           scipy._lib.array_api_extra._lib._at\nimport time:       738 |       2630 |                         scipy._lib.array_api_extra._lib._funcs\nimport time:       373 |       3105 |                       scipy._lib.array_api_extra._delegation\nimport time:       412 |        412 |                       scipy._lib.array_api_extra._lib._lazy\nimport time:       168 |       3683 |                     scipy._lib.array_api_extra\nimport time:       413 |       4358 |                   scipy.optimize._numdiff\nimport time:       526 |        526 |                     scipy.optimize._hessian_update_strategy\nimport time:       696 |       1222 |                   scipy.optimize._differentiable_functions\nimport time:      3313 |      24397 |                 scipy.optimize._optimize\nimport time:       270 |        270 |                     scipy.optimize._trustregion\nimport time:       254 |        523 |                   scipy.optimize._trustregion_dogleg\nimport time:       260 |        260 |                   scipy.optimize._trustregion_ncg\nimport time:       358 |        358 |                         scipy._lib.messagestream\nimport time:       659 |       1016 |                       scipy.optimize._trlib._trlib\nimport time:       256 |       1271 |                     scipy.optimize._trlib\nimport time:       176 |       1446 |                   scipy.optimize._trustregion_krylov\nimport time:       291 |        291 |                   scipy.optimize._trustregion_exact\nimport time:       374 |        374 |                       scipy.optimize._constraints\nimport time:       103 |        103 |                             sksparse\nimport time:        45 |        147 |                           sksparse.cholmod\nimport time:       267 |        413 |                         scipy.optimize._trustregion_constr.projections\nimport time:       281 |        281 |                         scipy.optimize._trustregion_constr.qp_subproblem\nimport time:       386 |       1078 |                       scipy.optimize._trustregion_constr.equality_constrained_sqp\nimport time:       345 |        345 |                       scipy.optimize._trustregion_constr.canonical_constraint\nimport time:       299 |        299 |                       scipy.optimize._trustregion_constr.tr_interior_point\nimport time:       290 |        290 |                       scipy.optimize._trustregion_constr.report\nimport time:       530 |       2914 |                     scipy.optimize._trustregion_constr.minimize_trustregion_constr\nimport time:       164 |       3077 |                   scipy.optimize._trustregion_constr\nimport time:       792 |        792 |                     scipy.optimize._lbfgsb\nimport time:       448 |       1239 |                   scipy.optimize._lbfgsb_py\nimport time:       426 |        426 |                     scipy.optimize._moduleTNC\nimport time:       322 |        748 |                   scipy.optimize._tnc\nimport time:       268 |        268 |                   scipy.optimize._cobyla_py\nimport time:       205 |        205 |                   scipy.optimize._cobyqa_py\nimport time:       704 |        704 |                     scipy.optimize._slsqplib\nimport time:       419 |       1122 |                   scipy.optimize._slsqp_py\nimport time:       760 |       9935 |                 scipy.optimize._minimize\nimport time:       305 |        305 |                     scipy.optimize._minpack\nimport time:       877 |        877 |                           scipy.optimize._lsq.common\nimport time:       255 |       1131 |                         scipy.optimize._lsq.trf\nimport time:       292 |        292 |                         scipy.optimize._lsq.dogbox\nimport time:       467 |       1889 |                       scipy.optimize._lsq.least_squares\nimport time:       282 |        282 |                           scipy.optimize._lsq.givens_elimination\nimport time:       284 |        566 |                         scipy.optimize._lsq.trf_linear\nimport time:       207 |        207 |                         scipy.optimize._lsq.bvls\nimport time:       213 |        984 |                       scipy.optimize._lsq.lsq_linear\nimport time:       270 |       3141 |                     scipy.optimize._lsq\nimport time:       450 |       3895 |                   scipy.optimize._minpack_py\nimport time:       235 |        235 |                   scipy.optimize._spectral\nimport time:      2476 |       2476 |                   scipy.optimize._nonlin\nimport time:       378 |       6982 |                 scipy.optimize._root\nimport time:       188 |        188 |                     scipy.optimize._zeros\nimport time:       455 |        643 |                   scipy.optimize._zeros_py\nimport time:       363 |       1005 |                 scipy.optimize._root_scalar\nimport time:       616 |        616 |                 scipy.optimize._nnls\nimport time:      1698 |       1698 |                 scipy.optimize._basinhopping\nimport time:       108 |        108 |                       scipy.optimize._highspy\nimport time:      5477 |       5477 |                       scipy.optimize._highspy._core\nimport time:       484 |        484 |                       scipy.optimize._highspy._highs_options\nimport time:       382 |       6449 |                     scipy.optimize._highspy._highs_wrapper\nimport time:       238 |       6687 |                   scipy.optimize._linprog_highs\nimport time:       138 |        138 |                                   uarray\nimport time:       468 |        468 |                                       scipy._lib._uarray._uarray\nimport time:       527 |        995 |                                     scipy._lib._uarray._backend\nimport time:       296 |       1290 |                                   scipy._lib._uarray\nimport time:       229 |       1656 |                                 scipy._lib.uarray\nimport time:      1051 |       2707 |                               scipy.fft._basic\nimport time:       534 |        534 |                               scipy.fft._realtransforms\nimport time:       237 |        237 |                                     scipy.special._sf_error\nimport time:       381 |        381 |                                       scipy.special._ufuncs_cxx\nimport time:       324 |        324 |                                       scipy.special._ellip_harm_2\nimport time:       763 |        763 |                                       scipy.special._special_ufuncs\nimport time:       486 |        486 |                                       scipy.special._gufuncs\nimport time:      1121 |       3073 |                                     scipy.special._ufuncs\nimport time:     10733 |      10733 |                                     scipy.special._support_alternative_backends\nimport time:       171 |        171 |                                       scipy.special._input_validation\nimport time:       757 |        757 |                                       scipy.special._specfun\nimport time:       284 |        284 |                                       scipy.special._comb\nimport time:       479 |        479 |                                       scipy.special._multiufuncs\nimport time:       944 |       2633 |                                     scipy.special._basic\nimport time:      1752 |       1752 |                                     scipy.special._logsumexp\nimport time:       656 |        656 |                                     scipy.special._orthogonal\nimport time:       160 |        160 |                                     scipy.special._spfun_stats\nimport time:       192 |        192 |                                     scipy.special._ellip_harm\nimport time:       199 |        199 |                                     scipy.special._lambertw\nimport time:       198 |        198 |                                     scipy.special._spherical_bessel\nimport time:       105 |        105 |                                     scipy.special.add_newdocs\nimport time:       176 |        176 |                                     scipy.special.basic\nimport time:       127 |        127 |                                     scipy.special.orthogonal\nimport time:        82 |         82 |                                     scipy.special.specfun\nimport time:        74 |         74 |                                     scipy.special.sf_error\nimport time:        78 |         78 |                                     scipy.special.spfun_stats\nimport time:       818 |      21285 |                                   scipy.special\nimport time:       270 |      21555 |                                 scipy.fft._fftlog_backend\nimport time:       280 |      21835 |                               scipy.fft._fftlog\nimport time:       512 |        512 |                                     scipy.fft._pocketfft.pypocketfft\nimport time:       273 |        273 |                                     scipy.fft._pocketfft.helper\nimport time:       351 |       1134 |                                   scipy.fft._pocketfft.basic\nimport time:       319 |        319 |                                   scipy.fft._pocketfft.realtransforms\nimport time:       234 |       1687 |                                 scipy.fft._pocketfft\nimport time:       345 |       2032 |                               scipy.fft._helper\nimport time:       322 |        322 |                                 scipy.fft._basic_backend\nimport time:       295 |        295 |                                 scipy.fft._realtransforms_backend\nimport time:       264 |        880 |                               scipy.fft._backend\nimport time:       249 |      28233 |                             scipy.fft\nimport time:       527 |      28760 |                           scipy.linalg._decomp_interpolative\nimport time:       258 |      29017 |                         scipy.linalg.interpolative\nimport time:       181 |      29198 |                       scipy.optimize._remove_redundancy\nimport time:       551 |      29749 |                     scipy.optimize._linprog_util\nimport time:       103 |        103 |                     sksparse\nimport time:        59 |         59 |                       scikits\nimport time:        17 |         75 |                     scikits.umfpack\nimport time:       360 |      30285 |                   scipy.optimize._linprog_ip\nimport time:       210 |        210 |                   scipy.optimize._linprog_simplex\nimport time:       388 |        388 |                     scipy.optimize._bglu_dense\nimport time:       277 |        664 |                   scipy.optimize._linprog_rs\nimport time:       215 |        215 |                   scipy.optimize._linprog_doc\nimport time:       352 |      38411 |                 scipy.optimize._linprog\nimport time:       225 |        225 |                 scipy.optimize._lsap\nimport time:      2910 |       2910 |                 scipy.optimize._differentialevolution\nimport time:       312 |        312 |                   scipy.optimize._pava_pybind\nimport time:       200 |        511 |                 scipy.optimize._isotonic\nimport time:       537 |        537 |                     scipy.spatial._ckdtree\nimport time:       763 |       1300 |                   scipy.spatial._kdtree\nimport time:       719 |        719 |                   scipy.spatial._qhull\nimport time:       260 |        260 |                     scipy.spatial._voronoi\nimport time:       359 |        618 |                   scipy.spatial._spherical_voronoi\nimport time:       771 |        771 |                   scipy.spatial._plotutils\nimport time:       181 |        181 |                   scipy.spatial._procrustes\nimport time:       273 |        273 |                       scipy.spatial._hausdorff\nimport time:       472 |        472 |                       scipy.spatial._distance_pybind\nimport time:       231 |        231 |                       scipy.spatial._distance_wrap\nimport time:      3191 |       4166 |                     scipy.spatial.distance\nimport time:       195 |       4360 |                   scipy.spatial._geometric_slerp\nimport time:       207 |        207 |                   scipy.spatial.ckdtree\nimport time:       156 |        156 |                   scipy.spatial.kdtree\nimport time:        93 |         93 |                   scipy.spatial.qhull\nimport time:      4921 |       4921 |                             scipy.constants._codata\nimport time:      1456 |       1456 |                             scipy.constants._constants\nimport time:       269 |        269 |                             scipy.constants.codata\nimport time:       284 |        284 |                             scipy.constants.constants\nimport time:       960 |       7888 |                           scipy.constants\nimport time:       165 |       8053 |                         scipy.spatial.transform._rotation_groups\nimport time:      1136 |       9188 |                       scipy.spatial.transform._rotation\nimport time:       481 |       9669 |                     scipy.spatial.transform._rigid_transform\nimport time:       244 |        244 |                     scipy.spatial.transform._rotation_spline\nimport time:       104 |        104 |                     scipy.spatial.transform.rotation\nimport time:       189 |      10205 |                   scipy.spatial.transform\nimport time:       112 |        112 |                     scipy.optimize._shgo_lib\nimport time:       401 |        401 |                     scipy.optimize._shgo_lib._vertex\nimport time:       487 |        999 |                   scipy.optimize._shgo_lib._complex\nimport time:       869 |      20472 |                 scipy.optimize._shgo\nimport time:      1316 |       1316 |                 scipy.optimize._dual_annealing\nimport time:       288 |        288 |                 scipy.optimize._qap\nimport time:       225 |        225 |                   scipy.optimize._direct\nimport time:       394 |        619 |                 scipy.optimize._direct_py\nimport time:       233 |        233 |                 scipy.optimize._milp\nimport time:       139 |        139 |                 scipy.optimize.cobyla\nimport time:        91 |         91 |                 scipy.optimize.lbfgsb\nimport time:        77 |         77 |                 scipy.optimize.linesearch\nimport time:       107 |        107 |                 scipy.optimize.minpack\nimport time:        85 |         85 |                 scipy.optimize.minpack2\nimport time:        75 |         75 |                 scipy.optimize.moduleTNC\nimport time:        79 |         79 |                 scipy.optimize.nonlin\nimport time:        81 |         81 |                 scipy.optimize.optimize\nimport time:        78 |         78 |                 scipy.optimize.slsqp\nimport time:        70 |         70 |                 scipy.optimize.tnc\nimport time:        76 |         76 |                 scipy.optimize.zeros\nimport time:       632 |     111194 |               scipy.optimize\nimport time:      1247 |     112441 |             optimagic.parameters.bounds\nimport time:       400 |     112841 |           optimagic.deprecations\nimport time:       102 |        102 |                   _plotly_utils\nimport time:       164 |        266 |                 _plotly_utils.importers\nimport time:       195 |        460 |               plotly\nimport time:       104 |        104 |                 _plotly_utils.optional_imports\nimport time:        92 |        196 |               plotly.optional_imports\nimport time:       307 |        307 |                 plotly.graph_objs\nimport time:       198 |        198 |                   PIL._version\nimport time:      1803 |       2001 |                 _plotly_utils.basevalidators\nimport time:       478 |        478 |                   plotly.io\nimport time:       192 |        192 |                   plotly.express._special_inputs\nimport time:       172 |        172 |                   plotly.express.trendline_functions\nimport time:       406 |        406 |                       _plotly_utils.exceptions\nimport time:       169 |        169 |                         _plotly_utils.colors._swatches\nimport time:       222 |        222 |                         _plotly_utils.colors.colorbrewer\nimport time:       200 |        200 |                         _plotly_utils.colors.carto\nimport time:       305 |        894 |                       _plotly_utils.colors.qualitative\nimport time:       106 |        106 |                         _plotly_utils.colors.plotlyjs\nimport time:       165 |        165 |                         _plotly_utils.colors.cmocean\nimport time:       219 |        490 |                       _plotly_utils.colors.sequential\nimport time:       128 |        128 |                       _plotly_utils.colors.diverging\nimport time:       155 |        155 |                       _plotly_utils.colors.cyclical\nimport time:      3578 |       5648 |                     _plotly_utils.colors\nimport time:       149 |       5797 |                   plotly.colors\nimport time:       149 |        149 |                   packaging\nimport time:       168 |        168 |                     packaging._structures\nimport time:      2139 |       2306 |                   packaging.version\nimport time:       727 |        727 |                   plotly._subplots\nimport time:       668 |        668 |                     _plotly_utils.utils\nimport time:       249 |        249 |                     plotly.shapeannotation\nimport time:      1544 |       2460 |                   plotly.basedatatypes\nimport time:      6215 |      18491 |                 plotly.express._core\nimport time:       241 |        241 |                 plotly.express.imshow_utils\nimport time:      1250 |       1250 |                     _plotly_utils.png\nimport time:      3831 |       3831 |                       PIL.ExifTags\nimport time:       152 |        152 |                         PIL._deprecate\nimport time:       657 |        809 |                       PIL.ImageMode\nimport time:      1100 |       1100 |                       PIL.TiffTags\nimport time:       210 |        210 |                       PIL._binary\nimport time:       481 |        481 |                         PIL._typing\nimport time:       175 |        655 |                       PIL._util\nimport time:       101 |        101 |                       defusedxml\nimport time:      1331 |       1331 |                       PIL._imaging\nimport time:      2292 |      10327 |                     PIL.Image\nimport time:       162 |      11738 |                   _plotly_utils.data_utils\nimport time:       216 |      11953 |                 plotly.utils\nimport time:       143 |        143 |                 xarray\nimport time:      2387 |      35519 |               plotly.express._imshow\nimport time:       307 |        307 |                 plotly.express._doc\nimport time:     68148 |      68455 |               plotly.express._chart_types\nimport time:       285 |        285 |                 plotly.data\nimport time:       327 |        612 |               plotly.express.data\nimport time:       187 |        187 |               plotly.express.colors\nimport time:       329 |     105755 |             plotly.express\nimport time:       120 |        120 |             petsc4py\nimport time:        78 |         78 |             nlopt\nimport time:        64 |         64 |             pybobyqa\nimport time:        64 |         64 |             dfols\nimport time:        64 |         64 |             pygmo\nimport time:        65 |         65 |             cyipopt\nimport time:        62 |         62 |             fides\nimport time:        63 |         63 |             jax\nimport time:        59 |         59 |             tranquilo\nimport time:        63 |         63 |             numba\nimport time:       220 |        220 |                       iminuit.pdg_format\nimport time:       255 |        255 |                         iminuit.warnings\nimport time:       307 |        561 |                       iminuit._optional_dependencies\nimport time:       350 |       1130 |                     iminuit._repr_text\nimport time:       620 |       1750 |                   iminuit._repr_html\nimport time:       293 |        293 |                     iminuit._parse_version\nimport time:       255 |        255 |                         quopri\nimport time:       409 |        409 |                           email._parseaddr\nimport time:      1182 |       1591 |                         email.utils\nimport time:       833 |        833 |                         email.errors\nimport time:       374 |        374 |                             email.quoprimime\nimport time:       238 |        238 |                             email.base64mime\nimport time:       255 |        255 |                               email.encoders\nimport time:       550 |        805 |                             email.charset\nimport time:      5968 |       7384 |                           email.header\nimport time:      1357 |       8741 |                         email._policybase\nimport time:       536 |        536 |                         email._encoded_words\nimport time:       217 |        217 |                         email.iterators\nimport time:      1096 |      13265 |                       email.message\nimport time:       273 |        273 |                       importlib.metadata._text\nimport time:       361 |      13899 |                     importlib.metadata._adapters\nimport time:       900 |        900 |                       email.feedparser\nimport time:       319 |       1219 |                     email.parser\nimport time:      1564 |      16974 |                   iminuit._deprecated\nimport time:      3040 |       3040 |                   iminuit.typing\nimport time:      1736 |      23498 |                 iminuit.util\nimport time:      2411 |       2411 |                 iminuit._core\nimport time:      1838 |      27746 |               iminuit.minuit\nimport time:       376 |        376 |               iminuit.minimize\nimport time:      1122 |      29243 |             iminuit\nimport time:       495 |        495 |             nevergrad\nimport time:       412 |        412 |                   scipy.stats._warnings_errors\nimport time:       505 |        505 |                         scipy._lib.doccer\nimport time:       283 |        283 |                         scipy.stats._distr_params\nimport time:      1232 |       1232 |                         scipy.integrate._quadrature\nimport time:       896 |        896 |                           scipy.integrate._odepack\nimport time:       958 |       1853 |                         scipy.integrate._odepack_py\nimport time:       365 |        365 |                           scipy.integrate._quadpack\nimport time:       531 |        896 |                         scipy.integrate._quadpack_py\nimport time:       867 |        867 |                           scipy.integrate._vode\nimport time:       455 |        455 |                           scipy.integrate._dop\nimport time:       910 |        910 |                           scipy.integrate._lsoda\nimport time:      1077 |       3308 |                         scipy.integrate._ode\nimport time:       550 |        550 |                         scipy.integrate._bvp\nimport time:       413 |        413 |                               scipy.integrate._ivp.common\nimport time:       335 |        335 |                               scipy.integrate._ivp.base\nimport time:       700 |       1447 |                             scipy.integrate._ivp.bdf\nimport time:       726 |        726 |                             scipy.integrate._ivp.radau\nimport time:       268 |        268 |                               scipy.integrate._ivp.dop853_coefficients\nimport time:       839 |       1106 |                             scipy.integrate._ivp.rk\nimport time:       548 |        548 |                             scipy.integrate._ivp.lsoda\nimport time:       524 |       4349 |                           scipy.integrate._ivp.ivp\nimport time:       224 |       4573 |                         scipy.integrate._ivp\nimport time:       383 |        383 |                         scipy.integrate._quad_vec\nimport time:       243 |        243 |                           scipy._lib._elementwise_iterative_method\nimport time:       665 |        907 |                         scipy.integrate._tanhsinh\nimport time:       416 |        416 |                             scipy.integrate._rules._base\nimport time:       313 |        313 |                             scipy.integrate._rules._genz_malik\nimport time:       163 |        163 |                               scipy.integrate._rules._gauss_legendre\nimport time:       268 |        430 |                             scipy.integrate._rules._gauss_kronrod\nimport time:       250 |       1408 |                           scipy.integrate._rules\nimport time:      2061 |       3468 |                         scipy.integrate._cubature\nimport time:       682 |        682 |                         scipy.integrate._lebedev\nimport time:       232 |        232 |                         scipy.integrate.dop\nimport time:       107 |        107 |                         scipy.integrate.lsoda\nimport time:       486 |        486 |                         scipy.integrate.vode\nimport time:       108 |        108 |                         scipy.integrate.odepack\nimport time:       111 |        111 |                         scipy.integrate.quadpack\nimport time:       179 |        179 |                         scipy.stats._finite_differences\nimport time:       194 |        194 |                         scipy.stats._constants\nimport time:       236 |        236 |                         scipy.stats._censored_data\nimport time:      2612 |      22896 |                       scipy.stats._distn_infrastructure\nimport time:       352 |        352 |                                 scipy.interpolate._fitpack\nimport time:       408 |        408 |                                 scipy.interpolate._dfitpack\nimport time:       584 |       1343 |                               scipy.interpolate._fitpack_impl\nimport time:       312 |        312 |                                 scipy.interpolate._dierckx\nimport time:       800 |       1112 |                               scipy.interpolate._bsplines\nimport time:       194 |       2649 |                             scipy.interpolate._fitpack_py\nimport time:       374 |        374 |                             scipy.interpolate._polyint\nimport time:       422 |        422 |                             scipy.interpolate._ppoly\nimport time:       545 |        545 |                             scipy.interpolate._interpnd\nimport time:       810 |       4798 |                           scipy.interpolate._interpolate\nimport time:       640 |        640 |                           scipy.interpolate._fitpack2\nimport time:       236 |        236 |                           scipy.interpolate._rbf\nimport time:       306 |        306 |                             scipy.interpolate._rbfinterp_pythran\nimport time:       298 |        604 |                           scipy.interpolate._rbfinterp\nimport time:       409 |        409 |                           scipy.interpolate._cubic\nimport time:       226 |        226 |                           scipy.interpolate._ndgriddata\nimport time:       305 |        305 |                           scipy.interpolate._fitpack_repro\nimport time:       115 |        115 |                           scipy.interpolate._pade\nimport time:       369 |        369 |                             scipy.interpolate._rgi_cython\nimport time:       609 |        609 |                             scipy.interpolate._ndbspline\nimport time:       354 |       1331 |                           scipy.interpolate._rgi\nimport time:       448 |        448 |                           scipy.interpolate._bary_rational\nimport time:       134 |        134 |                           scipy.interpolate.fitpack\nimport time:        96 |         96 |                           scipy.interpolate.fitpack2\nimport time:        96 |         96 |                           scipy.interpolate.interpolate\nimport time:        82 |         82 |                           scipy.interpolate.ndgriddata\nimport time:        90 |         90 |                           scipy.interpolate.polyint\nimport time:       117 |        117 |                           scipy.interpolate.rbf\nimport time:       102 |        102 |                           scipy.interpolate.interpnd\nimport time:       631 |      10450 |                         scipy.interpolate\nimport time:      1294 |       1294 |                           scipy.special.cython_special\nimport time:      3321 |       4614 |                         scipy.stats._stats\nimport time:       547 |        547 |                         scipy.stats._tukeylambda_stats\nimport time:       443 |        443 |                         scipy.stats._ksstats\nimport time:     58726 |      74778 |                       scipy.stats._continuous_distns\nimport time:       651 |        651 |                         scipy.stats._biasedurn\nimport time:       344 |        344 |                         scipy.stats._stats_pythran\nimport time:     12059 |      13053 |                       scipy.stats._discrete_distns\nimport time:       409 |        409 |                         scipy.stats._levy_stable.levyst\nimport time:      2087 |       2495 |                       scipy.stats._levy_stable\nimport time:       359 |        359 |                         scipy.stats._axis_nan_policy\nimport time:      1616 |       1975 |                       scipy.stats._entropy\nimport time:       476 |     115670 |                     scipy.stats.distributions\nimport time:       177 |        177 |                       scipy._lib._bunch\nimport time:      2004 |       2004 |                       scipy.stats._stats_mstats_common\nimport time:      2010 |       4190 |                     scipy.stats._mstats_basic\nimport time:       556 |        556 |                       scipy.stats._common\nimport time:      3474 |       4030 |                     scipy.stats._hypotests\nimport time:      6325 |       6325 |                     scipy.stats._resampling\nimport time:       296 |        296 |                     scipy.stats._binomtest\nimport time:     44997 |     175505 |                   scipy.stats._stats_py\nimport time:       810 |        810 |                   scipy.stats._variation\nimport time:       444 |        444 |                     scipy.stats._ansari_swilk_statistics\nimport time:       390 |        390 |                     scipy.stats._wilcoxon\nimport time:      2283 |       2283 |                     scipy.stats._fit\nimport time:       979 |        979 |                       scipy.stats._relative_risk\nimport time:       503 |        503 |                       scipy.stats._crosstab\nimport time:       298 |        298 |                       scipy.stats._odds_ratio\nimport time:       716 |       2495 |                     scipy.stats.contingency\nimport time:      9212 |      14821 |                   scipy.stats._morestats\nimport time:       226 |        226 |                         scipy.sparse.csgraph._laplacian\nimport time:       307 |        307 |                             scipy.sparse.csgraph._tools\nimport time:       170 |        476 |                           scipy.sparse.csgraph._validation\nimport time:       553 |       1028 |                         scipy.sparse.csgraph._shortest_path\nimport time:       447 |        447 |                         scipy.sparse.csgraph._traversal\nimport time:       271 |        271 |                         scipy.sparse.csgraph._min_spanning_tree\nimport time:       372 |        372 |                         scipy.sparse.csgraph._flow\nimport time:       346 |        346 |                         scipy.sparse.csgraph._matching\nimport time:       312 |        312 |                         scipy.sparse.csgraph._reordering\nimport time:       275 |       3273 |                       scipy.sparse.csgraph\nimport time:       349 |        349 |                       scipy.stats._sobol\nimport time:       330 |        330 |                       scipy.stats._qmc_cy\nimport time:      1510 |       5461 |                     scipy.stats._qmc\nimport time:      1417 |       6877 |                   scipy.stats._multicomp\nimport time:       563 |        563 |                   scipy.stats._binned_statistic\nimport time:       430 |        430 |                       scipy.stats._covariance\nimport time:       347 |        347 |                         scipy.stats._rcont.rcont\nimport time:       205 |        552 |                       scipy.stats._rcont\nimport time:       319 |        319 |                         scipy.stats._qmvnt_cy\nimport time:       265 |        584 |                       scipy.stats._qmvnt\nimport time:      4291 |       5855 |                     scipy.stats._multivariate\nimport time:       398 |       6252 |                   scipy.stats._kde\nimport time:       630 |        630 |                     scipy.stats._mstats_extras\nimport time:       267 |        897 |                   scipy.stats.mstats\nimport time:       126 |        126 |                   scipy.stats.qmc\nimport time:       891 |        891 |                   scipy.stats._page_trend_test\nimport time:      1487 |       1487 |                   scipy.stats._mannwhitneyu\nimport time:       323 |        323 |                   scipy.stats._bws_test\nimport time:      1712 |       1712 |                   scipy.stats._sensitivity_analysis\nimport time:      1406 |       1406 |                   scipy.stats._survival\nimport time:       247 |        247 |                     scipy.optimize._bracket\nimport time:       279 |        279 |                     scipy.optimize._chandrupatla\nimport time:       348 |        348 |                     scipy.stats._probability_distribution\nimport time:      3345 |       4217 |                   scipy.stats._distribution_infrastructure\nimport time:     17682 |      17682 |                   scipy.stats._new_distributions\nimport time:       224 |        224 |                             scipy.ndimage._ni_support\nimport time:       358 |        358 |                             scipy.ndimage._nd_image\nimport time:       256 |        256 |                             scipy.ndimage._ni_docstrings\nimport time:       229 |        229 |                             scipy.ndimage._rank_filter_1d\nimport time:      1961 |       3025 |                           scipy.ndimage._filters\nimport time:       235 |        235 |                           scipy.ndimage._fourier\nimport time:       725 |        725 |                           scipy.ndimage._interpolation\nimport time:       378 |        378 |                             scipy.ndimage._ni_label\nimport time:       381 |        381 |                             scipy.ndimage._morphology\nimport time:       479 |       1236 |                           scipy.ndimage._measurements\nimport time:       254 |       5473 |                         scipy.ndimage._ndimage_api\nimport time:       247 |        247 |                         scipy.ndimage._delegators\nimport time:       319 |       6038 |                       scipy.ndimage._support_alternative_backends\nimport time:       157 |        157 |                       scipy.ndimage.filters\nimport time:       130 |        130 |                       scipy.ndimage.fourier\nimport time:       100 |        100 |                       scipy.ndimage.interpolation\nimport time:        86 |         86 |                       scipy.ndimage.measurements\nimport time:        93 |         93 |                       scipy.ndimage.morphology\nimport time:       282 |       6882 |                     scipy.ndimage\nimport time:       812 |       7694 |                   scipy.stats._mgc\nimport time:       911 |        911 |                   scipy.stats._correlation\nimport time:       246 |        246 |                   scipy.stats._quantile\nimport time:       111 |        111 |                   scipy.stats.biasedurn\nimport time:        91 |         91 |                   scipy.stats.kde\nimport time:        99 |         99 |                   scipy.stats.morestats\nimport time:       116 |        116 |                   scipy.stats.mstats_basic\nimport time:        89 |         89 |                   scipy.stats.mstats_extras\nimport time:        86 |         86 |                   scipy.stats.mvn\nimport time:       103 |        103 |                   scipy.stats.stats\nimport time:       955 |     244469 |                 scipy.stats\nimport time:       226 |        226 |                     sklearn._config\nimport time:       273 |        273 |                       sklearn.__check_build._check_build\nimport time:       130 |        402 |                     sklearn.__check_build\nimport time:       131 |        131 |                     sklearn._distributor_init\nimport time:       298 |        298 |                       sklearn.exceptions\nimport time:       143 |        143 |                               sklearn.utils._bunch\nimport time:       799 |        941 |                             sklearn.utils._metadata_requests\nimport time:       331 |       1272 |                           sklearn.utils.metadata_routing\nimport time:       299 |        299 |                                   sklearn.externals\nimport time:       320 |        320 |                                       sklearn.externals.array_api_compat.common._helpers\nimport time:       158 |        478 |                                     sklearn.externals.array_api_compat.common\nimport time:       377 |        855 |                                   sklearn.externals.array_api_compat\nimport time:        89 |         89 |                                           sklearn.externals.array_api_extra._lib._utils\nimport time:       135 |        135 |                                             sklearn.externals._array_api_compat_vendor\nimport time:       132 |        267 |                                           sklearn.externals.array_api_extra._lib._utils._compat\nimport time:       406 |        762 |                                         sklearn.externals.array_api_extra._lib._backends\nimport time:       101 |        862 |                                       sklearn.externals.array_api_extra._lib\nimport time:        95 |         95 |                                             sklearn.externals.array_api_extra._lib._utils._typing\nimport time:       228 |        322 |                                           sklearn.externals.array_api_extra._lib._utils._helpers\nimport time:       445 |        767 |                                         sklearn.externals.array_api_extra._lib._at\nimport time:       338 |       1105 |                                       sklearn.externals.array_api_extra._lib._funcs\nimport time:       226 |       2192 |                                     sklearn.externals.array_api_extra._delegation\nimport time:       218 |        218 |                                     sklearn.externals.array_api_extra._lib._lazy\nimport time:       331 |       2741 |                                   sklearn.externals.array_api_extra\nimport time:       876 |        876 |                                       sklearn.externals.array_api_compat.common._aliases\nimport time:       171 |        171 |                                       sklearn.externals.array_api_compat._internal\nimport time:       217 |        217 |                                       sklearn.externals.array_api_compat.numpy._info\nimport time:      1610 |       2872 |                                     sklearn.externals.array_api_compat.numpy._aliases\nimport time:       945 |        945 |                                       sklearn.externals.array_api_compat.common._linalg\nimport time:       684 |       1628 |                                     sklearn.externals.array_api_compat.numpy.linalg\nimport time:       145 |        145 |                                       sklearn.externals.array_api_compat.common._fft\nimport time:       522 |        667 |                                     sklearn.externals.array_api_compat.numpy.fft\nimport time:       646 |       5812 |                                   sklearn.externals.array_api_compat.numpy\nimport time:       116 |        116 |                                       sklearn.externals._packaging\nimport time:       164 |        164 |                                       sklearn.externals._packaging._structures\nimport time:      1140 |       1419 |                                     sklearn.externals._packaging.version\nimport time:       303 |        303 |                                         ctypes.util\nimport time:      1059 |       1362 |                                       threadpoolctl\nimport time:       287 |       1648 |                                     sklearn.utils.parallel\nimport time:      1078 |       4144 |                                   sklearn.utils.fixes\nimport time:       593 |      14440 |                                 sklearn.utils._array_api\nimport time:       212 |        212 |                                 sklearn.utils.deprecation\nimport time:       855 |        855 |                                 sklearn.utils._isfinite\nimport time:      4669 |       4669 |                                 sklearn.utils._tags\nimport time:      7246 |      27421 |                               sklearn.utils.validation\nimport time:       765 |      28185 |                             sklearn.utils._param_validation\nimport time:       218 |      28403 |                           sklearn.utils._chunking\nimport time:       652 |        652 |                               sklearn.utils.sparsefuncs_fast\nimport time:       458 |       1109 |                             sklearn.utils.extmath\nimport time:       491 |       1600 |                           sklearn.utils._indexing\nimport time:        97 |         97 |                             sklearn.utils._missing\nimport time:       199 |        295 |                           sklearn.utils._mask\nimport time:       101 |        101 |                             sklearn.utils._repr_html\nimport time:       255 |        356 |                           sklearn.utils._repr_html.base\nimport time:      1210 |       1210 |                               html.entities\nimport time:       466 |       1676 |                             html\nimport time:       727 |       2403 |                           sklearn.utils._repr_html.estimator\nimport time:       293 |        293 |                           sklearn.utils.class_weight\nimport time:       198 |        198 |                           sklearn.utils.discovery\nimport time:       498 |        498 |                           sklearn.utils.murmurhash\nimport time:       326 |      35639 |                         sklearn.utils\nimport time:        35 |      35673 |                       sklearn.utils._metadata_requests\nimport time:       212 |        212 |                       sklearn.utils._repr_html.params\nimport time:       158 |        158 |                         sklearn.utils._available_if\nimport time:       455 |        613 |                       sklearn.utils._set_output\nimport time:       728 |      37523 |                     sklearn.base\nimport time:       494 |        494 |                       sklearn.utils._openmp_helpers\nimport time:       263 |        756 |                     sklearn.utils._show_versions\nimport time:       125 |        125 |                     sklearn._built_with_meson\nimport time:       395 |      39555 |                   sklearn\nimport time:       196 |        196 |                           sklearn.metrics.cluster._bicluster\nimport time:       156 |        156 |                               sklearn.utils._unique\nimport time:       231 |        387 |                             sklearn.utils.multiclass\nimport time:       450 |        450 |                             sklearn.metrics.cluster._expected_mutual_info_fast\nimport time:       357 |       1193 |                           sklearn.metrics.cluster._supervised\nimport time:       394 |        394 |                                 sklearn.utils.sparsefuncs\nimport time:       638 |        638 |                                   sklearn.utils._encode\nimport time:      1223 |       1861 |                                 sklearn.preprocessing._encoders\nimport time:      5116 |       7370 |                               sklearn.preprocessing._data\nimport time:       286 |        286 |                                 sklearn.utils.stats\nimport time:       894 |       1179 |                               sklearn.preprocessing._discretization\nimport time:       459 |        459 |                                 sklearn.utils.metaestimators\nimport time:       801 |       1260 |                               sklearn.preprocessing._function_transformer\nimport time:      1830 |       1830 |                               sklearn.preprocessing._label\nimport time:       778 |        778 |                                 sklearn.preprocessing._csr_polynomial_expansion\nimport time:      1469 |       2246 |                               sklearn.preprocessing._polynomial\nimport time:       788 |        788 |                                 sklearn.preprocessing._target_encoder_fast\nimport time:       979 |       1766 |                               sklearn.preprocessing._target_encoder\nimport time:       273 |      15921 |                             sklearn.preprocessing\nimport time:      1264 |       1264 |                                   sklearn.metrics._dist_metrics\nimport time:      1352 |       1352 |                                       sklearn.metrics._pairwise_distances_reduction._datasets_pair\nimport time:      1405 |       1405 |                                       sklearn.utils._cython_blas\nimport time:       804 |       3560 |                                     sklearn.metrics._pairwise_distances_reduction._base\nimport time:       811 |        811 |                                     sklearn.metrics._pairwise_distances_reduction._middle_term_computer\nimport time:       392 |        392 |                                     sklearn.utils._heap\nimport time:       315 |        315 |                                     sklearn.utils._sorting\nimport time:      1039 |       6115 |                                   sklearn.metrics._pairwise_distances_reduction._argkmin\nimport time:       731 |        731 |                                   sklearn.metrics._pairwise_distances_reduction._argkmin_classmode\nimport time:       638 |        638 |                                     sklearn.utils._vector_sentinel\nimport time:      1173 |       1811 |                                   sklearn.metrics._pairwise_distances_reduction._radius_neighbors\nimport time:       774 |        774 |                                   sklearn.metrics._pairwise_distances_reduction._radius_neighbors_classmode\nimport time:       924 |      11617 |                                 sklearn.metrics._pairwise_distances_reduction._dispatcher\nimport time:       302 |      11918 |                               sklearn.metrics._pairwise_distances_reduction\nimport time:       773 |        773 |                               sklearn.metrics._pairwise_fast\nimport time:      1272 |      13962 |                             sklearn.metrics.pairwise\nimport time:       335 |      30218 |                           sklearn.metrics.cluster._unsupervised\nimport time:       336 |      31941 |                         sklearn.metrics.cluster\nimport time:      1780 |       1780 |                         sklearn.metrics._classification\nimport time:       330 |        330 |                           sklearn.metrics._plot\nimport time:       203 |        203 |                           sklearn.utils._optional_dependencies\nimport time:       233 |        233 |                             sklearn.utils._response\nimport time:       601 |        833 |                           sklearn.utils._plotting\nimport time:       586 |       1950 |                         sklearn.metrics._plot.confusion_matrix\nimport time:       238 |        238 |                             sklearn.metrics._base\nimport time:       978 |       1216 |                           sklearn.metrics._ranking\nimport time:       325 |       1540 |                         sklearn.metrics._plot.det_curve\nimport time:       279 |        279 |                         sklearn.metrics._plot.precision_recall_curve\nimport time:       301 |        301 |                         sklearn.metrics._plot.regression\nimport time:       373 |        373 |                         sklearn.metrics._plot.roc_curve\nimport time:       688 |        688 |                         sklearn.metrics._regression\nimport time:      6105 |       6105 |                         sklearn.metrics._scorer\nimport time:       466 |      45418 |                       sklearn.metrics\nimport time:        47 |      45465 |                     sklearn.metrics.pairwise\nimport time:      1878 |      47342 |                   sklearn.gaussian_process.kernels\nimport time:      1772 |       1772 |                     sklearn.multiclass\nimport time:       335 |        335 |                     sklearn.utils.optimize\nimport time:      1407 |       3513 |                   sklearn.gaussian_process._gpc\nimport time:       542 |        542 |                   sklearn.gaussian_process._gpr\nimport time:       226 |      91176 |                 sklearn.gaussian_process\nimport time:       292 |        292 |                 bayes_opt.exception\nimport time:       276 |        276 |                         colorama.ansi\nimport time:        96 |         96 |                           msvcrt\nimport time:       178 |        178 |                           colorama.win32\nimport time:       338 |        612 |                         colorama.winterm\nimport time:       559 |       1446 |                       colorama.ansitowin32\nimport time:       339 |       1785 |                     colorama.initialise\nimport time:       208 |       1992 |                   colorama\nimport time:        90 |         90 |                       bayes_opt.util\nimport time:       410 |        500 |                     bayes_opt.parameter\nimport time:       288 |        787 |                   bayes_opt.constraint\nimport time:       382 |       3161 |                 bayes_opt.target_space\nimport time:       769 |     339865 |               bayes_opt.acquisition\nimport time:       248 |        248 |                 bayes_opt.domain_reduction\nimport time:       261 |        261 |                 bayes_opt.logger\nimport time:       415 |        923 |               bayes_opt.bayesian_optimization\nimport time:      1921 |     342707 |             bayes_opt\nimport time:       569 |     479464 |           optimagic.config\nimport time:       242 |        242 |           optimagic.decorators\nimport time:       452 |     619703 |         optimagic.batch_evaluators\nimport time:       127 |        127 |           optimagic.differentiation\nimport time:       350 |        350 |           optimagic.differentiation.finite_differences\nimport time:       347 |        347 |           optimagic.differentiation.generate_steps\nimport time:       180 |        180 |           optimagic.differentiation.richardson_extrapolation\nimport time:       637 |        637 |           optimagic.parameters.block_trees\nimport time:      1881 |       3520 |         optimagic.differentiation.derivatives\nimport time:      1544 |       1544 |         optimagic.differentiation.numdiff_options\nimport time:       192 |        192 |           optimagic.parameters.process_selectors\nimport time:       964 |        964 |             optimagic.parameters.scaling\nimport time:       244 |        244 |               optimagic.parameters.kernel_transformations\nimport time:       155 |        155 |                 optimagic.parameters.check_constraints\nimport time:       266 |        266 |                 optimagic.parameters.consolidate_constraints\nimport time:       199 |        619 |               optimagic.parameters.process_constraints\nimport time:      1983 |       2845 |             optimagic.parameters.space_conversion\nimport time:      1009 |       4816 |           optimagic.parameters.scale_conversion\nimport time:       622 |        622 |           optimagic.parameters.tree_conversion\nimport time:      1109 |       6738 |         optimagic.parameters.conversion\nimport time:      1739 |     633242 |       optimagic.optimization.internal_optimization_problem\nimport time:       258 |        258 |       optimagic.type_conversion\nimport time:      3672 |    1030396 |     optimagic.optimization.algorithm\nimport time:       601 |    1030997 |   optimagic.mark\nimport time:       193 |        193 |       optimagic.optimizers\nimport time:      2196 |       2389 |     optimagic.optimizers.bayesian_optimizer\nimport time:      1019 |       1019 |     optimagic.optimizers.bhhh\nimport time:      1931 |       1931 |     optimagic.optimizers.fides\nimport time:      1147 |       1147 |     optimagic.optimizers.iminuit_migrad\nimport time:       314 |        314 |         optimagic.parameters.nonlinear_constraints\nimport time:     21740 |      22054 |       optimagic.optimizers.scipy_optimizers\nimport time:     13044 |      35097 |     optimagic.optimizers.ipopt\nimport time:      5284 |       5284 |     optimagic.optimizers.nag_optimizers\nimport time:      1572 |       1572 |     optimagic.optimizers.neldermead\nimport time:      1540 |       1540 |     optimagic.optimizers.nevergrad_optimizers\nimport time:     15414 |      15414 |     optimagic.optimizers.nlopt_optimizers\nimport time:       114 |        114 |         optimagic.optimizers._pounders\nimport time:       119 |        119 |           optimagic.optimizers._pounders._conjugate_gradient\nimport time:       192 |        192 |           optimagic.optimizers._pounders._steihaug_toint\nimport time:       184 |        184 |           optimagic.optimizers._pounders._trsbox\nimport time:       533 |       1027 |         optimagic.optimizers._pounders.bntr\nimport time:       605 |        605 |         optimagic.optimizers._pounders.gqtpar\nimport time:      1223 |       2967 |       optimagic.optimizers._pounders.pounders_auxiliary\nimport time:       294 |        294 |       optimagic.optimizers._pounders.pounders_history\nimport time:      2341 |       5601 |     optimagic.optimizers.pounders\nimport time:        90 |         90 |       pygmo\nimport time:     25284 |      25374 |     optimagic.optimizers.pygmo_optimizers\nimport time:       106 |        106 |       petsc4py\nimport time:      1297 |       1403 |     optimagic.optimizers.tao_optimizers\nimport time:      6077 |       6077 |     optimagic.optimizers.tranquilo\nimport time:    193666 |     297507 |   optimagic.algorithms\nimport time:       192 |        192 |     optimagic.benchmarking\nimport time:       150 |        150 |     optimagic.benchmarking.process_benchmark_results\nimport time:        88 |         88 |       optimagic.visualization\nimport time:       275 |        363 |     optimagic.visualization.profile_plot\nimport time:       255 |        958 |   optimagic.benchmarking.benchmark_reports\nimport time:      3220 |       3220 |       optimagic.benchmarking.more_wild\nimport time:      1764 |       4984 |     optimagic.benchmarking.cartis_roberts\nimport time:       205 |        205 |     optimagic.benchmarking.noise_distributions\nimport time:       136 |        136 |       optimagic.shared\nimport time:       260 |        396 |     optimagic.shared.process_user_function\nimport time:       300 |       5883 |   optimagic.benchmarking.get_benchmark_problems\nimport time:      3149 |       3149 |         optimagic.optimization.multistart_options\nimport time:       212 |        212 |         optimagic.optimization.scipy_aliases\nimport time:      1891 |       5251 |       optimagic.optimization.create_optimization_problem\nimport time:       405 |        405 |       optimagic.optimization.error_penalty\nimport time:       121 |        121 |         optimagic.optimization.optimization_logging\nimport time:       330 |        450 |       optimagic.optimization.multistart\nimport time:        96 |         96 |         optimagic.shared.compat\nimport time:      1929 |       2025 |       optimagic.optimization.optimize_result\nimport time:       156 |        156 |         optimagic.optimization.convergence_report\nimport time:       917 |       1073 |       optimagic.optimization.process_results\nimport time:       430 |       9631 |     optimagic.optimization.optimize\nimport time:       176 |       9806 |   optimagic.benchmarking.run_benchmark\nimport time:       549 |        549 |   optimagic.logging.read_log\nimport time:       159 |        159 |   optimagic.parameters.constraint_tools\nimport time:       255 |        255 |     plotly.graph_objects\nimport time:       139 |        139 |       plotly.subplots\nimport time:       258 |        396 |     optimagic.visualization.plotting_utilities\nimport time:       178 |        828 |   optimagic.visualization.convergence_plot\nimport time:       210 |        210 |   optimagic.visualization.history_plots\nimport time:       171 |        171 |   optimagic.visualization.slice_plot\nimport time:       132 |        132 |   optimagic._version\nimport time:       454 |    1780118 | optimagic\n"}}}}