Optimagic Import Time Measurement
========================================
Timestamp: 2025-07-19 00:59:06
Python version: 3.13.5 | packaged by Anaconda, Inc. | (main, Jun 12 2025, 16:09:02) [GCC 11.2.0]

optimagic: 1.3004s (±0.1264s)
  Times: [1.37572740700125, 1.484249754999837, 1.1810108959998615, 1.3212147939993883, 1.1398330590000114]
optimagic.optimizers: 1.3074s (±0.0482s)
  Times: [1.3751058210000338, 1.3272133760001452, 1.275509630999295, 1.2343230620008399, 1.3248620059985114]
optimagic.optimizers.pygmo_optimizers: 1.3212s (±0.0178s)
  Times: [1.3337631419999525, 1.3262148529993283, 1.344728785999905, 1.3012076269988029, 1.3002151300006517]
optimagic.optimizers.ipopt: 1.3444s (±0.0317s)
  Times: [1.3438762900004804, 1.3685084499993536, 1.3294692609997583, 1.2943092140012595, 1.3858062449999125]
optimagic.optimizers.fides: 1.3368s (±0.0315s)
  Times: [1.3846323879988631, 1.3138218370004324, 1.3011418400001276, 1.3623905860004015, 1.3219721259993094]
optimagic.optimizers.tao_optimizers: 1.3343s (±0.0257s)
  Times: [1.3634731539987115, 1.321911614999408, 1.319661110999732, 1.365514974000689, 1.301091440998789]
optimagic.optimizers.nevergrad_optimizers: 1.4488s (±0.0665s)
  Times: [1.401241134999509, 1.3873322470008134, 1.3957261409996136, 1.537848671001484, 1.5218451860000641]
optimagic.optimizers.nag_optimizers: 1.3372s (±0.0155s)
  Times: [1.3322021000003588, 1.3418258739984594, 1.357314087999839, 1.3106638369990833, 1.3439285780004866]
optimagic.optimizers.iminuit_migrad: 1.3188s (±0.0154s)
  Times: [1.2880759709987615, 1.3238238020003337, 1.3291201230003935, 1.3257285639992915, 1.3270513960014796]
optimagic.optimizers.bayesian_optimizer: 1.3650s (±0.0221s)
  Times: [1.39085942499878, 1.362165565999021, 1.3423039830013295, 1.389961195000069, 1.3397432299989305]
optimagic.optimizers.tranquilo: 1.3234s (±0.0404s)
  Times: [1.3559742519992142, 1.3830483679994359, 1.2964663809998456, 1.3094278379994648, 1.272238944999117]
