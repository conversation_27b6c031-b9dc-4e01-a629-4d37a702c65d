{"timestamp": "2025-07-19 01:37:52", "tests": {"original": {"path": "/home/<USER>/learnings/gsoc/pygad_new/optimagic", "functionality": "passed", "timing": {"times": [1.000603, 0.965906, 1.003191, 1.010897, 1.019897], "mean": 1.0000988, "min": 0.965906, "max": 1.019897, "std": 0.01837135453253242, "sample_output": "import time: self [us] | cumulative | imported package\nimport time:       135 |        135 |   _io\nimport time:        26 |         26 |   marshal\nimport time:       262 |        262 |   posix\nimport time:       413 |        834 | _frozen_importlib_external\nimport time:        56 |         56 |   time\nimport time:       167 |        222 | zipimport\nimport time:        31 |         31 |     _codecs\nimport time:       359 |        390 |   codecs\nimport time:       252 |        252 |   encodings.aliases\nimport time:       415 |       1056 | encodings\nimport time:       131 |        131 | encodings.utf_8\nimport time:        66 |         66 | _signal\nimport time:        23 |         23 |     _abc\nimport time:       107 |        130 |   abc\nimport time:       109 |        238 | io\nimport time:        38 |         38 |       _stat\nimport time:        67 |        104 |     stat\nimport time:       638 |        638 |     _collections_abc\nimport time:        41 |         41 |       errno\nimport time:        67 |         67 |       genericpath\nimport time:       118 |        225 |     posixpath\nimport time:       398 |       1363 |   os\nimport time:        92 |         92 |   _sitebuiltins\nimport time:       289 |        289 |   encodings.utf_8_sig\nimport time:       403 |        403 |   _distutils_hack\nimport time:       131 |        131 |   sitecustomize\nimport time:       601 |       2877 | site\nimport time:       119 |        119 | linecache\nimport time:        89 |         89 |   __future__\nimport time:       105 |        105 |               itertools\nimport time:       106 |        106 |               keyword\nimport time:        66 |         66 |                 _operator\nimport time:       373 |        439 |               operator\nimport time:       128 |        128 |               reprlib\nimport time:        51 |         51 |               _collections\nimport time:       664 |       1489 |             collections\nimport time:        48 |         48 |             _functools\nimport time:       388 |       1924 |           functools\nimport time:       183 |        183 |           types\nimport time:      1033 |       3139 |         enum\nimport time:        59 |         59 |           _sre\nimport time:       168 |        168 |             re._constants\nimport time:       260 |        428 |           re._parser\nimport time:       108 |        108 |           re._casefix\nimport time:       260 |        853 |         re._compiler\nimport time:       108 |        108 |         copyreg\nimport time:       378 |       4477 |       re\nimport time:       161 |        161 |           _weakrefset\nimport time:      1007 |       1168 |         weakref\nimport time:       127 |       1294 |       copy\nimport time:      1134 |       1134 |           _ast\nimport time:      1560 |       1560 |           contextlib\nimport time:      2461 |       5154 |         ast\nimport time:       129 |        129 |             _opcode\nimport time:       137 |        137 |             _opcode_metadata\nimport time:       216 |        482 |           opcode\nimport time:       744 |       1225 |         dis\nimport time:       135 |        135 |           importlib\nimport time:        55 |        189 |         importlib.machinery\nimport time:       109 |        109 |           token\nimport time:        29 |         29 |           _tokenize\nimport time:       680 |        817 |         tokenize\nimport time:      5652 |      13036 |       inspect\nimport time:       454 |      19259 |     dataclasses\nimport time:        61 |         61 |       _typing\nimport time:      1919 |       1979 |     typing\nimport time:       215 |        215 |       warnings\nimport time:       153 |        153 |       numpy.version\nimport time:        73 |         73 |       numpy._expired_attrs_2_0\nimport time:       112 |        112 |           numpy._utils._convertions\nimport time:        71 |        182 |         numpy._utils\nimport time:       334 |        515 |       numpy._globals\nimport time:        20 |         20 |         numpy._distributor_init_local\nimport time:        77 |         97 |       numpy._distributor_init\nimport time:       287 |        287 |                   _datetime\nimport time:       249 |        536 |                 datetime\nimport time:       176 |        176 |                 math\nimport time:       180 |        180 |                 numpy.exceptions\nimport time:       231 |        231 |                 numpy._core._exceptions\nimport time:       115 |        115 |                     _contextvars\nimport time:        63 |        178 |                   contextvars\nimport time:        90 |        267 |                 numpy._core.printoptions\nimport time:       246 |        246 |                 numpy.dtypes\nimport time:      9074 |      10707 |               numpy._core._multiarray_umath\nimport time:        96 |         96 |                 numpy._utils._inspect\nimport time:      3563 |       3659 |               numpy._core.overrides\nimport time:       438 |      14802 |             numpy._core.multiarray\nimport time:       172 |        172 |             numpy._core.umath\nimport time:       309 |        309 |               numbers\nimport time:       133 |        133 |               numpy._core._dtype\nimport time:        73 |         73 |               numpy._core._string_helpers\nimport time:       190 |        190 |               numpy._core._type_aliases\nimport time:       240 |        941 |             numpy._core.numerictypes\nimport time:       123 |        123 |               numpy._core._ufunc_config\nimport time:       202 |        202 |                       _struct\nimport time:        78 |        279 |                     struct\nimport time:       218 |        218 |                     _compat_pickle\nimport time:       243 |        243 |                     _pickle\nimport time:       617 |       1355 |                   pickle\nimport time:       161 |       1516 |                 numpy._core._methods\nimport time:      2689 |       4204 |               numpy._core.fromnumeric\nimport time:       152 |       4479 |             numpy._core._machar\nimport time:       282 |        282 |                 numpy._core.shape_base\nimport time:        97 |         97 |                 numpy._core._asarray\nimport time:       406 |        406 |                 numpy._core.arrayprint\nimport time:       622 |       1405 |               numpy._core.numeric\nimport time:       306 |       1710 |             numpy._core.einsumfunc\nimport time:       203 |        203 |             numpy._core.function_base\nimport time:       227 |        227 |             numpy._core.getlimits\nimport time:       120 |        120 |             numpy._core.memmap\nimport time:       230 |        230 |             numpy._core.records\nimport time:       767 |        767 |             numpy._core._add_newdocs\nimport time:       232 |        232 |             numpy._core._add_newdocs_scalars\nimport time:        80 |         80 |             numpy._core._dtype_ctypes\nimport time:       396 |        396 |                 _ctypes\nimport time:       220 |        220 |                 ctypes._endian\nimport time:       601 |       1216 |               ctypes\nimport time:       640 |       1855 |             numpy._core._internal\nimport time:       108 |        108 |             numpy._pytesttester\nimport time:       368 |      26288 |           numpy._core\nimport time:        20 |      26308 |         numpy._core._multiarray_umath\nimport time:       202 |      26509 |       numpy.__config__\nimport time:       818 |        818 |                         numpy._typing._nbit_base\nimport time:       171 |        171 |                         numpy._typing._nested_sequence\nimport time:        67 |         67 |                         numpy._typing._shape\nimport time:      1594 |       2650 |                       numpy._typing._array_like\nimport time:      1017 |       1017 |                       numpy._typing._char_codes\nimport time:      1596 |       1596 |                       numpy._typing._dtype_like\nimport time:       234 |        234 |                       numpy._typing._nbit\nimport time:       151 |        151 |                       numpy._typing._scalars\nimport time:       108 |        108 |                       numpy._typing._ufunc\nimport time:       308 |       6061 |                     numpy._typing\nimport time:       183 |        183 |                       numpy.lib._stride_tricks_impl\nimport time:       506 |        689 |                     numpy.lib._twodim_base_impl\nimport time:       166 |        166 |                       numpy.lib._array_utils_impl\nimport time:       216 |        381 |                     numpy.lib.array_utils\nimport time:       733 |        733 |                     numpy.linalg._umath_linalg\nimport time:      1773 |       9635 |                   numpy.linalg._linalg\nimport time:       144 |        144 |                   numpy.linalg.linalg\nimport time:       206 |       9985 |                 numpy.linalg\nimport time:       215 |      10199 |               numpy.matrixlib.defmatrix\nimport time:        86 |      10284 |             numpy.matrixlib\nimport time:       215 |        215 |               numpy.lib._histograms_impl\nimport time:       793 |       1007 |             numpy.lib._function_base_impl\nimport time:        79 |         79 |             numpy.lib.stride_tricks\nimport time:       357 |      11725 |           numpy.lib._index_tricks_impl\nimport time:       245 |      11970 |         numpy.lib._arraypad_impl\nimport time:       763 |        763 |         numpy.lib._arraysetops_impl\nimport time:       175 |        175 |         numpy.lib._arrayterator_impl\nimport time:       321 |        321 |         numpy.lib._nanfunctions_impl\nimport time:       113 |        113 |                   _wmi\nimport time:       585 |        697 |                 platform\nimport time:       892 |        892 |                 textwrap\nimport time:       202 |       1789 |               numpy.lib._utils_impl\nimport time:       187 |       1976 |             numpy.lib._format_impl\nimport time:        83 |       2059 |           numpy.lib.format\nimport time:       220 |        220 |           numpy.lib._datasource\nimport time:       292 |        292 |           numpy.lib._iotools\nimport time:       502 |       3072 |         numpy.lib._npyio_impl\nimport time:        84 |         84 |             numpy.lib._ufunclike_impl\nimport time:       222 |        306 |           numpy.lib._type_check_impl\nimport time:       378 |        684 |         numpy.lib._polynomial_impl\nimport time:       252 |        252 |         numpy.lib._shape_base_impl\nimport time:        91 |         91 |         numpy.lib._version\nimport time:        56 |         56 |         numpy.lib.introspect\nimport time:       104 |        104 |         numpy.lib.mixins\nimport time:        50 |         50 |         numpy.lib.npyio\nimport time:       186 |        186 |           numpy.lib._scimath_impl\nimport time:       180 |        366 |         numpy.lib.scimath\nimport time:       336 |      18234 |       numpy.lib\nimport time:       331 |        331 |       numpy._array_api_info\nimport time:      4856 |      50978 |     numpy\nimport time:       343 |        343 |         pytz.exceptions\nimport time:      1022 |       1022 |           threading\nimport time:       302 |       1324 |         pytz.lazy\nimport time:       265 |        265 |             _bisect\nimport time:       148 |        413 |           bisect\nimport time:       303 |        716 |         pytz.tzinfo\nimport time:       156 |        156 |         pytz.tzfile\nimport time:       702 |       3238 |       pytz\nimport time:       207 |        207 |         dateutil._version\nimport time:       180 |        386 |       dateutil\nimport time:       417 |        417 |           sysconfig\nimport time:       633 |        633 |           _sysconfigdata__linux_x86_64-linux-gnu\nimport time:       938 |       1987 |         pandas.compat._constants\nimport time:       222 |        222 |             _compression\nimport time:       747 |        747 |             _bz2\nimport time:       261 |       1229 |           bz2\nimport time:       354 |        354 |             _lzma\nimport time:       258 |        612 |           lzma\nimport time:       195 |       2035 |         pandas.compat.compressors\nimport time:       119 |        119 |             pandas.util\nimport time:      3294 |       3412 |           pandas.util.version\nimport time:       354 |       3766 |         pandas.compat.numpy\nimport time:        55 |         55 |           pyarrow\nimport time:       105 |        160 |         pandas.compat.pyarrow\nimport time:       275 |       8221 |       pandas.compat\nimport time:      1244 |       1244 |                   numpy.random._common\nimport time:       261 |        261 |                       binascii\nimport time:       323 |        583 |                     base64\nimport time:      2249 |       2249 |                       _hashlib\nimport time:       225 |        225 |                         _blake2\nimport time:       286 |        511 |                       hashlib\nimport time:       262 |       3021 |                     hmac\nimport time:       203 |        203 |                       _random\nimport time:       499 |        701 |                     random\nimport time:       207 |       4511 |                   secrets\nimport time:       612 |       6366 |                 numpy.random.bit_generator\nimport time:       391 |       6756 |               numpy.random._bounded_integers\nimport time:       627 |        627 |                   numpy.random._pcg64\nimport time:       343 |        343 |                   numpy.random._mt19937\nimport time:      1003 |       1971 |                 numpy.random._generator\nimport time:       295 |        295 |                 numpy.random._philox\nimport time:       278 |        278 |                 numpy.random._sfc64\nimport time:      1311 |       1311 |                 numpy.random.mtrand\nimport time:       213 |       4067 |               numpy.random._pickle\nimport time:       238 |      11061 |             numpy.random\nimport time:      4041 |      15101 |           pandas._typing\nimport time:       202 |        202 |           pandas.util._exceptions\nimport time:       875 |      16176 |         pandas._config.config\nimport time:       271 |        271 |         pandas._config.dates\nimport time:        96 |         96 |             _locale\nimport time:       964 |       1059 |           locale\nimport time:       236 |       1295 |         pandas._config.display\nimport time:       182 |      17922 |       pandas._config\nimport time:       127 |        127 |         pandas.core\nimport time:      1136 |       1262 |       pandas.core.config_init\nimport time:       357 |        357 |           pandas._libs.pandas_parser\nimport time:       193 |        193 |           pandas._libs.pandas_datetime\nimport time:       264 |        264 |                       pandas._libs.tslibs.ccalendar\nimport time:       416 |        416 |                       pandas._libs.tslibs.np_datetime\nimport time:      1249 |       1928 |                     pandas._libs.tslibs.dtypes\nimport time:       232 |        232 |                       pandas._libs.tslibs.base\nimport time:       524 |        524 |                           pandas._libs.tslibs.nattype\nimport time:       143 |        143 |                               pandas.compat._optional\nimport time:       344 |        344 |                                 zoneinfo._tzpath\nimport time:       158 |        158 |                                 zoneinfo._common\nimport time:       173 |        173 |                                 _zoneinfo\nimport time:       384 |       1057 |                               zoneinfo\nimport time:       226 |        226 |                                       importlib._abc\nimport time:       155 |        381 |                                     importlib.util\nimport time:       552 |        932 |                                   six\nimport time:        48 |         48 |                                   six.moves\nimport time:       164 |        164 |                                   dateutil.tz._common\nimport time:       114 |        114 |                                   dateutil.tz._factories\nimport time:        21 |         21 |                                     six.moves.winreg\nimport time:       157 |        177 |                                   dateutil.tz.win\nimport time:       570 |       2002 |                                 dateutil.tz.tz\nimport time:       113 |       2114 |                               dateutil.tz\nimport time:       483 |       3795 |                             pandas._libs.tslibs.timezones\nimport time:       792 |        792 |                                 calendar\nimport time:       778 |       1569 |                               _strptime\nimport time:       373 |        373 |                                   signal\nimport time:       175 |        175 |                                   fcntl\nimport time:        52 |         52 |                                   msvcrt\nimport time:        92 |         92 |                                   _posixsubprocess\nimport time:       118 |        118 |                                   select\nimport time:       402 |        402 |                                   selectors\nimport time:       513 |       1722 |                                 subprocess\nimport time:       125 |       1846 |                               pandas._config.localization\nimport time:       308 |       3723 |                             pandas._libs.tslibs.fields\nimport time:       750 |       8267 |                           pandas._libs.tslibs.timedeltas\nimport time:       301 |        301 |                           pandas._libs.tslibs.tzconversion\nimport time:      1057 |      10148 |                         pandas._libs.tslibs.timestamps\nimport time:       190 |        190 |                         pandas._libs.properties\nimport time:      1509 |      11845 |                       pandas._libs.tslibs.offsets\nimport time:       518 |        518 |                           _decimal\nimport time:       103 |        621 |                         decimal\nimport time:        64 |         64 |                               _string\nimport time:       429 |        492 |                             string\nimport time:        78 |         78 |                             dateutil._common\nimport time:       949 |       1518 |                           dateutil.parser._parser\nimport time:       243 |        243 |                           dateutil.parser.isoparser\nimport time:       196 |       1956 |                         dateutil.parser\nimport time:       646 |        646 |                         pandas._libs.tslibs.strptime\nimport time:       464 |       3685 |                       pandas._libs.tslibs.parsing\nimport time:       578 |      16339 |                     pandas._libs.tslibs.conversion\nimport time:       426 |        426 |                     pandas._libs.tslibs.period\nimport time:       255 |        255 |                     pandas._libs.tslibs.vectorized\nimport time:       258 |      19204 |                   pandas._libs.tslibs\nimport time:        29 |      19232 |                 pandas._libs.tslibs.nattype\nimport time:       131 |        131 |                 pandas._libs.ops_dispatch\nimport time:      1026 |      20389 |               pandas._libs.missing\nimport time:      1744 |      22132 |             pandas._libs.hashtable\nimport time:      2623 |       2623 |             pandas._libs.algos\nimport time:       995 |      25749 |           pandas._libs.interval\nimport time:       169 |      26467 |         pandas._libs\nimport time:       110 |        110 |           pandas.core.dtypes\nimport time:        60 |         60 |             pyarrow\nimport time:       689 |        748 |           pandas._libs.lib\nimport time:       438 |        438 |           pandas.errors\nimport time:       377 |        377 |             pandas.core.dtypes.generic\nimport time:       239 |        616 |           pandas.core.dtypes.base\nimport time:       124 |        124 |           pandas.core.dtypes.inference\nimport time:      1004 |       3037 |         pandas.core.dtypes.dtypes\nimport time:       206 |        206 |           pandas.core.dtypes.common\nimport time:       259 |        464 |         pandas.core.dtypes.missing\nimport time:       148 |        148 |           pandas.util._decorators\nimport time:        75 |         75 |               pandas.io\nimport time:       145 |        219 |             pandas.io._util\nimport time:       369 |        587 |           pandas.core.dtypes.cast\nimport time:        96 |         96 |             pandas.core.dtypes.astype\nimport time:       118 |        214 |           pandas.core.dtypes.concat\nimport time:        57 |         57 |             pandas.core.array_algos\nimport time:      2879 |       2879 |                 numpy.ma.core\nimport time:       667 |        667 |                 numpy.ma.extras\nimport time:       137 |       3682 |               numpy.ma\nimport time:       188 |        188 |               pandas.core.common\nimport time:       286 |       4155 |             pandas.core.construction\nimport time:       202 |       4413 |           pandas.core.array_algos.take\nimport time:       123 |        123 |             pandas.core.indexers.utils\nimport time:        91 |        213 |           pandas.core.indexers\nimport time:       402 |       5974 |         pandas.core.algorithms\nimport time:       175 |        175 |             pandas.core.arrays.arrow.accessors\nimport time:       169 |        169 |               unicodedata\nimport time:       136 |        136 |               pandas.util._validators\nimport time:       230 |        230 |               pandas.core.missing\nimport time:       242 |        242 |                   pandas._libs.ops\nimport time:        83 |         83 |                   pandas.core.roperator\nimport time:        58 |         58 |                   pandas.core.computation\nimport time:       110 |        110 |                     pandas.core.computation.check\nimport time:       178 |        287 |                   pandas.core.computation.expressions\nimport time:        67 |         67 |                   pandas.core.ops.missing\nimport time:        55 |         55 |                   pandas.core.ops.dispatch\nimport time:        55 |         55 |                   pandas.core.ops.invalid\nimport time:       219 |       1062 |                 pandas.core.ops.array_ops\nimport time:        63 |         63 |                 pandas.core.ops.common\nimport time:       111 |        111 |                 pandas.core.ops.docstrings\nimport time:        70 |         70 |                 pandas.core.ops.mask_ops\nimport time:       107 |       1411 |               pandas.core.ops\nimport time:       186 |        186 |               pandas.core.arraylike\nimport time:       164 |        164 |               pandas.core.arrays._arrow_string_mixins\nimport time:       134 |        134 |               pandas.core.arrays._utils\nimport time:       170 |        170 |                 pandas.compat.numpy.function\nimport time:        92 |         92 |                 pandas.core.array_algos.quantile\nimport time:       136 |        136 |                 pandas.core.sorting\nimport time:       642 |       1039 |               pandas.core.arrays.base\nimport time:       577 |        577 |                 pandas.core.nanops\nimport time:       110 |        110 |                 pandas.core.array_algos.masked_accumulations\nimport time:        81 |         81 |                 pandas.core.array_algos.masked_reductions\nimport time:        58 |         58 |                   pandas.core.util\nimport time:       215 |        215 |                   pandas._libs.hashing\nimport time:       156 |        427 |                 pandas.core.util.hashing\nimport time:       614 |       1807 |               pandas.core.arrays.masked\nimport time:       177 |        177 |                 pandas._libs.arrays\nimport time:       150 |        150 |                   pandas.core.arrays.numeric\nimport time:       182 |        331 |                 pandas.core.arrays.floating\nimport time:       208 |        208 |                 pandas.core.arrays.integer\nimport time:        79 |         79 |                     pandas.core.array_algos.transforms\nimport time:       412 |        491 |                   pandas.core.arrays._mixins\nimport time:        70 |         70 |                     pandas.core.strings\nimport time:       156 |        156 |                     pandas.core.strings.base\nimport time:       321 |        546 |                   pandas.core.strings.object_array\nimport time:       213 |       1248 |                 pandas.core.arrays.numpy_\nimport time:        72 |         72 |                 pandas.io.formats\nimport time:       124 |        124 |                       fnmatch\nimport time:       169 |        169 |                       zlib\nimport time:       476 |        768 |                     shutil\nimport time:        96 |        864 |                   pandas.io.formats.console\nimport time:       248 |       1111 |                 pandas.io.formats.printing\nimport time:       413 |       3557 |               pandas.core.arrays.string_\nimport time:        89 |         89 |                 pandas.tseries\nimport time:       256 |        344 |               pandas.tseries.frequencies\nimport time:      1054 |      10225 |             pandas.core.arrays.arrow.array\nimport time:       105 |      10504 |           pandas.core.arrays.arrow\nimport time:       191 |        191 |           pandas.core.arrays.boolean\nimport time:       146 |        146 |               _csv\nimport time:       364 |        510 |             csv\nimport time:       218 |        218 |             pandas.core.accessor\nimport time:       462 |        462 |             pandas.core.base\nimport time:       767 |       1954 |           pandas.core.arrays.categorical\nimport time:       404 |        404 |             pandas._libs.tslib\nimport time:        85 |         85 |               pandas.core.array_algos.datetimelike_accumulations\nimport time:       810 |        895 |             pandas.core.arrays.datetimelike\nimport time:       264 |        264 |             pandas.core.arrays._ranges\nimport time:        90 |         90 |             pandas.tseries.offsets\nimport time:       507 |       2159 |           pandas.core.arrays.datetimes\nimport time:       329 |        329 |             pandas.core.arrays.timedeltas\nimport time:       856 |       1184 |           pandas.core.arrays.interval\nimport time:       377 |        377 |           pandas.core.arrays.period\nimport time:       305 |        305 |                 pandas._libs.sparse\nimport time:       578 |        883 |               pandas.core.arrays.sparse.array\nimport time:       194 |       1077 |             pandas.core.arrays.sparse.accessor\nimport time:        89 |       1165 |           pandas.core.arrays.sparse\nimport time:       282 |        282 |           pandas.core.arrays.string_arrow\nimport time:       152 |      17964 |         pandas.core.arrays\nimport time:       103 |        103 |         pandas.core.flags\nimport time:       305 |        305 |               pandas._libs.internals\nimport time:        82 |         82 |                 pandas.core._numba\nimport time:       231 |        313 |               pandas.core._numba.executor\nimport time:       550 |       1166 |             pandas.core.apply\nimport time:        64 |         64 |                 gc\nimport time:       133 |        133 |                       _json\nimport time:       241 |        373 |                     json.scanner\nimport time:       337 |        710 |                   json.decoder\nimport time:       323 |        323 |                   json.encoder\nimport time:       166 |       1197 |                 json\nimport time:       153 |        153 |                   pandas._libs.indexing\nimport time:        73 |         73 |                     pandas.core.indexes\nimport time:       544 |        544 |                       pandas._libs.index\nimport time:       215 |        215 |                       pandas._libs.writers\nimport time:       355 |        355 |                       pandas._libs.join\nimport time:       111 |        111 |                       pandas.core.array_algos.putmask\nimport time:       110 |        110 |                       pandas.core.indexes.frozen\nimport time:      1517 |       1517 |                       pandas.core.strings.accessor\nimport time:      1775 |       4622 |                     pandas.core.indexes.base\nimport time:       178 |        178 |                       pandas.core.indexes.extension\nimport time:       289 |        467 |                     pandas.core.indexes.category\nimport time:       377 |        377 |                         pandas.core.indexes.range\nimport time:        73 |         73 |                           pandas.core.tools\nimport time:       168 |        241 |                         pandas.core.tools.timedeltas\nimport time:       437 |       1053 |                       pandas.core.indexes.datetimelike\nimport time:        92 |         92 |                       pandas.core.tools.times\nimport time:       462 |       1606 |                     pandas.core.indexes.datetimes\nimport time:       840 |        840 |                       pandas.core.indexes.multi\nimport time:       221 |        221 |                       pandas.core.indexes.timedeltas\nimport time:      2235 |       3295 |                     pandas.core.indexes.interval\nimport time:       325 |        325 |                     pandas.core.indexes.period\nimport time:       247 |      10632 |                   pandas.core.indexes.api\nimport time:       690 |      11475 |                 pandas.core.indexing\nimport time:       100 |        100 |                 pandas.core.sample\nimport time:        98 |         98 |                 pandas.core.array_algos.replace\nimport time:       777 |        777 |                     pandas.core.internals.blocks\nimport time:       123 |        899 |                   pandas.core.internals.api\nimport time:       180 |        180 |                     pandas.core.internals.base\nimport time:       293 |        293 |                       pandas.core.internals.ops\nimport time:       580 |        873 |                     pandas.core.internals.managers\nimport time:       401 |       1452 |                   pandas.core.internals.array_manager\nimport time:       183 |        183 |                   pandas.core.internals.concat\nimport time:        95 |       2627 |                 pandas.core.internals\nimport time:       186 |        186 |                 pandas.core.internals.construction\nimport time:        76 |         76 |                   pandas.core.methods\nimport time:        66 |         66 |                     pandas.core.reshape\nimport time:       398 |        463 |                   pandas.core.reshape.concat\nimport time:       282 |        282 |                       gzip\nimport time:       206 |        206 |                       mmap\nimport time:       297 |        297 |                           glob\nimport time:       339 |        635 |                         pathlib._abc\nimport time:        74 |         74 |                             _winapi\nimport time:        37 |         37 |                             nt\nimport time:        28 |         28 |                             nt\nimport time:        38 |         38 |                             nt\nimport time:        29 |         29 |                             nt\nimport time:        41 |         41 |                             nt\nimport time:        42 |         42 |                             nt\nimport time:        49 |         49 |                             nt\nimport time:       199 |        533 |                           ntpath\nimport time:        42 |         42 |                           pwd\nimport time:       128 |        128 |                           grp\nimport time:       515 |       1217 |                         pathlib._local\nimport time:       110 |       1962 |                       pathlib\nimport time:       816 |        816 |                       tarfile\nimport time:        83 |         83 |                         urllib\nimport time:       848 |        848 |                         ipaddress\nimport time:      8497 |       9427 |                       urllib.parse\nimport time:       161 |        161 |                           zipfile._path.glob\nimport time:       315 |        476 |                         zipfile._path\nimport time:       797 |       1273 |                       zipfile\nimport time:        93 |         93 |                       pandas.core.shared_docs\nimport time:      1402 |      15459 |                     pandas.io.common\nimport time:       595 |      16053 |                   pandas.io.formats.format\nimport time:       274 |      16865 |                 pandas.core.methods.describe\nimport time:        72 |         72 |                       pandas._libs.window\nimport time:       433 |        505 |                     pandas._libs.window.aggregations\nimport time:       224 |        224 |                       pandas._libs.window.indexers\nimport time:       364 |        587 |                     pandas.core.indexers.objects\nimport time:       108 |        108 |                     pandas.core.util.numba_\nimport time:       102 |        102 |                     pandas.core.window.common\nimport time:       125 |        125 |                     pandas.core.window.doc\nimport time:       328 |        328 |                     pandas.core.window.numba_\nimport time:       111 |        111 |                     pandas.core.window.online\nimport time:      1238 |       1238 |                     pandas.core.window.rolling\nimport time:       558 |       3658 |                   pandas.core.window.ewm\nimport time:       663 |        663 |                   pandas.core.window.expanding\nimport time:       140 |       4460 |                 pandas.core.window\nimport time:      3136 |      40205 |               pandas.core.generic\nimport time:       210 |        210 |               pandas.core.methods.selectn\nimport time:        65 |         65 |                 pandas.core.reshape.util\nimport time:        95 |         95 |                 pandas.core.tools.numeric\nimport time:       194 |        353 |               pandas.core.reshape.melt\nimport time:       275 |        275 |                 pandas._libs.reshape\nimport time:       438 |        438 |                 pandas.core.indexes.accessors\nimport time:        79 |         79 |                   pandas.arrays\nimport time:       510 |        589 |                 pandas.core.tools.datetimes\nimport time:       572 |        572 |                 pandas.io.formats.info\nimport time:       583 |        583 |                   pandas.plotting._core\nimport time:       165 |        165 |                   pandas.plotting._misc\nimport time:       106 |        852 |                 pandas.plotting\nimport time:      2622 |       5345 |               pandas.core.series\nimport time:      4411 |      50521 |             pandas.core.frame\nimport time:       741 |        741 |             pandas.core.groupby.base\nimport time:       584 |        584 |               pandas._libs.groupby\nimport time:        69 |         69 |                 pandas.core.groupby.categorical\nimport time:       282 |        350 |               pandas.core.groupby.grouper\nimport time:       626 |       1559 |             pandas.core.groupby.ops\nimport time:       104 |        104 |               pandas.core.groupby.numba_\nimport time:       180 |        180 |               pandas.core.groupby.indexing\nimport time:      1231 |       1513 |             pandas.core.groupby.groupby\nimport time:      1144 |      56642 |           pandas.core.groupby.generic\nimport time:        83 |      56724 |         pandas.core.groupby\nimport time:       195 |     110924 |       pandas.core.api\nimport time:        96 |         96 |       pandas.tseries.api\nimport time:        65 |         65 |               pandas.core.computation.common\nimport time:       157 |        222 |             pandas.core.computation.align\nimport time:       202 |        202 |                 pprint\nimport time:       161 |        363 |               pandas.core.computation.scope\nimport time:       251 |        614 |             pandas.core.computation.ops\nimport time:       190 |       1025 |           pandas.core.computation.engines\nimport time:       286 |        286 |             pandas.core.computation.parsing\nimport time:       702 |        987 |           pandas.core.computation.expr\nimport time:       179 |       2190 |         pandas.core.computation.eval\nimport time:        62 |       2251 |       pandas.core.computation.api\nimport time:       155 |        155 |         pandas.core.reshape.encoding\nimport time:       202 |        202 |             _uuid\nimport time:       667 |        869 |           uuid\nimport time:       576 |       1444 |         pandas.core.reshape.merge\nimport time:       478 |        478 |         pandas.core.reshape.pivot\nimport time:       150 |        150 |         pandas.core.reshape.tile\nimport time:       134 |       2358 |       pandas.core.reshape.api\nimport time:        97 |         97 |         pandas.api.extensions\nimport time:        50 |         50 |         pandas.api.indexers\nimport time:        45 |         45 |             pandas.core.interchange\nimport time:       645 |        689 |           pandas.core.interchange.dataframe_protocol\nimport time:       297 |        297 |             pandas.core.interchange.utils\nimport time:       185 |        482 |           pandas.core.interchange.from_dataframe\nimport time:        55 |       1226 |         pandas.api.interchange\nimport time:        86 |         86 |           pandas.core.dtypes.api\nimport time:        85 |        170 |         pandas.api.types\nimport time:       839 |        839 |           pandas.core.resample\nimport time:       179 |        179 |                 pandas._libs.json\nimport time:       140 |        140 |                 pandas.io.json._normalize\nimport time:       118 |        118 |                 pandas.io.json._table_schema\nimport time:       418 |        418 |                       pandas._libs.parsers\nimport time:       456 |        456 |                         pandas.io.parsers.base_parser\nimport time:       159 |        614 |                       pandas.io.parsers.arrow_parser_wrapper\nimport time:       155 |        155 |                       pandas.io.parsers.c_parser_wrapper\nimport time:       287 |        287 |                       pandas.io.parsers.python_parser\nimport time:      1380 |       2853 |                     pandas.io.parsers.readers\nimport time:        69 |       2922 |                   pandas.io.parsers\nimport time:        17 |       2939 |                 pandas.io.parsers.readers\nimport time:       915 |       4289 |               pandas.io.json._json\nimport time:        88 |       4377 |             pandas.io.json\nimport time:        23 |       4399 |           pandas.io.json._json\nimport time:      1020 |       1020 |           pandas.io.stata\nimport time:        95 |       6352 |         pandas.api.typing\nimport time:       124 |       8017 |       pandas.api\nimport time:       270 |        270 |               tempfile\nimport time:       376 |        646 |             pandas._testing.contexts\nimport time:       128 |        774 |           pandas._testing._io\nimport time:       132 |        132 |           pandas._testing._warnings\nimport time:       120 |        120 |               cmath\nimport time:       183 |        303 |             pandas._libs.testing\nimport time:       222 |        524 |           pandas._testing.asserters\nimport time:        79 |         79 |           pandas._testing.compat\nimport time:      1142 |       2649 |         pandas._testing\nimport time:        88 |       2737 |       pandas.testing\nimport time:       143 |        143 |       pandas.util._print_versions\nimport time:        87 |         87 |         pandas.io.clipboards\nimport time:      2634 |       2634 |             pandas.io.excel._util\nimport time:       274 |        274 |             pandas.io.excel._calamine\nimport time:       199 |        199 |             pandas.io.excel._odfreader\nimport time:       259 |        259 |             pandas.io.excel._openpyxl\nimport time:       407 |        407 |             pandas.io.excel._pyxlsb\nimport time:       208 |        208 |             pandas.io.excel._xlrd\nimport time:      1203 |       5181 |           pandas.io.excel._base\nimport time:       167 |        167 |           pandas.io.excel._odswriter\nimport time:       151 |        151 |           pandas.io.excel._xlsxwriter\nimport time:        84 |       5581 |         pandas.io.excel\nimport time:       120 |        120 |         pandas.io.feather_format\nimport time:        90 |         90 |         pandas.io.gbq\nimport time:       459 |        459 |         pandas.io.html\nimport time:        96 |         96 |         pandas.io.orc\nimport time:       295 |        295 |         pandas.io.parquet\nimport time:       142 |        142 |           pandas.compat.pickle_compat\nimport time:       169 |        310 |         pandas.io.pickle\nimport time:       363 |        363 |           pandas.core.computation.pytables\nimport time:      3053 |       3416 |         pandas.io.pytables\nimport time:       204 |        204 |           pandas.io.sas.sasreader\nimport time:       172 |        375 |         pandas.io.sas\nimport time:       122 |        122 |         pandas.io.spss\nimport time:       669 |        669 |         pandas.io.sql\nimport time:       430 |        430 |         pandas.io.xml\nimport time:       183 |      12228 |       pandas.io.api\nimport time:        96 |         96 |       pandas.util._tester\nimport time:        52 |         52 |       pandas._version_meson\nimport time:       499 |     170420 |     pandas\nimport time:       301 |        301 |       numpy._typing._add_docstring\nimport time:       101 |        402 |     numpy.typing\nimport time:       148 |        148 |         _colorize\nimport time:       825 |        972 |       traceback\nimport time:       246 |       1218 |     optimagic.exceptions\nimport time:        91 |         91 |       optimagic.optimization\nimport time:       121 |        211 |     optimagic.optimization.algo_options\nimport time:       437 |        437 |           _socket\nimport time:      1041 |       1477 |         typing_extensions\nimport time:      4753 |       6230 |       annotated_types\nimport time:      1935 |       8164 |     optimagic.typing\nimport time:      4634 |     257261 |   optimagic.constraints\nimport time:       147 |        147 |                 sqlalchemy.util.preloaded\nimport time:       110 |        110 |                     sqlalchemy.cyextension\nimport time:       437 |        437 |                     sqlalchemy.cyextension.collections\nimport time:       219 |        219 |                     sqlalchemy.cyextension.immutabledict\nimport time:       152 |        152 |                     sqlalchemy.cyextension.processors\nimport time:       148 |        148 |                     sqlalchemy.cyextension.resultproxy\nimport time:        97 |         97 |                             email\nimport time:       295 |        295 |                             importlib.metadata._meta\nimport time:       260 |        260 |                             importlib.metadata._collections\nimport time:        94 |         94 |                             importlib.metadata._functools\nimport time:        60 |         60 |                             importlib.metadata._itertools\nimport time:       292 |        292 |                                   importlib.resources.abc\nimport time:       341 |        632 |                                 importlib.resources._common\nimport time:       175 |        175 |                                 importlib.resources._functional\nimport time:       156 |        962 |                               importlib.resources\nimport time:       309 |       1271 |                             importlib.abc\nimport time:      1028 |       3103 |                           importlib.metadata\nimport time:       446 |       3548 |                         sqlalchemy.util.compat\nimport time:       867 |       4415 |                       sqlalchemy.exc\nimport time:       553 |       4967 |                     sqlalchemy.cyextension.util\nimport time:       180 |       6209 |                   sqlalchemy.util._has_cy\nimport time:       666 |        666 |                   sqlalchemy.util.typing\nimport time:       630 |       7504 |                 sqlalchemy.util._collections\nimport time:        68 |         68 |                         concurrent\nimport time:        38 |         38 |                             atexit\nimport time:      1233 |       1271 |                           logging\nimport time:       612 |       1883 |                         concurrent.futures._base\nimport time:       124 |       2074 |                       concurrent.futures\nimport time:       146 |        146 |                         _heapq\nimport time:       159 |        305 |                       heapq\nimport time:       190 |        190 |                         array\nimport time:       905 |       1095 |                       socket\nimport time:       854 |        854 |                         _ssl\nimport time:      1456 |       2309 |                       ssl\nimport time:       195 |        195 |                       asyncio.constants\nimport time:        84 |         84 |                       asyncio.coroutines\nimport time:        88 |         88 |                         asyncio.format_helpers\nimport time:        81 |         81 |                           asyncio.base_futures\nimport time:       115 |        115 |                           asyncio.exceptions\nimport time:        78 |         78 |                           asyncio.base_tasks\nimport time:       213 |        485 |                         _asyncio\nimport time:       416 |        988 |                       asyncio.events\nimport time:       155 |        155 |                       asyncio.futures\nimport time:       131 |        131 |                       asyncio.protocols\nimport time:       172 |        172 |                         asyncio.transports\nimport time:        61 |         61 |                         asyncio.log\nimport time:       636 |        868 |                       asyncio.sslproto\nimport time:        75 |         75 |                           asyncio.mixins\nimport time:       365 |        439 |                         asyncio.locks\nimport time:       165 |        165 |                           asyncio.queues\nimport time:       308 |        308 |                           asyncio.timeouts\nimport time:       314 |        787 |                         asyncio.tasks\nimport time:       162 |       1387 |                       asyncio.staggered\nimport time:       105 |        105 |                       asyncio.trsock\nimport time:       715 |      10405 |                     asyncio.base_events\nimport time:       198 |        198 |                     asyncio.runners\nimport time:       276 |        276 |                     asyncio.streams\nimport time:       168 |        168 |                     asyncio.subprocess\nimport time:       154 |        154 |                     asyncio.taskgroups\nimport time:        59 |         59 |                     asyncio.threads\nimport time:       155 |        155 |                       asyncio.base_subprocess\nimport time:      3346 |       3346 |                       asyncio.selector_events\nimport time:       500 |       4000 |                     asyncio.unix_events\nimport time:       160 |      15418 |                   asyncio\nimport time:       256 |        256 |                     greenlet._greenlet\nimport time:       143 |        398 |                   greenlet\nimport time:      1294 |       1294 |                     sqlalchemy.util.langhelpers\nimport time:       286 |       1580 |                   sqlalchemy.util._concurrency_py3k\nimport time:       164 |      17558 |                 sqlalchemy.util.concurrency\nimport time:       201 |        201 |                 sqlalchemy.util.deprecations\nimport time:      4569 |      29978 |               sqlalchemy.util\nimport time:       572 |        572 |                                 sqlalchemy.event.registry\nimport time:       133 |        704 |                               sqlalchemy.event.legacy\nimport time:       492 |       1195 |                             sqlalchemy.event.attr\nimport time:       391 |       1586 |                           sqlalchemy.event.base\nimport time:       129 |       1714 |                         sqlalchemy.event.api\nimport time:       117 |       1831 |                       sqlalchemy.event\nimport time:       243 |        243 |                             sqlalchemy.log\nimport time:      1546 |       1789 |                           sqlalchemy.pool.base\nimport time:       684 |       2472 |                         sqlalchemy.pool.events\nimport time:       268 |        268 |                           sqlalchemy.util.queue\nimport time:       349 |        617 |                         sqlalchemy.pool.impl\nimport time:       181 |       3269 |                       sqlalchemy.pool\nimport time:       486 |        486 |                             sqlalchemy.sql.roles\nimport time:       262 |        262 |                             sqlalchemy.inspection\nimport time:       980 |       1727 |                           sqlalchemy.sql._typing\nimport time:      2671 |       2671 |                             sqlalchemy.sql.visitors\nimport time:       707 |        707 |                             sqlalchemy.sql.cache_key\nimport time:       561 |        561 |                               sqlalchemy.sql.operators\nimport time:       498 |       1058 |                             sqlalchemy.sql.traversals\nimport time:      1793 |       6227 |                           sqlalchemy.sql.base\nimport time:       801 |        801 |                             sqlalchemy.sql.coercions\nimport time:       268 |        268 |                                   sqlalchemy.sql.annotation\nimport time:       925 |        925 |                                       sqlalchemy.sql.type_api\nimport time:      3930 |       4855 |                                     sqlalchemy.sql.elements\nimport time:       124 |        124 |                                     sqlalchemy.util.topological\nimport time:      1248 |       6225 |                                   sqlalchemy.sql.ddl\nimport time:       144 |        144 |                                           sqlalchemy.engine._py_processors\nimport time:        95 |        239 |                                         sqlalchemy.engine.processors\nimport time:      2099 |       2337 |                                       sqlalchemy.sql.sqltypes\nimport time:      5819 |       8156 |                                     sqlalchemy.sql.selectable\nimport time:      3188 |      11343 |                                   sqlalchemy.sql.schema\nimport time:       647 |      18482 |                                 sqlalchemy.sql.util\nimport time:      2388 |      20870 |                               sqlalchemy.sql.dml\nimport time:       784 |      21653 |                             sqlalchemy.sql.crud\nimport time:      2267 |       2267 |                             sqlalchemy.sql.functions\nimport time:      4097 |      28817 |                           sqlalchemy.sql.compiler\nimport time:        64 |         64 |                             sqlalchemy.sql._dml_constructors\nimport time:       264 |        264 |                             sqlalchemy.sql._elements_constructors\nimport time:       245 |        245 |                             sqlalchemy.sql._selectable_constructors\nimport time:       682 |        682 |                             sqlalchemy.sql.lambdas\nimport time:       259 |       1513 |                           sqlalchemy.sql.expression\nimport time:       469 |        469 |                             sqlalchemy.sql.events\nimport time:       370 |        839 |                           sqlalchemy.sql.naming\nimport time:       280 |        280 |                           sqlalchemy.sql.default_comparator\nimport time:      4306 |      43706 |                         sqlalchemy.sql\nimport time:        27 |      43732 |                       sqlalchemy.sql.compiler\nimport time:      1987 |      50818 |                     sqlalchemy.engine.interfaces\nimport time:       257 |        257 |                     sqlalchemy.engine.util\nimport time:      1668 |      52742 |                   sqlalchemy.engine.base\nimport time:      1305 |      54046 |                 sqlalchemy.engine.events\nimport time:        88 |         88 |                     sqlalchemy.dialects\nimport time:       593 |        681 |                   sqlalchemy.engine.url\nimport time:       169 |        169 |                   sqlalchemy.engine.mock\nimport time:       576 |       1425 |                 sqlalchemy.engine.create\nimport time:       993 |        993 |                     sqlalchemy.engine.row\nimport time:      1415 |       2407 |                   sqlalchemy.engine.result\nimport time:       777 |       3184 |                 sqlalchemy.engine.cursor\nimport time:      1367 |       1367 |                 sqlalchemy.engine.reflection\nimport time:       259 |      60280 |               sqlalchemy.engine\nimport time:       138 |        138 |               sqlalchemy.schema\nimport time:       116 |        116 |               sqlalchemy.types\nimport time:       146 |        146 |                 sqlalchemy.engine.characteristics\nimport time:       945 |       1091 |               sqlalchemy.engine.default\nimport time:       456 |      92057 |             sqlalchemy\nimport time:       620 |        620 |                 cloudpickle.cloudpickle\nimport time:       125 |        744 |               cloudpickle\nimport time:       297 |       1041 |             optimagic.logging.base\nimport time:        45 |         45 |                           jax\nimport time:        61 |        105 |                         pybaum.config\nimport time:       108 |        213 |                       pybaum.registry_entries\nimport time:       121 |        333 |                     pybaum.registry\nimport time:        56 |         56 |                       pybaum.equality\nimport time:        51 |         51 |                       pybaum.typecheck\nimport time:       125 |        231 |                     pybaum.tree_util\nimport time:        96 |        660 |                   pybaum\nimport time:        56 |         56 |                     optimagic.parameters\nimport time:       107 |        163 |                   optimagic.parameters.tree_registry\nimport time:       428 |        428 |                     difflib\nimport time:       234 |        234 |                         scipy.__config__\nimport time:        92 |         92 |                         scipy.version\nimport time:        21 |         21 |                           scipy._distributor_init_local\nimport time:        64 |         84 |                         scipy._distributor_init\nimport time:        47 |         47 |                             cython\nimport time:       242 |        289 |                           scipy._lib._testutils\nimport time:        56 |        345 |                         scipy._lib\nimport time:       265 |        265 |                         scipy._lib._pep440\nimport time:       372 |        372 |                           scipy._lib._ccallback_c\nimport time:       159 |        530 |                         scipy._lib._ccallback\nimport time:       237 |       1785 |                       scipy\nimport time:      1396 |       1396 |                           scipy.linalg._fblas\nimport time:        30 |         30 |                           scipy.linalg._cblas\nimport time:        17 |         17 |                           scipy.linalg._fblas_64\nimport time:       171 |       1613 |                         scipy.linalg.blas\nimport time:      1019 |       1019 |                           scipy.linalg._flapack\nimport time:        32 |         32 |                           scipy.linalg._clapack\nimport time:        17 |         17 |                           scipy.linalg._flapack_64\nimport time:       529 |       1596 |                         scipy.linalg.lapack\nimport time:       150 |       3358 |                       scipy.linalg._misc\nimport time:       275 |        275 |                         scipy._cyutility\nimport time:       878 |        878 |                         scipy.linalg.cython_lapack\nimport time:      1452 |       1452 |                                   scipy._lib.array_api_compat.common._typing\nimport time:       598 |       2050 |                                 scipy._lib.array_api_compat.common._helpers\nimport time:       166 |       2215 |                               scipy._lib.array_api_compat.common\nimport time:       163 |       2378 |                             scipy._lib.array_api_compat\nimport time:      3890 |       3890 |                                 numpy._core.strings\nimport time:       179 |       4068 |                               numpy.strings\nimport time:      2349 |       2349 |                                 numpy._core.defchararray\nimport time:       202 |       2551 |                               numpy.char\nimport time:       455 |        455 |                                 numpy.fft._helper\nimport time:       360 |        360 |                                   numpy.fft._pocketfft_umath\nimport time:       621 |        980 |                                 numpy.fft._pocketfft\nimport time:       276 |        276 |                                 numpy.fft.helper\nimport time:       288 |       1999 |                               numpy.fft\nimport time:       289 |        289 |                                   numpy.polynomial.polyutils\nimport time:       693 |        693 |                                   numpy.polynomial._polybase\nimport time:      1153 |       2134 |                                 numpy.polynomial.chebyshev\nimport time:       899 |        899 |                                 numpy.polynomial.hermite\nimport time:       817 |        817 |                                 numpy.polynomial.hermite_e\nimport time:       814 |        814 |                                 numpy.polynomial.laguerre\nimport time:       792 |        792 |                                 numpy.polynomial.legendre\nimport time:       878 |        878 |                                 numpy.polynomial.polynomial\nimport time:       359 |       6690 |                               numpy.polynomial\nimport time:       537 |        537 |                                 numpy.core._utils\nimport time:       199 |        735 |                               numpy.core\nimport time:      1173 |       1173 |                                 numpy.ctypeslib._ctypeslib\nimport time:       192 |       1365 |                               numpy.ctypeslib\nimport time:       165 |        165 |                               numpy.rec\nimport time:       474 |        474 |                                     unittest.util\nimport time:       569 |       1043 |                                   unittest.result\nimport time:       939 |        939 |                                   unittest.case\nimport time:       377 |        377 |                                   unittest.suite\nimport time:       903 |        903 |                                   unittest.loader\nimport time:       801 |        801 |                                       gettext\nimport time:      1075 |       1876 |                                     argparse\nimport time:       147 |        147 |                                       unittest.signals\nimport time:       271 |        417 |                                     unittest.runner\nimport time:       314 |       2605 |                                   unittest.main\nimport time:       760 |       6625 |                                 unittest\nimport time:       375 |        375 |                                 numpy.testing._private\nimport time:       298 |        298 |                                 numpy.testing.overrides\nimport time:       367 |        367 |                                 numpy.testing._private.extbuild\nimport time:      6641 |       6641 |                                 numpy.testing._private.utils\nimport time:       318 |      14621 |                               numpy.testing\nimport time:       371 |        371 |                                 numpy.f2py.diagnose\nimport time:       161 |        161 |                                   numpy.f2py._backends\nimport time:        96 |         96 |                                   numpy.f2py.__version__\nimport time:       250 |        250 |                                     numpy.f2py.cfuncs\nimport time:       786 |       1035 |                                   numpy.f2py.auxfuncs\nimport time:      1977 |       1977 |                                     numpy.f2py.cb_rules\nimport time:       149 |        149 |                                     numpy.f2py._isocbind\nimport time:       249 |        249 |                                       fileinput\nimport time:      1164 |       1164 |                                             charset_normalizer.constant\nimport time:       899 |        899 |                                                 _multibytecodec\nimport time:       380 |       1278 |                                               charset_normalizer.utils\nimport time:       491 |       1769 |                                             charset_normalizer.md\nimport time:       538 |        538 |                                             charset_normalizer.models\nimport time:       356 |       3826 |                                           charset_normalizer.cd\nimport time:       515 |       4341 |                                         charset_normalizer.api\nimport time:       200 |        200 |                                         charset_normalizer.legacy\nimport time:       119 |        119 |                                         charset_normalizer.version\nimport time:       219 |       4877 |                                       charset_normalizer\nimport time:      1559 |       1559 |                                       numpy.f2py.symbolic\nimport time:     11910 |      18595 |                                     numpy.f2py.crackfortran\nimport time:      1280 |      21998 |                                   numpy.f2py.capi_maps\nimport time:       154 |        154 |                                     numpy.f2py.func2subr\nimport time:       317 |        470 |                                   numpy.f2py.f90mod_rules\nimport time:       163 |        163 |                                     numpy.f2py.common_rules\nimport time:       124 |        124 |                                     numpy.f2py.use_rules\nimport time:      5483 |       5769 |                                   numpy.f2py.rules\nimport time:       604 |      30130 |                                 numpy.f2py.f2py2e\nimport time:       252 |      30752 |                               numpy.f2py\nimport time:       218 |        218 |                                 scipy._lib.array_api_compat._internal\nimport time:      1584 |       1584 |                                 scipy._lib.array_api_compat.common._aliases\nimport time:      1212 |       1212 |                                   scipy._lib.array_api_compat.numpy._typing\nimport time:       266 |       1477 |                                 scipy._lib.array_api_compat.numpy._info\nimport time:      7514 |      10792 |                               scipy._lib.array_api_compat.numpy._aliases\nimport time:      1716 |       1716 |                                 scipy._lib.array_api_compat.common._linalg\nimport time:      1878 |       3594 |                               scipy._lib.array_api_compat.numpy.linalg\nimport time:       230 |        230 |                                 scipy._lib.array_api_compat.common._fft\nimport time:      1025 |       1254 |                               scipy._lib.array_api_compat.numpy.fft\nimport time:       618 |      79200 |                             scipy._lib.array_api_compat.numpy\nimport time:       140 |        140 |                             scipy._lib._sparse\nimport time:       454 |        454 |                                 pkgutil\nimport time:       145 |        145 |                                   _pyrepl\nimport time:       154 |        299 |                                 _pyrepl.pager\nimport time:      1092 |       1844 |                               pydoc\nimport time:      1155 |       2998 |                             scipy._lib._docscrape\nimport time:      3425 |      88140 |                           scipy._lib._array_api\nimport time:       673 |      88813 |                         scipy._lib._util\nimport time:      1273 |      91237 |                       scipy.linalg._cythonized_array_utils\nimport time:      2741 |       2741 |                         scipy.linalg._decomp\nimport time:      1171 |       1171 |                         scipy.linalg._decomp_svd\nimport time:       230 |        230 |                         scipy.linalg._solve_toeplitz\nimport time:      2223 |       6365 |                       scipy.linalg._basic\nimport time:       174 |        174 |                         scipy.linalg._decomp_lu_cython\nimport time:       599 |        773 |                       scipy.linalg._decomp_lu\nimport time:       381 |        381 |                       scipy.linalg._decomp_ldl\nimport time:       714 |        714 |                       scipy.linalg._decomp_cholesky\nimport time:       714 |        714 |                       scipy.linalg._decomp_qr\nimport time:       745 |        745 |                       scipy.linalg._decomp_qz\nimport time:       628 |        628 |                       scipy.linalg._decomp_schur\nimport time:       270 |        270 |                       scipy.linalg._decomp_polar\nimport time:       117 |        117 |                         scipy._lib.deprecation\nimport time:       500 |        500 |                         scipy.linalg._expm_frechet\nimport time:       425 |        425 |                         scipy.linalg._matfuncs_schur_sqrtm\nimport time:       375 |        375 |                         scipy.linalg._matfuncs_expm\nimport time:       186 |        186 |                         scipy.linalg._linalg_pythran\nimport time:      1486 |       3086 |                       scipy.linalg._matfuncs\nimport time:       380 |        380 |                       scipy.linalg._special_matrices\nimport time:      1150 |       1150 |                       scipy.linalg._solvers\nimport time:       278 |        278 |                       scipy.linalg._procrustes\nimport time:       338 |        338 |                         scipy.linalg.cython_blas\nimport time:      1161 |       1499 |                       scipy.linalg._decomp_update\nimport time:       324 |        324 |                             scipy.sparse._sputils\nimport time:       156 |        156 |                             scipy.sparse._matrix\nimport time:       687 |       1166 |                           scipy.sparse._base\nimport time:       183 |        183 |                             scipy.sparse._sparsetools\nimport time:       196 |        196 |                               scipy.sparse._data\nimport time:       138 |        138 |                               scipy.sparse._index\nimport time:       396 |        729 |                             scipy.sparse._compressed\nimport time:       249 |       1160 |                           scipy.sparse._csr\nimport time:       172 |        172 |                           scipy.sparse._csc\nimport time:       420 |        420 |                             scipy.sparse._csparsetools\nimport time:       337 |        757 |                           scipy.sparse._lil\nimport time:       310 |        310 |                           scipy.sparse._dok\nimport time:       573 |        573 |                           scipy.sparse._coo\nimport time:       231 |        231 |                           scipy.sparse._dia\nimport time:       267 |        267 |                           scipy.sparse._bsr\nimport time:       902 |        902 |                           scipy.sparse._construct\nimport time:       114 |        114 |                           scipy.sparse._extract\nimport time:        80 |         80 |                           scipy.sparse._matrix_io\nimport time:        54 |         54 |                           scipy.sparse.base\nimport time:        49 |         49 |                           scipy.sparse.bsr\nimport time:        45 |         45 |                           scipy.sparse.compressed\nimport time:        51 |         51 |                           scipy.sparse.construct\nimport time:        49 |         49 |                           scipy.sparse.coo\nimport time:        45 |         45 |                           scipy.sparse.csc\nimport time:        44 |         44 |                           scipy.sparse.csr\nimport time:        69 |         69 |                           scipy.sparse.data\nimport time:        45 |         45 |                           scipy.sparse.dia\nimport time:        83 |         83 |                           scipy.sparse.dok\nimport time:        45 |         45 |                           scipy.sparse.extract\nimport time:        44 |         44 |                           scipy.sparse.lil\nimport time:        77 |         77 |                           scipy.sparse.sparsetools\nimport time:        59 |         59 |                           scipy.sparse.sputils\nimport time:       404 |       6882 |                         scipy.sparse\nimport time:       411 |       7292 |                       scipy.linalg._sketches\nimport time:       158 |        158 |                       scipy.linalg._decomp_cossin\nimport time:        69 |         69 |                       scipy.linalg.decomp\nimport time:        51 |         51 |                       scipy.linalg.decomp_cholesky\nimport time:        48 |         48 |                       scipy.linalg.decomp_lu\nimport time:        57 |         57 |                       scipy.linalg.decomp_qr\nimport time:        47 |         47 |                       scipy.linalg.decomp_svd\nimport time:        45 |         45 |                       scipy.linalg.decomp_schur\nimport time:        46 |         46 |                       scipy.linalg.basic\nimport time:        42 |         42 |                       scipy.linalg.misc\nimport time:        50 |         50 |                       scipy.linalg.special_matrices\nimport time:        54 |         54 |                       scipy.linalg.matfuncs\nimport time:       466 |     121774 |                     scipy.linalg\nimport time:       167 |     122368 |                   optimagic.utilities\nimport time:      1851 |     125041 |                 optimagic.optimization.fun_value\nimport time:      2728 |     127768 |               optimagic.logging.types\nimport time:       661 |     128429 |             optimagic.logging.sqlalchemy\nimport time:       458 |     221983 |           optimagic.logging.logger\nimport time:        74 |     222057 |         optimagic.logging\nimport time:        25 |     222081 |       optimagic.logging.types\nimport time:       775 |        775 |         optimagic.timing\nimport time:       775 |       1549 |       optimagic.optimization.history\nimport time:       135 |        135 |                 _multiprocessing\nimport time:       229 |        229 |                     multiprocessing.process\nimport time:       256 |        256 |                     multiprocessing.reduction\nimport time:       349 |        833 |                   multiprocessing.context\nimport time:       153 |        985 |                 multiprocessing\nimport time:       181 |       1300 |               joblib._multiprocessing_helpers\nimport time:        71 |         71 |                 joblib.externals\nimport time:       106 |        106 |                 joblib.externals.loky._base\nimport time:       188 |        188 |                       multiprocessing.util\nimport time:       220 |        408 |                     multiprocessing.synchronize\nimport time:       308 |        308 |                           _queue\nimport time:       214 |        522 |                         queue\nimport time:        53 |         53 |                           _winapi\nimport time:       399 |        452 |                         multiprocessing.connection\nimport time:       201 |        201 |                         multiprocessing.queues\nimport time:       319 |       1493 |                       concurrent.futures.process\nimport time:       121 |        121 |                       joblib.externals.loky.backend.process\nimport time:       186 |       1799 |                     joblib.externals.loky.backend.context\nimport time:        78 |       2284 |                   joblib.externals.loky.backend\nimport time:        19 |       2302 |                 joblib.externals.loky.backend.context\nimport time:        73 |         73 |                   joblib.externals.loky.backend._posix_reduction\nimport time:       350 |        350 |                     joblib.externals.cloudpickle.cloudpickle\nimport time:        95 |        444 |                   joblib.externals.cloudpickle\nimport time:       169 |        685 |                 joblib.externals.loky.backend.reduction\nimport time:        47 |         47 |                     faulthandler\nimport time:       126 |        126 |                     joblib.externals.loky.backend.queues\nimport time:        44 |         44 |                       psutil\nimport time:        91 |        134 |                     joblib.externals.loky.backend.utils\nimport time:        72 |         72 |                     joblib.externals.loky.initializers\nimport time:        39 |         39 |                     psutil\nimport time:       473 |        889 |                   joblib.externals.loky.process_executor\nimport time:       141 |       1030 |                 joblib.externals.loky.reusable_executor\nimport time:       110 |        110 |                 joblib.externals.loky.cloudpickle_wrapper\nimport time:       164 |       4465 |               joblib.externals.loky\nimport time:        95 |       5860 |             joblib._cloudpickle_wrapper\nimport time:      2596 |       2596 |               joblib._utils\nimport time:       408 |        408 |               multiprocessing.pool\nimport time:       203 |        203 |                   joblib.backports\nimport time:        81 |         81 |                   joblib.disk\nimport time:       114 |        114 |                         runpy\nimport time:       132 |        246 |                       multiprocessing.spawn\nimport time:       221 |        221 |                       _posixshmem\nimport time:       147 |        613 |                     multiprocessing.resource_tracker\nimport time:       122 |        122 |                     joblib.externals.loky.backend.spawn\nimport time:       183 |        917 |                   joblib.externals.loky.backend.resource_tracker\nimport time:        52 |         52 |                       lz4\nimport time:       284 |        335 |                     joblib.compressor\nimport time:        97 |         97 |                       joblib.numpy_pickle_utils\nimport time:       139 |        236 |                     joblib.numpy_pickle_compat\nimport time:       251 |        821 |                   joblib.numpy_pickle\nimport time:       260 |       2280 |                 joblib._memmapping_reducer\nimport time:       150 |       2430 |               joblib.executor\nimport time:       231 |        231 |               joblib.pool\nimport time:       324 |       5988 |             joblib._parallel_backends\nimport time:       104 |        104 |               joblib.logger\nimport time:       340 |        443 |             joblib._store_backends\nimport time:       148 |        148 |             joblib.hashing\nimport time:       233 |        233 |               joblib.func_inspect\nimport time:       574 |        806 |             joblib.memory\nimport time:       408 |        408 |             joblib.parallel\nimport time:       174 |      13822 |           joblib\nimport time:        50 |         50 |             pathos\nimport time:        18 |         68 |           pathos.pools\nimport time:       272 |        272 |                         scipy.sparse.linalg._interface\nimport time:       133 |        133 |                         scipy.sparse.linalg._isolve.utils\nimport time:       160 |        564 |                       scipy.sparse.linalg._isolve.iterative\nimport time:       110 |        110 |                       scipy.sparse.linalg._isolve.minres\nimport time:       103 |        103 |                         scipy.sparse.linalg._isolve._gcrotmk\nimport time:       108 |        210 |                       scipy.sparse.linalg._isolve.lgmres\nimport time:        92 |         92 |                       scipy.sparse.linalg._isolve.lsqr\nimport time:        82 |         82 |                       scipy.sparse.linalg._isolve.lsmr\nimport time:        61 |         61 |                       scipy.sparse.linalg._isolve.tfqmr\nimport time:       119 |       1236 |                     scipy.sparse.linalg._isolve\nimport time:       444 |        444 |                         scipy.sparse.linalg._dsolve._superlu\nimport time:        51 |         51 |                           scikits\nimport time:        18 |         69 |                         scikits.umfpack\nimport time:       225 |        736 |                       scipy.sparse.linalg._dsolve.linsolve\nimport time:        84 |         84 |                       scipy.sparse.linalg._dsolve._add_newdocs\nimport time:        92 |        910 |                     scipy.sparse.linalg._dsolve\nimport time:       517 |        517 |                             scipy._lib.decorator\nimport time:       309 |        825 |                           scipy._lib._threadsafety\nimport time:       665 |        665 |                           scipy.sparse.linalg._eigen.arpack._arpack\nimport time:       417 |       1906 |                         scipy.sparse.linalg._eigen.arpack.arpack\nimport time:        84 |       1990 |                       scipy.sparse.linalg._eigen.arpack\nimport time:       203 |        203 |                         scipy.sparse.linalg._eigen.lobpcg.lobpcg\nimport time:        99 |        301 |                       scipy.sparse.linalg._eigen.lobpcg\nimport time:        50 |         50 |                           scipy.sparse.linalg._propack\nimport time:       435 |        435 |                           scipy.sparse.linalg._propack._spropack\nimport time:       356 |        356 |                           scipy.sparse.linalg._propack._dpropack\nimport time:       369 |        369 |                           scipy.sparse.linalg._propack._cpropack\nimport time:       365 |        365 |                           scipy.sparse.linalg._propack._zpropack\nimport time:       180 |       1752 |                         scipy.sparse.linalg._svdp\nimport time:       876 |       2628 |                       scipy.sparse.linalg._eigen._svds\nimport time:        96 |       5013 |                     scipy.sparse.linalg._eigen\nimport time:       116 |        116 |                         scipy.sparse.linalg._onenormest\nimport time:       290 |        406 |                       scipy.sparse.linalg._expm_multiply\nimport time:       246 |        652 |                     scipy.sparse.linalg._matfuncs\nimport time:        94 |         94 |                     scipy.sparse.linalg._norm\nimport time:       217 |        217 |                     scipy.sparse.linalg._special_sparse_arrays\nimport time:        60 |         60 |                     scipy.sparse.linalg.isolve\nimport time:        48 |         48 |                     scipy.sparse.linalg.dsolve\nimport time:        43 |         43 |                     scipy.sparse.linalg.interface\nimport time:        42 |         42 |                     scipy.sparse.linalg.eigen\nimport time:        41 |         41 |                     scipy.sparse.linalg.matfuncs\nimport time:       158 |       8508 |                   scipy.sparse.linalg\nimport time:        93 |         93 |                     scipy.optimize._dcsrch\nimport time:       159 |        251 |                   scipy.optimize._linesearch\nimport time:       171 |        171 |                     scipy.optimize._group_columns\nimport time:        51 |         51 |                         scipy._lib.array_api_extra._lib\nimport time:        63 |         63 |                             scipy._lib.array_api_extra._lib._utils\nimport time:       103 |        103 |                               scipy._lib._array_api_compat_vendor\nimport time:       132 |        235 |                             scipy._lib.array_api_extra._lib._utils._compat\nimport time:       117 |        117 |                               scipy._lib.array_api_extra._lib._utils._typing\nimport time:       210 |        327 |                             scipy._lib.array_api_extra._lib._utils._helpers\nimport time:       292 |        915 |                           scipy._lib.array_api_extra._lib._at\nimport time:       356 |       1271 |                         scipy._lib.array_api_extra._lib._funcs\nimport time:       125 |       1445 |                       scipy._lib.array_api_extra._delegation\nimport time:       182 |        182 |                       scipy._lib.array_api_extra._lib._lazy\nimport time:       113 |       1739 |                     scipy._lib.array_api_extra\nimport time:       197 |       2106 |                   scipy.optimize._numdiff\nimport time:       207 |        207 |                     scipy.optimize._hessian_update_strategy\nimport time:       289 |        495 |                   scipy.optimize._differentiable_functions\nimport time:      1453 |      12812 |                 scipy.optimize._optimize\nimport time:       110 |        110 |                     scipy.optimize._trustregion\nimport time:        90 |        199 |                   scipy.optimize._trustregion_dogleg\nimport time:        77 |         77 |                   scipy.optimize._trustregion_ncg\nimport time:       200 |        200 |                         scipy._lib.messagestream\nimport time:       407 |        606 |                       scipy.optimize._trlib._trlib\nimport time:        84 |        689 |                     scipy.optimize._trlib\nimport time:        56 |        745 |                   scipy.optimize._trustregion_krylov\nimport time:       155 |        155 |                   scipy.optimize._trustregion_exact\nimport time:       197 |        197 |                       scipy.optimize._constraints\nimport time:        51 |         51 |                             sksparse\nimport time:        18 |         68 |                           sksparse.cholmod\nimport time:       113 |        181 |                         scipy.optimize._trustregion_constr.projections\nimport time:       106 |        106 |                         scipy.optimize._trustregion_constr.qp_subproblem\nimport time:       110 |        396 |                       scipy.optimize._trustregion_constr.equality_constrained_sqp\nimport time:       146 |        146 |                       scipy.optimize._trustregion_constr.canonical_constraint\nimport time:       119 |        119 |                       scipy.optimize._trustregion_constr.tr_interior_point\nimport time:       155 |        155 |                       scipy.optimize._trustregion_constr.report\nimport time:       251 |       1261 |                     scipy.optimize._trustregion_constr.minimize_trustregion_constr\nimport time:        79 |       1340 |                   scipy.optimize._trustregion_constr\nimport time:       370 |        370 |                     scipy.optimize._lbfgsb\nimport time:       174 |        544 |                   scipy.optimize._lbfgsb_py\nimport time:       189 |        189 |                     scipy.optimize._moduleTNC\nimport time:       135 |        324 |                   scipy.optimize._tnc\nimport time:       110 |        110 |                   scipy.optimize._cobyla_py\nimport time:        60 |         60 |                   scipy.optimize._cobyqa_py\nimport time:       340 |        340 |                     scipy.optimize._slsqplib\nimport time:       167 |        507 |                   scipy.optimize._slsqp_py\nimport time:       262 |       4318 |                 scipy.optimize._minimize\nimport time:       128 |        128 |                     scipy.optimize._minpack\nimport time:       417 |        417 |                           scipy.optimize._lsq.common\nimport time:       125 |        541 |                         scipy.optimize._lsq.trf\nimport time:       124 |        124 |                         scipy.optimize._lsq.dogbox\nimport time:       212 |        876 |                       scipy.optimize._lsq.least_squares\nimport time:       159 |        159 |                           scipy.optimize._lsq.givens_elimination\nimport time:       105 |        264 |                         scipy.optimize._lsq.trf_linear\nimport time:        92 |         92 |                         scipy.optimize._lsq.bvls\nimport time:       106 |        460 |                       scipy.optimize._lsq.lsq_linear\nimport time:        83 |       1418 |                     scipy.optimize._lsq\nimport time:       291 |       1836 |                   scipy.optimize._minpack_py\nimport time:       136 |        136 |                   scipy.optimize._spectral\nimport time:      1260 |       1260 |                   scipy.optimize._nonlin\nimport time:       212 |       3443 |                 scipy.optimize._root\nimport time:       121 |        121 |                     scipy.optimize._zeros\nimport time:       277 |        398 |                   scipy.optimize._zeros_py\nimport time:       151 |        548 |                 scipy.optimize._root_scalar\nimport time:       428 |        428 |                 scipy.optimize._nnls\nimport time:       966 |        966 |                 scipy.optimize._basinhopping\nimport time:        62 |         62 |                       scipy.optimize._highspy\nimport time:      3554 |       3554 |                       scipy.optimize._highspy._core\nimport time:       293 |        293 |                       scipy.optimize._highspy._highs_options\nimport time:       224 |       4132 |                     scipy.optimize._highspy._highs_wrapper\nimport time:       131 |       4262 |                   scipy.optimize._linprog_highs\nimport time:        99 |         99 |                                   uarray\nimport time:       264 |        264 |                                       scipy._lib._uarray._uarray\nimport time:       268 |        532 |                                     scipy._lib._uarray._backend\nimport time:       159 |        691 |                                   scipy._lib._uarray\nimport time:       127 |        916 |                                 scipy._lib.uarray\nimport time:       744 |       1660 |                               scipy.fft._basic\nimport time:       293 |        293 |                               scipy.fft._realtransforms\nimport time:       126 |        126 |                                     scipy.special._sf_error\nimport time:       256 |        256 |                                       scipy.special._ufuncs_cxx\nimport time:       199 |        199 |                                       scipy.special._ellip_harm_2\nimport time:       456 |        456 |                                       scipy.special._special_ufuncs\nimport time:       301 |        301 |                                       scipy.special._gufuncs\nimport time:       740 |       1950 |                                     scipy.special._ufuncs\nimport time:      5387 |       5387 |                                     scipy.special._support_alternative_backends\nimport time:        83 |         83 |                                       scipy.special._input_validation\nimport time:       540 |        540 |                                       scipy.special._specfun\nimport time:       137 |        137 |                                       scipy.special._comb\nimport time:       293 |        293 |                                       scipy.special._multiufuncs\nimport time:       498 |       1549 |                                     scipy.special._basic\nimport time:       885 |        885 |                                     scipy.special._logsumexp\nimport time:       338 |        338 |                                     scipy.special._orthogonal\nimport time:        80 |         80 |                                     scipy.special._spfun_stats\nimport time:        84 |         84 |                                     scipy.special._ellip_harm\nimport time:        56 |         56 |                                     scipy.special._lambertw\nimport time:        95 |         95 |                                     scipy.special._spherical_bessel\nimport time:        54 |         54 |                                     scipy.special.add_newdocs\nimport time:        72 |         72 |                                     scipy.special.basic\nimport time:        63 |         63 |                                     scipy.special.orthogonal\nimport time:       108 |        108 |                                     scipy.special.specfun\nimport time:        47 |         47 |                                     scipy.special.sf_error\nimport time:        46 |         46 |                                     scipy.special.spfun_stats\nimport time:       428 |      11360 |                                   scipy.special\nimport time:       144 |      11504 |                                 scipy.fft._fftlog_backend\nimport time:       159 |      11662 |                               scipy.fft._fftlog\nimport time:       275 |        275 |                                     scipy.fft._pocketfft.pypocketfft\nimport time:       179 |        179 |                                     scipy.fft._pocketfft.helper\nimport time:       302 |        754 |                                   scipy.fft._pocketfft.basic\nimport time:       148 |        148 |                                   scipy.fft._pocketfft.realtransforms\nimport time:       132 |       1034 |                                 scipy.fft._pocketfft\nimport time:       198 |       1232 |                               scipy.fft._helper\nimport time:       202 |        202 |                                 scipy.fft._basic_backend\nimport time:       162 |        162 |                                 scipy.fft._realtransforms_backend\nimport time:       164 |        528 |                               scipy.fft._backend\nimport time:       198 |      15570 |                             scipy.fft\nimport time:       330 |      15900 |                           scipy.linalg._decomp_interpolative\nimport time:       121 |      16020 |                         scipy.linalg.interpolative\nimport time:       118 |      16138 |                       scipy.optimize._remove_redundancy\nimport time:       345 |      16482 |                     scipy.optimize._linprog_util\nimport time:        62 |         62 |                     sksparse\nimport time:        36 |         36 |                       scikits\nimport time:        15 |         50 |                     scikits.umfpack\nimport time:       225 |      16818 |                   scipy.optimize._linprog_ip\nimport time:       120 |        120 |                   scipy.optimize._linprog_simplex\nimport time:       247 |        247 |                     scipy.optimize._bglu_dense\nimport time:       144 |        391 |                   scipy.optimize._linprog_rs\nimport time:       134 |        134 |                   scipy.optimize._linprog_doc\nimport time:       195 |      21917 |                 scipy.optimize._linprog\nimport time:       120 |        120 |                 scipy.optimize._lsap\nimport time:      1601 |       1601 |                 scipy.optimize._differentialevolution\nimport time:       176 |        176 |                   scipy.optimize._pava_pybind\nimport time:       202 |        378 |                 scipy.optimize._isotonic\nimport time:       363 |        363 |                     scipy.spatial._ckdtree\nimport time:       472 |        834 |                   scipy.spatial._kdtree\nimport time:       501 |        501 |                   scipy.spatial._qhull\nimport time:       149 |        149 |                     scipy.spatial._voronoi\nimport time:       195 |        343 |                   scipy.spatial._spherical_voronoi\nimport time:       451 |        451 |                   scipy.spatial._plotutils\nimport time:        98 |         98 |                   scipy.spatial._procrustes\nimport time:       166 |        166 |                       scipy.spatial._hausdorff\nimport time:       312 |        312 |                       scipy.spatial._distance_pybind\nimport time:       137 |        137 |                       scipy.spatial._distance_wrap\nimport time:      1795 |       2408 |                     scipy.spatial.distance\nimport time:        94 |       2501 |                   scipy.spatial._geometric_slerp\nimport time:       181 |        181 |                   scipy.spatial.ckdtree\nimport time:        62 |         62 |                   scipy.spatial.kdtree\nimport time:        70 |         70 |                   scipy.spatial.qhull\nimport time:      2933 |       2933 |                             scipy.constants._codata\nimport time:       937 |        937 |                             scipy.constants._constants\nimport time:       161 |        161 |                             scipy.constants.codata\nimport time:       210 |        210 |                             scipy.constants.constants\nimport time:       560 |       4799 |                           scipy.constants\nimport time:       153 |       4952 |                         scipy.spatial.transform._rotation_groups\nimport time:       639 |       5590 |                       scipy.spatial.transform._rotation\nimport time:       320 |       5910 |                     scipy.spatial.transform._rigid_transform\nimport time:       151 |        151 |                     scipy.spatial.transform._rotation_spline\nimport time:        58 |         58 |                     scipy.spatial.transform.rotation\nimport time:       102 |       6219 |                   scipy.spatial.transform\nimport time:        74 |         74 |                     scipy.optimize._shgo_lib\nimport time:       264 |        264 |                     scipy.optimize._shgo_lib._vertex\nimport time:       287 |        625 |                   scipy.optimize._shgo_lib._complex\nimport time:       539 |      12419 |                 scipy.optimize._shgo\nimport time:       755 |        755 |                 scipy.optimize._dual_annealing\nimport time:       215 |        215 |                 scipy.optimize._qap\nimport time:       144 |        144 |                   scipy.optimize._direct\nimport time:       256 |        400 |                 scipy.optimize._direct_py\nimport time:       154 |        154 |                 scipy.optimize._milp\nimport time:        67 |         67 |                 scipy.optimize.cobyla\nimport time:        50 |         50 |                 scipy.optimize.lbfgsb\nimport time:        62 |         62 |                 scipy.optimize.linesearch\nimport time:        46 |         46 |                 scipy.optimize.minpack\nimport time:        53 |         53 |                 scipy.optimize.minpack2\nimport time:        64 |         64 |                 scipy.optimize.moduleTNC\nimport time:        44 |         44 |                 scipy.optimize.nonlin\nimport time:        50 |         50 |                 scipy.optimize.optimize\nimport time:        45 |         45 |                 scipy.optimize.slsqp\nimport time:        65 |         65 |                 scipy.optimize.tnc\nimport time:        48 |         48 |                 scipy.optimize.zeros\nimport time:       317 |      61370 |               scipy.optimize\nimport time:       737 |      62107 |             optimagic.parameters.bounds\nimport time:       247 |      62353 |           optimagic.deprecations\nimport time:       108 |        108 |                   _plotly_utils\nimport time:       432 |        540 |                 _plotly_utils.importers\nimport time:       125 |        664 |               plotly\nimport time:        68 |         68 |                 _plotly_utils.optional_imports\nimport time:        60 |        127 |               plotly.optional_imports\nimport time:       164 |        164 |                 plotly.graph_objs\nimport time:       130 |        130 |                   PIL._version\nimport time:      1006 |       1135 |                 _plotly_utils.basevalidators\nimport time:       294 |        294 |                   plotly.io\nimport time:        92 |         92 |                   plotly.express._special_inputs\nimport time:       135 |        135 |                   plotly.express.trendline_functions\nimport time:       212 |        212 |                       _plotly_utils.exceptions\nimport time:       108 |        108 |                         _plotly_utils.colors._swatches\nimport time:       142 |        142 |                         _plotly_utils.colors.colorbrewer\nimport time:       111 |        111 |                         _plotly_utils.colors.carto\nimport time:       147 |        507 |                       _plotly_utils.colors.qualitative\nimport time:        64 |         64 |                         _plotly_utils.colors.plotlyjs\nimport time:       102 |        102 |                         _plotly_utils.colors.cmocean\nimport time:       170 |        335 |                       _plotly_utils.colors.sequential\nimport time:        87 |         87 |                       _plotly_utils.colors.diverging\nimport time:        66 |         66 |                       _plotly_utils.colors.cyclical\nimport time:      2300 |       3504 |                     _plotly_utils.colors\nimport time:        81 |       3585 |                   plotly.colors\nimport time:        85 |         85 |                   packaging\nimport time:       111 |        111 |                     packaging._structures\nimport time:      1208 |       1318 |                   packaging.version\nimport time:       430 |        430 |                   plotly._subplots\nimport time:       438 |        438 |                     _plotly_utils.utils\nimport time:       128 |        128 |                     plotly.shapeannotation\nimport time:       989 |       1554 |                   plotly.basedatatypes\nimport time:      3607 |      11096 |                 plotly.express._core\nimport time:       151 |        151 |                 plotly.express.imshow_utils\nimport time:       736 |        736 |                     _plotly_utils.png\nimport time:      2163 |       2163 |                       PIL.ExifTags\nimport time:        69 |         69 |                         PIL._deprecate\nimport time:       333 |        401 |                       PIL.ImageMode\nimport time:       658 |        658 |                       PIL.TiffTags\nimport time:       111 |        111 |                       PIL._binary\nimport time:       176 |        176 |                         PIL._typing\nimport time:       100 |        275 |                       PIL._util\nimport time:        72 |         72 |                       defusedxml\nimport time:       778 |        778 |                       PIL._imaging\nimport time:      1612 |       6067 |                     PIL.Image\nimport time:        97 |       6899 |                   _plotly_utils.data_utils\nimport time:       132 |       7030 |                 plotly.utils\nimport time:        77 |         77 |                 xarray\nimport time:      1371 |      21022 |               plotly.express._imshow\nimport time:       154 |        154 |                 plotly.express._doc\nimport time:     32195 |      32349 |               plotly.express._chart_types\nimport time:       126 |        126 |                 plotly.data\nimport time:       172 |        297 |               plotly.express.data\nimport time:       104 |        104 |               plotly.express.colors\nimport time:       211 |      54771 |             plotly.express\nimport time:        70 |         70 |             petsc4py\nimport time:        34 |         34 |             nlopt\nimport time:        28 |         28 |             pybobyqa\nimport time:        43 |         43 |             dfols\nimport time:        58 |         58 |             pygmo\nimport time:        31 |         31 |             cyipopt\nimport time:        43 |         43 |             fides\nimport time:        32 |         32 |             jax\nimport time:        28 |         28 |             tranquilo\nimport time:        28 |         28 |             numba\nimport time:       134 |        134 |                       iminuit.pdg_format\nimport time:       103 |        103 |                         iminuit.warnings\nimport time:       118 |        221 |                       iminuit._optional_dependencies\nimport time:       149 |        502 |                     iminuit._repr_text\nimport time:       153 |        655 |                   iminuit._repr_html\nimport time:       124 |        124 |                     iminuit._parse_version\nimport time:        96 |         96 |                         quopri\nimport time:       198 |        198 |                           email._parseaddr\nimport time:       445 |        642 |                         email.utils\nimport time:       328 |        328 |                         email.errors\nimport time:       173 |        173 |                             email.quoprimime\nimport time:       105 |        105 |                             email.base64mime\nimport time:        86 |         86 |                               email.encoders\nimport time:       262 |        347 |                             email.charset\nimport time:      2355 |       2978 |                           email.header\nimport time:       424 |       3401 |                         email._policybase\nimport time:       194 |        194 |                         email._encoded_words\nimport time:        97 |         97 |                         email.iterators\nimport time:       449 |       5204 |                       email.message\nimport time:        96 |         96 |                       importlib.metadata._text\nimport time:       155 |       5455 |                     importlib.metadata._adapters\nimport time:       388 |        388 |                       email.feedparser\nimport time:       159 |        547 |                     email.parser\nimport time:       680 |       6805 |                   iminuit._deprecated\nimport time:      1208 |       1208 |                   iminuit.typing\nimport time:       683 |       9349 |                 iminuit.util\nimport time:       964 |        964 |                 iminuit._core\nimport time:      1138 |      11450 |               iminuit.minuit\nimport time:       132 |        132 |               iminuit.minimize\nimport time:       497 |      12077 |             iminuit\nimport time:       259 |        259 |             nevergrad\nimport time:       152 |        152 |                   scipy.stats._warnings_errors\nimport time:       173 |        173 |                         scipy._lib.doccer\nimport time:       209 |        209 |                         scipy.stats._distr_params\nimport time:       515 |        515 |                         scipy.integrate._quadrature\nimport time:       463 |        463 |                           scipy.integrate._odepack\nimport time:       145 |        607 |                         scipy.integrate._odepack_py\nimport time:       182 |        182 |                           scipy.integrate._quadpack\nimport time:       248 |        430 |                         scipy.integrate._quadpack_py\nimport time:       467 |        467 |                           scipy.integrate._vode\nimport time:       195 |        195 |                           scipy.integrate._dop\nimport time:       402 |        402 |                           scipy.integrate._lsoda\nimport time:       413 |       1476 |                         scipy.integrate._ode\nimport time:       217 |        217 |                         scipy.integrate._bvp\nimport time:       414 |        414 |                               scipy.integrate._ivp.common\nimport time:       154 |        154 |                               scipy.integrate._ivp.base\nimport time:       302 |        869 |                             scipy.integrate._ivp.bdf\nimport time:       327 |        327 |                             scipy.integrate._ivp.radau\nimport time:       111 |        111 |                               scipy.integrate._ivp.dop853_coefficients\nimport time:       439 |        549 |                             scipy.integrate._ivp.rk\nimport time:       232 |        232 |                             scipy.integrate._ivp.lsoda\nimport time:       209 |       2185 |                           scipy.integrate._ivp.ivp\nimport time:        97 |       2281 |                         scipy.integrate._ivp\nimport time:       193 |        193 |                         scipy.integrate._quad_vec\nimport time:       123 |        123 |                           scipy._lib._elementwise_iterative_method\nimport time:       281 |        403 |                         scipy.integrate._tanhsinh\nimport time:       221 |        221 |                             scipy.integrate._rules._base\nimport time:       146 |        146 |                             scipy.integrate._rules._genz_malik\nimport time:        73 |         73 |                               scipy.integrate._rules._gauss_legendre\nimport time:       147 |        220 |                             scipy.integrate._rules._gauss_kronrod\nimport time:       113 |        699 |                           scipy.integrate._rules\nimport time:       996 |       1694 |                         scipy.integrate._cubature\nimport time:       350 |        350 |                         scipy.integrate._lebedev\nimport time:        75 |         75 |                         scipy.integrate.dop\nimport time:        53 |         53 |                         scipy.integrate.lsoda\nimport time:       274 |        274 |                         scipy.integrate.vode\nimport time:        51 |         51 |                         scipy.integrate.odepack\nimport time:        46 |         46 |                         scipy.integrate.quadpack\nimport time:        68 |         68 |                         scipy.stats._finite_differences\nimport time:        92 |         92 |                         scipy.stats._constants\nimport time:       116 |        116 |                         scipy.stats._censored_data\nimport time:      1204 |      10517 |                       scipy.stats._distn_infrastructure\nimport time:       163 |        163 |                                 scipy.interpolate._fitpack\nimport time:       186 |        186 |                                 scipy.interpolate._dfitpack\nimport time:       382 |        730 |                               scipy.interpolate._fitpack_impl\nimport time:       176 |        176 |                                 scipy.interpolate._dierckx\nimport time:       425 |        600 |                               scipy.interpolate._bsplines\nimport time:       121 |       1449 |                             scipy.interpolate._fitpack_py\nimport time:       240 |        240 |                             scipy.interpolate._polyint\nimport time:       278 |        278 |                             scipy.interpolate._ppoly\nimport time:       247 |        247 |                             scipy.interpolate._interpnd\nimport time:       459 |       2671 |                           scipy.interpolate._interpolate\nimport time:       350 |        350 |                           scipy.interpolate._fitpack2\nimport time:       151 |        151 |                           scipy.interpolate._rbf\nimport time:       147 |        147 |                             scipy.interpolate._rbfinterp_pythran\nimport time:       133 |        279 |                           scipy.interpolate._rbfinterp\nimport time:       214 |        214 |                           scipy.interpolate._cubic\nimport time:       116 |        116 |                           scipy.interpolate._ndgriddata\nimport time:       177 |        177 |                           scipy.interpolate._fitpack_repro\nimport time:        83 |         83 |                           scipy.interpolate._pade\nimport time:       186 |        186 |                             scipy.interpolate._rgi_cython\nimport time:       325 |        325 |                             scipy.interpolate._ndbspline\nimport time:       211 |        721 |                           scipy.interpolate._rgi\nimport time:       199 |        199 |                           scipy.interpolate._bary_rational\nimport time:        91 |         91 |                           scipy.interpolate.fitpack\nimport time:        54 |         54 |                           scipy.interpolate.fitpack2\nimport time:        46 |         46 |                           scipy.interpolate.interpolate\nimport time:        47 |         47 |                           scipy.interpolate.ndgriddata\nimport time:        46 |         46 |                           scipy.interpolate.polyint\nimport time:        43 |         43 |                           scipy.interpolate.rbf\nimport time:        43 |         43 |                           scipy.interpolate.interpnd\nimport time:       277 |       5601 |                         scipy.interpolate\nimport time:       893 |        893 |                           scipy.special.cython_special\nimport time:      2144 |       3037 |                         scipy.stats._stats\nimport time:       397 |        397 |                         scipy.stats._tukeylambda_stats\nimport time:       254 |        254 |                         scipy.stats._ksstats\nimport time:     32455 |      41741 |                       scipy.stats._continuous_distns\nimport time:       376 |        376 |                         scipy.stats._biasedurn\nimport time:       226 |        226 |                         scipy.stats._stats_pythran\nimport time:      6683 |       7284 |                       scipy.stats._discrete_distns\nimport time:       215 |        215 |                         scipy.stats._levy_stable.levyst\nimport time:      1164 |       1378 |                       scipy.stats._levy_stable\nimport time:       212 |        212 |                         scipy.stats._axis_nan_policy\nimport time:       856 |       1067 |                       scipy.stats._entropy\nimport time:       215 |      62199 |                     scipy.stats.distributions\nimport time:       105 |        105 |                       scipy._lib._bunch\nimport time:      1138 |       1138 |                       scipy.stats._stats_mstats_common\nimport time:      1195 |       2437 |                     scipy.stats._mstats_basic\nimport time:       282 |        282 |                       scipy.stats._common\nimport time:      2018 |       2299 |                     scipy.stats._hypotests\nimport time:      3649 |       3649 |                     scipy.stats._resampling\nimport time:       142 |        142 |                     scipy.stats._binomtest\nimport time:     24925 |      95649 |                   scipy.stats._stats_py\nimport time:       426 |        426 |                   scipy.stats._variation\nimport time:       253 |        253 |                     scipy.stats._ansari_swilk_statistics\nimport time:       214 |        214 |                     scipy.stats._wilcoxon\nimport time:      1114 |       1114 |                     scipy.stats._fit\nimport time:       504 |        504 |                       scipy.stats._relative_risk\nimport time:       277 |        277 |                       scipy.stats._crosstab\nimport time:       135 |        135 |                       scipy.stats._odds_ratio\nimport time:       332 |       1246 |                     scipy.stats.contingency\nimport time:      5110 |       7934 |                   scipy.stats._morestats\nimport time:       143 |        143 |                         scipy.sparse.csgraph._laplacian\nimport time:       205 |        205 |                             scipy.sparse.csgraph._tools\nimport time:        99 |        303 |                           scipy.sparse.csgraph._validation\nimport time:       330 |        632 |                         scipy.sparse.csgraph._shortest_path\nimport time:       885 |        885 |                         scipy.sparse.csgraph._traversal\nimport time:       190 |        190 |                         scipy.sparse.csgraph._min_spanning_tree\nimport time:       253 |        253 |                         scipy.sparse.csgraph._flow\nimport time:       198 |        198 |                         scipy.sparse.csgraph._matching\nimport time:       213 |        213 |                         scipy.sparse.csgraph._reordering\nimport time:       162 |       2672 |                       scipy.sparse.csgraph\nimport time:       180 |        180 |                       scipy.stats._sobol\nimport time:       166 |        166 |                       scipy.stats._qmc_cy\nimport time:       846 |       3862 |                     scipy.stats._qmc\nimport time:       674 |       4536 |                   scipy.stats._multicomp\nimport time:       320 |        320 |                   scipy.stats._binned_statistic\nimport time:       223 |        223 |                       scipy.stats._covariance\nimport time:       213 |        213 |                         scipy.stats._rcont.rcont\nimport time:        84 |        296 |                       scipy.stats._rcont\nimport time:       196 |        196 |                         scipy.stats._qmvnt_cy\nimport time:       206 |        401 |                       scipy.stats._qmvnt\nimport time:      6404 |       7322 |                     scipy.stats._multivariate\nimport time:       402 |       7724 |                   scipy.stats._kde\nimport time:       367 |        367 |                     scipy.stats._mstats_extras\nimport time:       138 |        504 |                   scipy.stats.mstats\nimport time:        70 |         70 |                   scipy.stats.qmc\nimport time:       483 |        483 |                   scipy.stats._page_trend_test\nimport time:       947 |        947 |                   scipy.stats._mannwhitneyu\nimport time:       116 |        116 |                   scipy.stats._bws_test\nimport time:       974 |        974 |                   scipy.stats._sensitivity_analysis\nimport time:       916 |        916 |                   scipy.stats._survival\nimport time:       191 |        191 |                     scipy.optimize._bracket\nimport time:       126 |        126 |                     scipy.optimize._chandrupatla\nimport time:       135 |        135 |                     scipy.stats._probability_distribution\nimport time:      1969 |       2420 |                   scipy.stats._distribution_infrastructure\nimport time:      8781 |       8781 |                   scipy.stats._new_distributions\nimport time:        77 |         77 |                             scipy.ndimage._ni_support\nimport time:       233 |        233 |                             scipy.ndimage._nd_image\nimport time:       143 |        143 |                             scipy.ndimage._ni_docstrings\nimport time:       137 |        137 |                             scipy.ndimage._rank_filter_1d\nimport time:      1083 |       1671 |                           scipy.ndimage._filters\nimport time:       125 |        125 |                           scipy.ndimage._fourier\nimport time:       386 |        386 |                           scipy.ndimage._interpolation\nimport time:       251 |        251 |                             scipy.ndimage._ni_label\nimport time:       285 |        285 |                             scipy.ndimage._morphology\nimport time:       250 |        786 |                           scipy.ndimage._measurements\nimport time:       107 |       3072 |                         scipy.ndimage._ndimage_api\nimport time:       133 |        133 |                         scipy.ndimage._delegators\nimport time:       199 |       3403 |                       scipy.ndimage._support_alternative_backends\nimport time:        83 |         83 |                       scipy.ndimage.filters\nimport time:       211 |        211 |                       scipy.ndimage.fourier\nimport time:        92 |         92 |                       scipy.ndimage.interpolation\nimport time:        80 |         80 |                       scipy.ndimage.measurements\nimport time:        79 |         79 |                       scipy.ndimage.morphology\nimport time:       865 |       4809 |                     scipy.ndimage\nimport time:       420 |       5228 |                   scipy.stats._mgc\nimport time:       467 |        467 |                   scipy.stats._correlation\nimport time:       111 |        111 |                   scipy.stats._quantile\nimport time:        55 |         55 |                   scipy.stats.biasedurn\nimport time:        46 |         46 |                   scipy.stats.kde\nimport time:        95 |         95 |                   scipy.stats.morestats\nimport time:        73 |         73 |                   scipy.stats.mstats_basic\nimport time:        58 |         58 |                   scipy.stats.mstats_extras\nimport time:        47 |         47 |                   scipy.stats.mvn\nimport time:        76 |         76 |                   scipy.stats.stats\nimport time:       502 |     138699 |                 scipy.stats\nimport time:       130 |        130 |                     sklearn._config\nimport time:       157 |        157 |                       sklearn.__check_build._check_build\nimport time:        75 |        232 |                     sklearn.__check_build\nimport time:        69 |         69 |                     sklearn._distributor_init\nimport time:       173 |        173 |                       sklearn.exceptions\nimport time:        84 |         84 |                               sklearn.utils._bunch\nimport time:       431 |        514 |                             sklearn.utils._metadata_requests\nimport time:       113 |        627 |                           sklearn.utils.metadata_routing\nimport time:       150 |        150 |                                   sklearn.externals\nimport time:       156 |        156 |                                       sklearn.externals.array_api_compat.common._helpers\nimport time:       108 |        264 |                                     sklearn.externals.array_api_compat.common\nimport time:       226 |        489 |                                   sklearn.externals.array_api_compat\nimport time:        48 |         48 |                                           sklearn.externals.array_api_extra._lib._utils\nimport time:        47 |         47 |                                             sklearn.externals._array_api_compat_vendor\nimport time:        75 |        122 |                                           sklearn.externals.array_api_extra._lib._utils._compat\nimport time:       232 |        400 |                                         sklearn.externals.array_api_extra._lib._backends\nimport time:        61 |        460 |                                       sklearn.externals.array_api_extra._lib\nimport time:       114 |        114 |                                             sklearn.externals.array_api_extra._lib._utils._typing\nimport time:       100 |        214 |                                           sklearn.externals.array_api_extra._lib._utils._helpers\nimport time:       286 |        499 |                                         sklearn.externals.array_api_extra._lib._at\nimport time:       183 |        682 |                                       sklearn.externals.array_api_extra._lib._funcs\nimport time:       127 |       1269 |                                     sklearn.externals.array_api_extra._delegation\nimport time:       146 |        146 |                                     sklearn.externals.array_api_extra._lib._lazy\nimport time:       196 |       1610 |                                   sklearn.externals.array_api_extra\nimport time:       492 |        492 |                                       sklearn.externals.array_api_compat.common._aliases\nimport time:        81 |         81 |                                       sklearn.externals.array_api_compat._internal\nimport time:       132 |        132 |                                       sklearn.externals.array_api_compat.numpy._info\nimport time:       979 |       1682 |                                     sklearn.externals.array_api_compat.numpy._aliases\nimport time:       523 |        523 |                                       sklearn.externals.array_api_compat.common._linalg\nimport time:       369 |        891 |                                     sklearn.externals.array_api_compat.numpy.linalg\nimport time:        83 |         83 |                                       sklearn.externals.array_api_compat.common._fft\nimport time:       358 |        441 |                                     sklearn.externals.array_api_compat.numpy.fft\nimport time:       398 |       3412 |                                   sklearn.externals.array_api_compat.numpy\nimport time:        62 |         62 |                                       sklearn.externals._packaging\nimport time:        95 |         95 |                                       sklearn.externals._packaging._structures\nimport time:       678 |        834 |                                     sklearn.externals._packaging.version\nimport time:       144 |        144 |                                         ctypes.util\nimport time:       670 |        814 |                                       threadpoolctl\nimport time:       190 |       1003 |                                     sklearn.utils.parallel\nimport time:       573 |       2408 |                                   sklearn.utils.fixes\nimport time:       305 |       8372 |                                 sklearn.utils._array_api\nimport time:       133 |        133 |                                 sklearn.utils.deprecation\nimport time:       458 |        458 |                                 sklearn.utils._isfinite\nimport time:      2672 |       2672 |                                 sklearn.utils._tags\nimport time:      4862 |      16494 |                               sklearn.utils.validation\nimport time:       492 |      16986 |                             sklearn.utils._param_validation\nimport time:       151 |      17137 |                           sklearn.utils._chunking\nimport time:       376 |        376 |                               sklearn.utils.sparsefuncs_fast\nimport time:       214 |        589 |                             sklearn.utils.extmath\nimport time:       189 |        778 |                           sklearn.utils._indexing\nimport time:        84 |         84 |                             sklearn.utils._missing\nimport time:       334 |        418 |                           sklearn.utils._mask\nimport time:        55 |         55 |                             sklearn.utils._repr_html\nimport time:       139 |        194 |                           sklearn.utils._repr_html.base\nimport time:       763 |        763 |                               html.entities\nimport time:       222 |        985 |                             html\nimport time:       262 |       1247 |                           sklearn.utils._repr_html.estimator\nimport time:       130 |        130 |                           sklearn.utils.class_weight\nimport time:       104 |        104 |                           sklearn.utils.discovery\nimport time:       276 |        276 |                           sklearn.utils.murmurhash\nimport time:       156 |      21062 |                         sklearn.utils\nimport time:        20 |      21081 |                       sklearn.utils._metadata_requests\nimport time:       120 |        120 |                       sklearn.utils._repr_html.params\nimport time:        86 |         86 |                         sklearn.utils._available_if\nimport time:       242 |        328 |                       sklearn.utils._set_output\nimport time:       482 |      22181 |                     sklearn.base\nimport time:       293 |        293 |                       sklearn.utils._openmp_helpers\nimport time:       183 |        476 |                     sklearn.utils._show_versions\nimport time:        95 |         95 |                     sklearn._built_with_meson\nimport time:       208 |      23387 |                   sklearn\nimport time:       115 |        115 |                           sklearn.metrics.cluster._bicluster\nimport time:        61 |         61 |                               sklearn.utils._unique\nimport time:       155 |        216 |                             sklearn.utils.multiclass\nimport time:       263 |        263 |                             sklearn.metrics.cluster._expected_mutual_info_fast\nimport time:       246 |        724 |                           sklearn.metrics.cluster._supervised\nimport time:       229 |        229 |                                 sklearn.utils.sparsefuncs\nimport time:       294 |        294 |                                   sklearn.utils._encode\nimport time:       624 |        918 |                                 sklearn.preprocessing._encoders\nimport time:      3026 |       4172 |                               sklearn.preprocessing._data\nimport time:       194 |        194 |                                 sklearn.utils.stats\nimport time:       637 |        831 |                               sklearn.preprocessing._discretization\nimport time:       296 |        296 |                                 sklearn.utils.metaestimators\nimport time:       477 |        772 |                               sklearn.preprocessing._function_transformer\nimport time:       933 |        933 |                               sklearn.preprocessing._label\nimport time:       358 |        358 |                                 sklearn.preprocessing._csr_polynomial_expansion\nimport time:       768 |       1125 |                               sklearn.preprocessing._polynomial\nimport time:       330 |        330 |                                 sklearn.preprocessing._target_encoder_fast\nimport time:       469 |        798 |                               sklearn.preprocessing._target_encoder\nimport time:       137 |       8764 |                             sklearn.preprocessing\nimport time:       510 |        510 |                                   sklearn.metrics._dist_metrics\nimport time:       463 |        463 |                                       sklearn.metrics._pairwise_distances_reduction._datasets_pair\nimport time:       592 |        592 |                                       sklearn.utils._cython_blas\nimport time:       394 |       1447 |                                     sklearn.metrics._pairwise_distances_reduction._base\nimport time:       332 |        332 |                                     sklearn.metrics._pairwise_distances_reduction._middle_term_computer\nimport time:       151 |        151 |                                     sklearn.utils._heap\nimport time:       130 |        130 |                                     sklearn.utils._sorting\nimport time:       418 |       2475 |                                   sklearn.metrics._pairwise_distances_reduction._argkmin\nimport time:       273 |        273 |                                   sklearn.metrics._pairwise_distances_reduction._argkmin_classmode\nimport time:       216 |        216 |                                     sklearn.utils._vector_sentinel\nimport time:       340 |        555 |                                   sklearn.metrics._pairwise_distances_reduction._radius_neighbors\nimport time:       292 |        292 |                                   sklearn.metrics._pairwise_distances_reduction._radius_neighbors_classmode\nimport time:       341 |       4443 |                                 sklearn.metrics._pairwise_distances_reduction._dispatcher\nimport time:       104 |       4547 |                               sklearn.metrics._pairwise_distances_reduction\nimport time:       268 |        268 |                               sklearn.metrics._pairwise_fast\nimport time:       617 |       5431 |                             sklearn.metrics.pairwise\nimport time:       155 |      14350 |                           sklearn.metrics.cluster._unsupervised\nimport time:       114 |      15301 |                         sklearn.metrics.cluster\nimport time:       607 |        607 |                         sklearn.metrics._classification\nimport time:       297 |        297 |                           sklearn.metrics._plot\nimport time:        89 |         89 |                           sklearn.utils._optional_dependencies\nimport time:        71 |         71 |                             sklearn.utils._response\nimport time:       166 |        237 |                           sklearn.utils._plotting\nimport time:       204 |        826 |                         sklearn.metrics._plot.confusion_matrix\nimport time:        89 |         89 |                             sklearn.metrics._base\nimport time:       306 |        394 |                           sklearn.metrics._ranking\nimport time:       107 |        501 |                         sklearn.metrics._plot.det_curve\nimport time:       106 |        106 |                         sklearn.metrics._plot.precision_recall_curve\nimport time:        89 |         89 |                         sklearn.metrics._plot.regression\nimport time:       147 |        147 |                         sklearn.metrics._plot.roc_curve\nimport time:       213 |        213 |                         sklearn.metrics._regression\nimport time:      2539 |       2539 |                         sklearn.metrics._scorer\nimport time:       206 |      20531 |                       sklearn.metrics\nimport time:        19 |      20550 |                     sklearn.metrics.pairwise\nimport time:       885 |      21434 |                   sklearn.gaussian_process.kernels\nimport time:       765 |        765 |                     sklearn.multiclass\nimport time:       156 |        156 |                     sklearn.utils.optimize\nimport time:       791 |       1711 |                   sklearn.gaussian_process._gpc\nimport time:       310 |        310 |                   sklearn.gaussian_process._gpr\nimport time:       104 |      46944 |                 sklearn.gaussian_process\nimport time:       133 |        133 |                 bayes_opt.exception\nimport time:       164 |        164 |                         colorama.ansi\nimport time:        56 |         56 |                           msvcrt\nimport time:        94 |         94 |                           colorama.win32\nimport time:       197 |        346 |                         colorama.winterm\nimport time:       308 |        817 |                       colorama.ansitowin32\nimport time:       119 |        936 |                     colorama.initialise\nimport time:        93 |       1029 |                   colorama\nimport time:        50 |         50 |                       bayes_opt.util\nimport time:       223 |        272 |                     bayes_opt.parameter\nimport time:       145 |        416 |                   bayes_opt.constraint\nimport time:       208 |       1652 |                 bayes_opt.target_space\nimport time:       347 |     187774 |               bayes_opt.acquisition\nimport time:       163 |        163 |                 bayes_opt.domain_reduction\nimport time:       122 |        122 |                 bayes_opt.logger\nimport time:       216 |        501 |               bayes_opt.bayesian_optimization\nimport time:       617 |     188890 |             bayes_opt\nimport time:       251 |     256637 |           optimagic.config\nimport time:       105 |        105 |           optimagic.decorators\nimport time:       237 |     333221 |         optimagic.batch_evaluators\nimport time:        64 |         64 |           optimagic.differentiation\nimport time:       199 |        199 |           optimagic.differentiation.finite_differences\nimport time:       244 |        244 |           optimagic.differentiation.generate_steps\nimport time:        97 |         97 |           optimagic.differentiation.richardson_extrapolation\nimport time:       387 |        387 |           optimagic.parameters.block_trees\nimport time:       982 |       1971 |         optimagic.differentiation.derivatives\nimport time:       822 |        822 |         optimagic.differentiation.numdiff_options\nimport time:       117 |        117 |           optimagic.parameters.process_selectors\nimport time:       483 |        483 |             optimagic.parameters.scaling\nimport time:       127 |        127 |               optimagic.parameters.kernel_transformations\nimport time:        89 |         89 |                 optimagic.parameters.check_constraints\nimport time:       167 |        167 |                 optimagic.parameters.consolidate_constraints\nimport time:       150 |        405 |               optimagic.parameters.process_constraints\nimport time:      1167 |       1698 |             optimagic.parameters.space_conversion\nimport time:       527 |       2707 |           optimagic.parameters.scale_conversion\nimport time:       310 |        310 |           optimagic.parameters.tree_conversion\nimport time:       713 |       3845 |         optimagic.parameters.conversion\nimport time:      1009 |     340866 |       optimagic.optimization.internal_optimization_problem\nimport time:       155 |        155 |       optimagic.type_conversion\nimport time:      1915 |     566564 |     optimagic.optimization.algorithm\nimport time:       244 |     566808 |   optimagic.mark\nimport time:        69 |         69 |       optimagic.optimizers\nimport time:      1229 |       1297 |     optimagic.optimizers.bayesian_optimizer\nimport time:       597 |        597 |     optimagic.optimizers.bhhh\nimport time:      1043 |       1043 |     optimagic.optimizers.fides\nimport time:       632 |        632 |     optimagic.optimizers.iminuit_migrad\nimport time:       135 |        135 |         optimagic.parameters.nonlinear_constraints\nimport time:     11077 |      11211 |       optimagic.optimizers.scipy_optimizers\nimport time:      6440 |      17651 |     optimagic.optimizers.ipopt\nimport time:      2473 |       2473 |     optimagic.optimizers.nag_optimizers\nimport time:       717 |        717 |     optimagic.optimizers.neldermead\nimport time:       891 |        891 |     optimagic.optimizers.nevergrad_optimizers\nimport time:      7385 |       7385 |     optimagic.optimizers.nlopt_optimizers\nimport time:       105 |        105 |         optimagic.optimizers._pounders\nimport time:        60 |         60 |           optimagic.optimizers._pounders._conjugate_gradient\nimport time:       106 |        106 |           optimagic.optimizers._pounders._steihaug_toint\nimport time:        96 |         96 |           optimagic.optimizers._pounders._trsbox\nimport time:       310 |        571 |         optimagic.optimizers._pounders.bntr\nimport time:       334 |        334 |         optimagic.optimizers._pounders.gqtpar\nimport time:       391 |       1400 |       optimagic.optimizers._pounders.pounders_auxiliary\nimport time:       123 |        123 |       optimagic.optimizers._pounders.pounders_history\nimport time:      1179 |       2701 |     optimagic.optimizers.pounders\nimport time:        55 |         55 |       pygmo\nimport time:     11567 |      11622 |     optimagic.optimizers.pygmo_optimizers\nimport time:        53 |         53 |       petsc4py\nimport time:       646 |        699 |     optimagic.optimizers.tao_optimizers\nimport time:      3417 |       3417 |     optimagic.optimizers.tranquilo\nimport time:    114111 |     165230 |   optimagic.algorithms\nimport time:       171 |        171 |     optimagic.benchmarking\nimport time:        92 |         92 |     optimagic.benchmarking.process_benchmark_results\nimport time:        91 |         91 |       optimagic.visualization\nimport time:       167 |        257 |     optimagic.visualization.profile_plot\nimport time:       185 |        704 |   optimagic.benchmarking.benchmark_reports\nimport time:      1969 |       1969 |       optimagic.benchmarking.more_wild\nimport time:      1011 |       2980 |     optimagic.benchmarking.cartis_roberts\nimport time:        97 |         97 |     optimagic.benchmarking.noise_distributions\nimport time:        86 |         86 |       optimagic.shared\nimport time:       132 |        217 |     optimagic.shared.process_user_function\nimport time:       179 |       3471 |   optimagic.benchmarking.get_benchmark_problems\nimport time:      1614 |       1614 |         optimagic.optimization.multistart_options\nimport time:       102 |        102 |         optimagic.optimization.scipy_aliases\nimport time:      1140 |       2855 |       optimagic.optimization.create_optimization_problem\nimport time:       194 |        194 |       optimagic.optimization.error_penalty\nimport time:        67 |         67 |         optimagic.optimization.optimization_logging\nimport time:       228 |        295 |       optimagic.optimization.multistart\nimport time:        54 |         54 |         optimagic.shared.compat\nimport time:      1161 |       1215 |       optimagic.optimization.optimize_result\nimport time:        96 |         96 |         optimagic.optimization.convergence_report\nimport time:       501 |        597 |       optimagic.optimization.process_results\nimport time:       264 |       5415 |     optimagic.optimization.optimize\nimport time:       104 |       5519 |   optimagic.benchmarking.run_benchmark\nimport time:       333 |        333 |   optimagic.logging.read_log\nimport time:       106 |        106 |   optimagic.parameters.constraint_tools\nimport time:       159 |        159 |     plotly.graph_objects\nimport time:        79 |         79 |       plotly.subplots\nimport time:       135 |        214 |     optimagic.visualization.plotting_utilities\nimport time:       135 |        507 |   optimagic.visualization.convergence_plot\nimport time:       117 |        117 |   optimagic.visualization.history_plots\nimport time:       102 |        102 |   optimagic.visualization.slice_plot\nimport time:        81 |         81 |   optimagic._version\nimport time:       282 |    1000603 | optimagic\n"}}, "modified": {"path": "/home/<USER>/learnings/gsoc/pygad_new/new_code/optimagic", "functionality": "passed", "timing": {"times": [0.978915, 1.020228, 0.984873, 1.097909, 1.002054], "mean": 1.0167958000000001, "min": 0.978915, "max": 1.097909, "std": 0.04304061348726341, "sample_output": "import time: self [us] | cumulative | imported package\nimport time:       132 |        132 |   _io\nimport time:        25 |         25 |   marshal\nimport time:       202 |        202 |   posix\nimport time:       544 |        901 | _frozen_importlib_external\nimport time:        58 |         58 |   time\nimport time:       158 |        216 | zipimport\nimport time:        34 |         34 |     _codecs\nimport time:       369 |        402 |   codecs\nimport time:       292 |        292 |   encodings.aliases\nimport time:       342 |       1035 | encodings\nimport time:       113 |        113 | encodings.utf_8\nimport time:        60 |         60 | _signal\nimport time:        21 |         21 |     _abc\nimport time:       112 |        133 |   abc\nimport time:       114 |        246 | io\nimport time:        39 |         39 |       _stat\nimport time:        78 |        117 |     stat\nimport time:       602 |        602 |     _collections_abc\nimport time:        46 |         46 |       errno\nimport time:        67 |         67 |       genericpath\nimport time:       124 |        237 |     posixpath\nimport time:       360 |       1314 |   os\nimport time:        66 |         66 |   _sitebuiltins\nimport time:       294 |        294 |   encodings.utf_8_sig\nimport time:       357 |        357 |   _distutils_hack\nimport time:        91 |         91 |   sitecustomize\nimport time:       601 |       2720 | site\nimport time:        83 |         83 | linecache\nimport time:        78 |         78 |   __future__\nimport time:        88 |         88 |               itertools\nimport time:        84 |         84 |               keyword\nimport time:        67 |         67 |                 _operator\nimport time:       339 |        405 |               operator\nimport time:       129 |        129 |               reprlib\nimport time:        48 |         48 |               _collections\nimport time:       571 |       1323 |             collections\nimport time:        39 |         39 |             _functools\nimport time:       398 |       1759 |           functools\nimport time:      2562 |       2562 |           types\nimport time:      1023 |       5343 |         enum\nimport time:        92 |         92 |           _sre\nimport time:       174 |        174 |             re._constants\nimport time:       255 |        428 |           re._parser\nimport time:        89 |         89 |           re._casefix\nimport time:       251 |        860 |         re._compiler\nimport time:       106 |        106 |         copyreg\nimport time:       375 |       6682 |       re\nimport time:       149 |        149 |           _weakrefset\nimport time:       419 |        568 |         weakref\nimport time:       156 |        723 |       copy\nimport time:       838 |        838 |           _ast\nimport time:       446 |        446 |           contextlib\nimport time:       788 |       2072 |         ast\nimport time:       120 |        120 |             _opcode\nimport time:       141 |        141 |             _opcode_metadata\nimport time:       183 |        443 |           opcode\nimport time:       599 |       1041 |         dis\nimport time:       101 |        101 |           importlib\nimport time:        49 |        150 |         importlib.machinery\nimport time:       889 |        889 |           token\nimport time:        39 |         39 |           _tokenize\nimport time:      1057 |       1984 |         tokenize\nimport time:      1624 |       6869 |       inspect\nimport time:       498 |      14772 |     dataclasses\nimport time:        39 |         39 |       _typing\nimport time:      1775 |       1813 |     typing\nimport time:       225 |        225 |       warnings\nimport time:       111 |        111 |       numpy.version\nimport time:        67 |         67 |       numpy._expired_attrs_2_0\nimport time:        67 |         67 |           numpy._utils._convertions\nimport time:        64 |        131 |         numpy._utils\nimport time:       374 |        504 |       numpy._globals\nimport time:        21 |         21 |         numpy._distributor_init_local\nimport time:        87 |        107 |       numpy._distributor_init\nimport time:       231 |        231 |                   _datetime\nimport time:       107 |        338 |                 datetime\nimport time:       155 |        155 |                 math\nimport time:       157 |        157 |                 numpy.exceptions\nimport time:      2577 |       2577 |                 numpy._core._exceptions\nimport time:       123 |        123 |                     _contextvars\nimport time:        74 |        197 |                   contextvars\nimport time:       110 |        306 |                 numpy._core.printoptions\nimport time:       179 |        179 |                 numpy.dtypes\nimport time:      4599 |       8308 |               numpy._core._multiarray_umath\nimport time:       108 |        108 |                 numpy._utils._inspect\nimport time:      3571 |       3679 |               numpy._core.overrides\nimport time:       383 |      12369 |             numpy._core.multiarray\nimport time:       122 |        122 |             numpy._core.umath\nimport time:       274 |        274 |               numbers\nimport time:       145 |        145 |               numpy._core._dtype\nimport time:        76 |         76 |               numpy._core._string_helpers\nimport time:       184 |        184 |               numpy._core._type_aliases\nimport time:       211 |        888 |             numpy._core.numerictypes\nimport time:       159 |        159 |               numpy._core._ufunc_config\nimport time:      3664 |       3664 |                       _struct\nimport time:        83 |       3747 |                     struct\nimport time:       236 |        236 |                     _compat_pickle\nimport time:       244 |        244 |                     _pickle\nimport time:       599 |       4824 |                   pickle\nimport time:       158 |       4982 |                 numpy._core._methods\nimport time:      5789 |      10770 |               numpy._core.fromnumeric\nimport time:       135 |      11064 |             numpy._core._machar\nimport time:       225 |        225 |                 numpy._core.shape_base\nimport time:        91 |         91 |                 numpy._core._asarray\nimport time:       439 |        439 |                 numpy._core.arrayprint\nimport time:       513 |       1267 |               numpy._core.numeric\nimport time:       276 |       1542 |             numpy._core.einsumfunc\nimport time:       156 |        156 |             numpy._core.function_base\nimport time:       198 |        198 |             numpy._core.getlimits\nimport time:       139 |        139 |             numpy._core.memmap\nimport time:       229 |        229 |             numpy._core.records\nimport time:       964 |        964 |             numpy._core._add_newdocs\nimport time:       226 |        226 |             numpy._core._add_newdocs_scalars\nimport time:        78 |         78 |             numpy._core._dtype_ctypes\nimport time:       358 |        358 |                 _ctypes\nimport time:       228 |        228 |                 ctypes._endian\nimport time:       566 |       1152 |               ctypes\nimport time:       586 |       1737 |             numpy._core._internal\nimport time:       104 |        104 |             numpy._pytesttester\nimport time:       343 |      30151 |           numpy._core\nimport time:        18 |      30169 |         numpy._core._multiarray_umath\nimport time:       202 |      30370 |       numpy.__config__\nimport time:       171 |        171 |                         numpy._typing._nbit_base\nimport time:       147 |        147 |                         numpy._typing._nested_sequence\nimport time:        62 |         62 |                         numpy._typing._shape\nimport time:      1361 |       1739 |                       numpy._typing._array_like\nimport time:      1468 |       1468 |                       numpy._typing._char_codes\nimport time:      5230 |       5230 |                       numpy._typing._dtype_like\nimport time:       157 |        157 |                       numpy._typing._nbit\nimport time:       192 |        192 |                       numpy._typing._scalars\nimport time:       106 |        106 |                       numpy._typing._ufunc\nimport time:       254 |       9144 |                     numpy._typing\nimport time:       265 |        265 |                       numpy.lib._stride_tricks_impl\nimport time:       389 |        653 |                     numpy.lib._twodim_base_impl\nimport time:       166 |        166 |                       numpy.lib._array_utils_impl\nimport time:       120 |        285 |                     numpy.lib.array_utils\nimport time:       433 |        433 |                     numpy.linalg._umath_linalg\nimport time:      4076 |      14589 |                   numpy.linalg._linalg\nimport time:       145 |        145 |                   numpy.linalg.linalg\nimport time:       134 |      14868 |                 numpy.linalg\nimport time:       218 |      15086 |               numpy.matrixlib.defmatrix\nimport time:        92 |      15178 |             numpy.matrixlib\nimport time:       297 |        297 |               numpy.lib._histograms_impl\nimport time:      1290 |       1586 |             numpy.lib._function_base_impl\nimport time:       176 |        176 |             numpy.lib.stride_tricks\nimport time:       421 |      17360 |           numpy.lib._index_tricks_impl\nimport time:       203 |      17562 |         numpy.lib._arraypad_impl\nimport time:       933 |        933 |         numpy.lib._arraysetops_impl\nimport time:       176 |        176 |         numpy.lib._arrayterator_impl\nimport time:       573 |        573 |         numpy.lib._nanfunctions_impl\nimport time:       205 |        205 |                   _wmi\nimport time:       886 |       1091 |                 platform\nimport time:      1991 |       1991 |                 textwrap\nimport time:       403 |       3484 |               numpy.lib._utils_impl\nimport time:       306 |       3789 |             numpy.lib._format_impl\nimport time:       145 |       3934 |           numpy.lib.format\nimport time:       391 |        391 |           numpy.lib._datasource\nimport time:       435 |        435 |           numpy.lib._iotools\nimport time:       880 |       5639 |         numpy.lib._npyio_impl\nimport time:       139 |        139 |             numpy.lib._ufunclike_impl\nimport time:       378 |        517 |           numpy.lib._type_check_impl\nimport time:       713 |       1230 |         numpy.lib._polynomial_impl\nimport time:       472 |        472 |         numpy.lib._shape_base_impl\nimport time:       173 |        173 |         numpy.lib._version\nimport time:       134 |        134 |         numpy.lib.introspect\nimport time:       244 |        244 |         numpy.lib.mixins\nimport time:       134 |        134 |         numpy.lib.npyio\nimport time:       330 |        330 |           numpy.lib._scimath_impl\nimport time:       190 |        519 |         numpy.lib.scimath\nimport time:       486 |      28269 |       numpy.lib\nimport time:       179 |        179 |       numpy._array_api_info\nimport time:      3307 |      63135 |     numpy\nimport time:       177 |        177 |         pytz.exceptions\nimport time:       987 |        987 |           threading\nimport time:       348 |       1334 |         pytz.lazy\nimport time:       254 |        254 |             _bisect\nimport time:       179 |        433 |           bisect\nimport time:       294 |        726 |         pytz.tzinfo\nimport time:       153 |        153 |         pytz.tzfile\nimport time:       767 |       3156 |       pytz\nimport time:       200 |        200 |         dateutil._version\nimport time:       183 |        382 |       dateutil\nimport time:       406 |        406 |           sysconfig\nimport time:       620 |        620 |           _sysconfigdata__linux_x86_64-linux-gnu\nimport time:       947 |       1972 |         pandas.compat._constants\nimport time:       243 |        243 |             _compression\nimport time:       782 |        782 |             _bz2\nimport time:       287 |       1311 |           bz2\nimport time:       919 |        919 |             _lzma\nimport time:       318 |       1237 |           lzma\nimport time:       210 |       2757 |         pandas.compat.compressors\nimport time:       124 |        124 |             pandas.util\nimport time:      2569 |       2693 |           pandas.util.version\nimport time:       826 |       3518 |         pandas.compat.numpy\nimport time:        49 |         49 |           pyarrow\nimport time:       100 |        148 |         pandas.compat.pyarrow\nimport time:       271 |       8665 |       pandas.compat\nimport time:       556 |        556 |                   numpy.random._common\nimport time:       277 |        277 |                       binascii\nimport time:       271 |        547 |                     base64\nimport time:      2244 |       2244 |                       _hashlib\nimport time:       268 |        268 |                         _blake2\nimport time:       319 |        587 |                       hashlib\nimport time:       262 |       3091 |                     hmac\nimport time:       203 |        203 |                       _random\nimport time:       494 |        696 |                     random\nimport time:       177 |       4510 |                   secrets\nimport time:       595 |       5659 |                 numpy.random.bit_generator\nimport time:       333 |       5992 |               numpy.random._bounded_integers\nimport time:       325 |        325 |                   numpy.random._pcg64\nimport time:       336 |        336 |                   numpy.random._mt19937\nimport time:      2270 |       2930 |                 numpy.random._generator\nimport time:       293 |        293 |                 numpy.random._philox\nimport time:       227 |        227 |                 numpy.random._sfc64\nimport time:      1131 |       1131 |                 numpy.random.mtrand\nimport time:       206 |       4786 |               numpy.random._pickle\nimport time:       239 |      11016 |             numpy.random\nimport time:      4125 |      15141 |           pandas._typing\nimport time:       191 |        191 |           pandas.util._exceptions\nimport time:       718 |      16049 |         pandas._config.config\nimport time:       275 |        275 |         pandas._config.dates\nimport time:        63 |         63 |             _locale\nimport time:       564 |        626 |           locale\nimport time:       117 |        743 |         pandas._config.display\nimport time:       143 |      17208 |       pandas._config\nimport time:        83 |         83 |         pandas.core\nimport time:       588 |        670 |       pandas.core.config_init\nimport time:       151 |        151 |           pandas._libs.pandas_parser\nimport time:       119 |        119 |           pandas._libs.pandas_datetime\nimport time:       129 |        129 |                       pandas._libs.tslibs.ccalendar\nimport time:       215 |        215 |                       pandas._libs.tslibs.np_datetime\nimport time:       678 |       1022 |                     pandas._libs.tslibs.dtypes\nimport time:       121 |        121 |                       pandas._libs.tslibs.base\nimport time:       263 |        263 |                           pandas._libs.tslibs.nattype\nimport time:       129 |        129 |                               pandas.compat._optional\nimport time:       157 |        157 |                                 zoneinfo._tzpath\nimport time:       121 |        121 |                                 zoneinfo._common\nimport time:       133 |        133 |                                 _zoneinfo\nimport time:       174 |        584 |                               zoneinfo\nimport time:       178 |        178 |                                       importlib._abc\nimport time:       139 |        316 |                                     importlib.util\nimport time:       565 |        881 |                                   six\nimport time:        30 |         30 |                                   six.moves\nimport time:       149 |        149 |                                   dateutil.tz._common\nimport time:       110 |        110 |                                   dateutil.tz._factories\nimport time:        21 |         21 |                                     six.moves.winreg\nimport time:       184 |        205 |                                   dateutil.tz.win\nimport time:       531 |       1903 |                                 dateutil.tz.tz\nimport time:        99 |       2002 |                               dateutil.tz\nimport time:       411 |       3124 |                             pandas._libs.tslibs.timezones\nimport time:       824 |        824 |                                 calendar\nimport time:       688 |       1512 |                               _strptime\nimport time:       361 |        361 |                                   signal\nimport time:       150 |        150 |                                   fcntl\nimport time:        52 |         52 |                                   msvcrt\nimport time:        92 |         92 |                                   _posixsubprocess\nimport time:       109 |        109 |                                   select\nimport time:       350 |        350 |                                   selectors\nimport time:       525 |       1637 |                                 subprocess\nimport time:       119 |       1755 |                               pandas._config.localization\nimport time:       312 |       3578 |                             pandas._libs.tslibs.fields\nimport time:       548 |       7249 |                           pandas._libs.tslibs.timedeltas\nimport time:       279 |        279 |                           pandas._libs.tslibs.tzconversion\nimport time:       501 |       8291 |                         pandas._libs.tslibs.timestamps\nimport time:       154 |        154 |                         pandas._libs.properties\nimport time:       932 |       9376 |                       pandas._libs.tslibs.offsets\nimport time:       580 |        580 |                           _decimal\nimport time:        99 |        678 |                         decimal\nimport time:        27 |         27 |                               _string\nimport time:       394 |        420 |                             string\nimport time:        75 |         75 |                             dateutil._common\nimport time:       803 |       1298 |                           dateutil.parser._parser\nimport time:       217 |        217 |                           dateutil.parser.isoparser\nimport time:       191 |       1705 |                         dateutil.parser\nimport time:       541 |        541 |                         pandas._libs.tslibs.strptime\nimport time:       438 |       3361 |                       pandas._libs.tslibs.parsing\nimport time:       355 |      13211 |                     pandas._libs.tslibs.conversion\nimport time:       409 |        409 |                     pandas._libs.tslibs.period\nimport time:       272 |        272 |                     pandas._libs.tslibs.vectorized\nimport time:       160 |      15072 |                   pandas._libs.tslibs\nimport time:        18 |      15089 |                 pandas._libs.tslibs.nattype\nimport time:       120 |        120 |                 pandas._libs.ops_dispatch\nimport time:       446 |      15654 |               pandas._libs.missing\nimport time:       738 |      16392 |             pandas._libs.hashtable\nimport time:      2472 |       2472 |             pandas._libs.algos\nimport time:       531 |      19394 |           pandas._libs.interval\nimport time:        99 |      19761 |         pandas._libs\nimport time:       107 |        107 |           pandas.core.dtypes\nimport time:        54 |         54 |             pyarrow\nimport time:       688 |        742 |           pandas._libs.lib\nimport time:       453 |        453 |           pandas.errors\nimport time:       374 |        374 |             pandas.core.dtypes.generic\nimport time:       233 |        607 |           pandas.core.dtypes.base\nimport time:       134 |        134 |           pandas.core.dtypes.inference\nimport time:       960 |       3000 |         pandas.core.dtypes.dtypes\nimport time:       206 |        206 |           pandas.core.dtypes.common\nimport time:       234 |        440 |         pandas.core.dtypes.missing\nimport time:       154 |        154 |           pandas.util._decorators\nimport time:        72 |         72 |               pandas.io\nimport time:       131 |        203 |             pandas.io._util\nimport time:       384 |        586 |           pandas.core.dtypes.cast\nimport time:       108 |        108 |             pandas.core.dtypes.astype\nimport time:       135 |        243 |           pandas.core.dtypes.concat\nimport time:        58 |         58 |             pandas.core.array_algos\nimport time:      2687 |       2687 |                 numpy.ma.core\nimport time:       714 |        714 |                 numpy.ma.extras\nimport time:       125 |       3526 |               numpy.ma\nimport time:       184 |        184 |               pandas.core.common\nimport time:       197 |       3906 |             pandas.core.construction\nimport time:       192 |       4155 |           pandas.core.array_algos.take\nimport time:       120 |        120 |             pandas.core.indexers.utils\nimport time:        90 |        209 |           pandas.core.indexers\nimport time:       401 |       5747 |         pandas.core.algorithms\nimport time:       194 |        194 |             pandas.core.arrays.arrow.accessors\nimport time:       171 |        171 |               unicodedata\nimport time:       155 |        155 |               pandas.util._validators\nimport time:       205 |        205 |               pandas.core.missing\nimport time:       233 |        233 |                   pandas._libs.ops\nimport time:        85 |         85 |                   pandas.core.roperator\nimport time:        75 |         75 |                   pandas.core.computation\nimport time:       114 |        114 |                     pandas.core.computation.check\nimport time:       178 |        292 |                   pandas.core.computation.expressions\nimport time:        77 |         77 |                   pandas.core.ops.missing\nimport time:        54 |         54 |                   pandas.core.ops.dispatch\nimport time:        54 |         54 |                   pandas.core.ops.invalid\nimport time:       222 |       1089 |                 pandas.core.ops.array_ops\nimport time:        66 |         66 |                 pandas.core.ops.common\nimport time:        94 |         94 |                 pandas.core.ops.docstrings\nimport time:        66 |         66 |                 pandas.core.ops.mask_ops\nimport time:       101 |       1413 |               pandas.core.ops\nimport time:       184 |        184 |               pandas.core.arraylike\nimport time:       160 |        160 |               pandas.core.arrays._arrow_string_mixins\nimport time:       100 |        100 |               pandas.core.arrays._utils\nimport time:       170 |        170 |                 pandas.compat.numpy.function\nimport time:        80 |         80 |                 pandas.core.array_algos.quantile\nimport time:       159 |        159 |                 pandas.core.sorting\nimport time:       618 |       1024 |               pandas.core.arrays.base\nimport time:       522 |        522 |                 pandas.core.nanops\nimport time:       112 |        112 |                 pandas.core.array_algos.masked_accumulations\nimport time:        78 |         78 |                 pandas.core.array_algos.masked_reductions\nimport time:        55 |         55 |                   pandas.core.util\nimport time:       201 |        201 |                   pandas._libs.hashing\nimport time:       139 |        394 |                 pandas.core.util.hashing\nimport time:       511 |       1615 |               pandas.core.arrays.masked\nimport time:       176 |        176 |                 pandas._libs.arrays\nimport time:       144 |        144 |                   pandas.core.arrays.numeric\nimport time:       191 |        335 |                 pandas.core.arrays.floating\nimport time:       177 |        177 |                 pandas.core.arrays.integer\nimport time:        80 |         80 |                     pandas.core.array_algos.transforms\nimport time:       515 |        595 |                   pandas.core.arrays._mixins\nimport time:        69 |         69 |                     pandas.core.strings\nimport time:       172 |        172 |                     pandas.core.strings.base\nimport time:       318 |        559 |                   pandas.core.strings.object_array\nimport time:       254 |       1406 |                 pandas.core.arrays.numpy_\nimport time:        77 |         77 |                 pandas.io.formats\nimport time:        87 |         87 |                       fnmatch\nimport time:       151 |        151 |                       zlib\nimport time:       479 |        716 |                     shutil\nimport time:        77 |        792 |                   pandas.io.formats.console\nimport time:       247 |       1039 |                 pandas.io.formats.printing\nimport time:       447 |       3653 |               pandas.core.arrays.string_\nimport time:        89 |         89 |                 pandas.tseries\nimport time:       242 |        330 |               pandas.tseries.frequencies\nimport time:      1001 |      10007 |             pandas.core.arrays.arrow.array\nimport time:       117 |      10317 |           pandas.core.arrays.arrow\nimport time:       208 |        208 |           pandas.core.arrays.boolean\nimport time:       135 |        135 |               _csv\nimport time:       268 |        402 |             csv\nimport time:       192 |        192 |             pandas.core.accessor\nimport time:       388 |        388 |             pandas.core.base\nimport time:       741 |       1721 |           pandas.core.arrays.categorical\nimport time:       325 |        325 |             pandas._libs.tslib\nimport time:        87 |         87 |               pandas.core.array_algos.datetimelike_accumulations\nimport time:       844 |        930 |             pandas.core.arrays.datetimelike\nimport time:       255 |        255 |             pandas.core.arrays._ranges\nimport time:        95 |         95 |             pandas.tseries.offsets\nimport time:       517 |       2120 |           pandas.core.arrays.datetimes\nimport time:       308 |        308 |             pandas.core.arrays.timedeltas\nimport time:       833 |       1140 |           pandas.core.arrays.interval\nimport time:       341 |        341 |           pandas.core.arrays.period\nimport time:       337 |        337 |                 pandas._libs.sparse\nimport time:       562 |        898 |               pandas.core.arrays.sparse.array\nimport time:       214 |       1111 |             pandas.core.arrays.sparse.accessor\nimport time:       104 |       1215 |           pandas.core.arrays.sparse\nimport time:       293 |        293 |           pandas.core.arrays.string_arrow\nimport time:       190 |      17540 |         pandas.core.arrays\nimport time:       112 |        112 |         pandas.core.flags\nimport time:       294 |        294 |               pandas._libs.internals\nimport time:       101 |        101 |                 pandas.core._numba\nimport time:       152 |        252 |               pandas.core._numba.executor\nimport time:       489 |       1034 |             pandas.core.apply\nimport time:        47 |         47 |                 gc\nimport time:       132 |        132 |                       _json\nimport time:       271 |        402 |                     json.scanner\nimport time:       343 |        745 |                   json.decoder\nimport time:       247 |        247 |                   json.encoder\nimport time:       159 |       1149 |                 json\nimport time:       133 |        133 |                   pandas._libs.indexing\nimport time:        96 |         96 |                     pandas.core.indexes\nimport time:       558 |        558 |                       pandas._libs.index\nimport time:       240 |        240 |                       pandas._libs.writers\nimport time:       311 |        311 |                       pandas._libs.join\nimport time:       102 |        102 |                       pandas.core.array_algos.putmask\nimport time:       101 |        101 |                       pandas.core.indexes.frozen\nimport time:      1427 |       1427 |                       pandas.core.strings.accessor\nimport time:      1600 |       4337 |                     pandas.core.indexes.base\nimport time:       181 |        181 |                       pandas.core.indexes.extension\nimport time:       237 |        417 |                     pandas.core.indexes.category\nimport time:       405 |        405 |                         pandas.core.indexes.range\nimport time:        95 |         95 |                           pandas.core.tools\nimport time:       138 |        232 |                         pandas.core.tools.timedeltas\nimport time:       407 |       1044 |                       pandas.core.indexes.datetimelike\nimport time:       120 |        120 |                       pandas.core.tools.times\nimport time:       488 |       1650 |                     pandas.core.indexes.datetimes\nimport time:       831 |        831 |                       pandas.core.indexes.multi\nimport time:       223 |        223 |                       pandas.core.indexes.timedeltas\nimport time:      2305 |       3357 |                     pandas.core.indexes.interval\nimport time:       303 |        303 |                     pandas.core.indexes.period\nimport time:       442 |      10598 |                   pandas.core.indexes.api\nimport time:       679 |      11410 |                 pandas.core.indexing\nimport time:       122 |        122 |                 pandas.core.sample\nimport time:        98 |         98 |                 pandas.core.array_algos.replace\nimport time:       711 |        711 |                     pandas.core.internals.blocks\nimport time:       117 |        827 |                   pandas.core.internals.api\nimport time:       182 |        182 |                     pandas.core.internals.base\nimport time:       294 |        294 |                       pandas.core.internals.ops\nimport time:       505 |        798 |                     pandas.core.internals.managers\nimport time:       424 |       1403 |                   pandas.core.internals.array_manager\nimport time:       196 |        196 |                   pandas.core.internals.concat\nimport time:       117 |       2541 |                 pandas.core.internals\nimport time:       192 |        192 |                 pandas.core.internals.construction\nimport time:        86 |         86 |                   pandas.core.methods\nimport time:        62 |         62 |                     pandas.core.reshape\nimport time:       397 |        458 |                   pandas.core.reshape.concat\nimport time:       253 |        253 |                       gzip\nimport time:       179 |        179 |                       mmap\nimport time:       350 |        350 |                           glob\nimport time:       320 |        670 |                         pathlib._abc\nimport time:        49 |         49 |                             _winapi\nimport time:        36 |         36 |                             nt\nimport time:        26 |         26 |                             nt\nimport time:        31 |         31 |                             nt\nimport time:        29 |         29 |                             nt\nimport time:        27 |         27 |                             nt\nimport time:        27 |         27 |                             nt\nimport time:        26 |         26 |                             nt\nimport time:       178 |        426 |                           ntpath\nimport time:        40 |         40 |                           pwd\nimport time:       144 |        144 |                           grp\nimport time:       514 |       1122 |                         pathlib._local\nimport time:        99 |       1890 |                       pathlib\nimport time:       836 |        836 |                       tarfile\nimport time:       108 |        108 |                         urllib\nimport time:       796 |        796 |                         ipaddress\nimport time:       851 |       1754 |                       urllib.parse\nimport time:       108 |        108 |                           zipfile._path.glob\nimport time:       284 |        392 |                         zipfile._path\nimport time:       615 |       1006 |                       zipfile\nimport time:        80 |         80 |                       pandas.core.shared_docs\nimport time:      1168 |       7162 |                     pandas.io.common\nimport time:       523 |       7685 |                   pandas.io.formats.format\nimport time:       218 |       8445 |                 pandas.core.methods.describe\nimport time:        62 |         62 |                       pandas._libs.window\nimport time:       338 |        399 |                     pandas._libs.window.aggregations\nimport time:       195 |        195 |                       pandas._libs.window.indexers\nimport time:       289 |        483 |                     pandas.core.indexers.objects\nimport time:       103 |        103 |                     pandas.core.util.numba_\nimport time:        88 |         88 |                     pandas.core.window.common\nimport time:       119 |        119 |                     pandas.core.window.doc\nimport time:       324 |        324 |                     pandas.core.window.numba_\nimport time:       137 |        137 |                     pandas.core.window.online\nimport time:      1126 |       1126 |                     pandas.core.window.rolling\nimport time:       491 |       3268 |                   pandas.core.window.ewm\nimport time:       641 |        641 |                   pandas.core.window.expanding\nimport time:       113 |       4021 |                 pandas.core.window\nimport time:      2686 |      30706 |               pandas.core.generic\nimport time:       248 |        248 |               pandas.core.methods.selectn\nimport time:        68 |         68 |                 pandas.core.reshape.util\nimport time:        88 |         88 |                 pandas.core.tools.numeric\nimport time:       183 |        338 |               pandas.core.reshape.melt\nimport time:       249 |        249 |                 pandas._libs.reshape\nimport time:       449 |        449 |                 pandas.core.indexes.accessors\nimport time:        78 |         78 |                   pandas.arrays\nimport time:       482 |        560 |                 pandas.core.tools.datetimes\nimport time:       544 |        544 |                 pandas.io.formats.info\nimport time:       554 |        554 |                   pandas.plotting._core\nimport time:       199 |        199 |                   pandas.plotting._misc\nimport time:       131 |        882 |                 pandas.plotting\nimport time:      2208 |       4889 |               pandas.core.series\nimport time:      4204 |      40384 |             pandas.core.frame\nimport time:       758 |        758 |             pandas.core.groupby.base\nimport time:       496 |        496 |               pandas._libs.groupby\nimport time:        70 |         70 |                 pandas.core.groupby.categorical\nimport time:       318 |        388 |               pandas.core.groupby.grouper\nimport time:       572 |       1455 |             pandas.core.groupby.ops\nimport time:        94 |         94 |               pandas.core.groupby.numba_\nimport time:       194 |        194 |               pandas.core.groupby.indexing\nimport time:      1271 |       1559 |             pandas.core.groupby.groupby\nimport time:      1151 |      46338 |           pandas.core.groupby.generic\nimport time:        82 |      46419 |         pandas.core.groupby\nimport time:       160 |      93176 |       pandas.core.api\nimport time:        87 |         87 |       pandas.tseries.api\nimport time:        53 |         53 |               pandas.core.computation.common\nimport time:       108 |        160 |             pandas.core.computation.align\nimport time:       202 |        202 |                 pprint\nimport time:       150 |        352 |               pandas.core.computation.scope\nimport time:       262 |        613 |             pandas.core.computation.ops\nimport time:       158 |        930 |           pandas.core.computation.engines\nimport time:       255 |        255 |             pandas.core.computation.parsing\nimport time:       627 |        881 |           pandas.core.computation.expr\nimport time:       157 |       1967 |         pandas.core.computation.eval\nimport time:        60 |       2026 |       pandas.core.computation.api\nimport time:       125 |        125 |         pandas.core.reshape.encoding\nimport time:       155 |        155 |             _uuid\nimport time:       321 |        476 |           uuid\nimport time:       548 |       1024 |         pandas.core.reshape.merge\nimport time:       417 |        417 |         pandas.core.reshape.pivot\nimport time:       142 |        142 |         pandas.core.reshape.tile\nimport time:       135 |       1841 |       pandas.core.reshape.api\nimport time:        86 |         86 |         pandas.api.extensions\nimport time:        53 |         53 |         pandas.api.indexers\nimport time:        44 |         44 |             pandas.core.interchange\nimport time:       586 |        629 |           pandas.core.interchange.dataframe_protocol\nimport time:       302 |        302 |             pandas.core.interchange.utils\nimport time:       207 |        508 |           pandas.core.interchange.from_dataframe\nimport time:        73 |       1210 |         pandas.api.interchange\nimport time:        68 |         68 |           pandas.core.dtypes.api\nimport time:       106 |        174 |         pandas.api.types\nimport time:       737 |        737 |           pandas.core.resample\nimport time:       151 |        151 |                 pandas._libs.json\nimport time:       139 |        139 |                 pandas.io.json._normalize\nimport time:       134 |        134 |                 pandas.io.json._table_schema\nimport time:       355 |        355 |                       pandas._libs.parsers\nimport time:       373 |        373 |                         pandas.io.parsers.base_parser\nimport time:       148 |        521 |                       pandas.io.parsers.arrow_parser_wrapper\nimport time:       168 |        168 |                       pandas.io.parsers.c_parser_wrapper\nimport time:       286 |        286 |                       pandas.io.parsers.python_parser\nimport time:      1342 |       2669 |                     pandas.io.parsers.readers\nimport time:        94 |       2763 |                   pandas.io.parsers\nimport time:        33 |       2796 |                 pandas.io.parsers.readers\nimport time:       608 |       3825 |               pandas.io.json._json\nimport time:       133 |       3958 |             pandas.io.json\nimport time:        20 |       3977 |           pandas.io.json._json\nimport time:       925 |        925 |           pandas.io.stata\nimport time:        90 |       5728 |         pandas.api.typing\nimport time:       148 |       7395 |       pandas.api\nimport time:       262 |        262 |               tempfile\nimport time:       125 |        387 |             pandas._testing.contexts\nimport time:       116 |        502 |           pandas._testing._io\nimport time:       119 |        119 |           pandas._testing._warnings\nimport time:       121 |        121 |               cmath\nimport time:       178 |        298 |             pandas._libs.testing\nimport time:       202 |        500 |           pandas._testing.asserters\nimport time:        91 |         91 |           pandas._testing.compat\nimport time:       311 |       1520 |         pandas._testing\nimport time:       106 |       1625 |       pandas.testing\nimport time:       114 |        114 |       pandas.util._print_versions\nimport time:        81 |         81 |         pandas.io.clipboards\nimport time:      2014 |       2014 |             pandas.io.excel._util\nimport time:       244 |        244 |             pandas.io.excel._calamine\nimport time:       207 |        207 |             pandas.io.excel._odfreader\nimport time:       241 |        241 |             pandas.io.excel._openpyxl\nimport time:       137 |        137 |             pandas.io.excel._pyxlsb\nimport time:       140 |        140 |             pandas.io.excel._xlrd\nimport time:       824 |       3804 |           pandas.io.excel._base\nimport time:       189 |        189 |           pandas.io.excel._odswriter\nimport time:       149 |        149 |           pandas.io.excel._xlsxwriter\nimport time:       107 |       4247 |         pandas.io.excel\nimport time:       110 |        110 |         pandas.io.feather_format\nimport time:        73 |         73 |         pandas.io.gbq\nimport time:       388 |        388 |         pandas.io.html\nimport time:       107 |        107 |         pandas.io.orc\nimport time:       292 |        292 |         pandas.io.parquet\nimport time:       132 |        132 |           pandas.compat.pickle_compat\nimport time:       140 |        272 |         pandas.io.pickle\nimport time:       350 |        350 |           pandas.core.computation.pytables\nimport time:      1415 |       1764 |         pandas.io.pytables\nimport time:       152 |        152 |           pandas.io.sas.sasreader\nimport time:        87 |        238 |         pandas.io.sas\nimport time:        86 |         86 |         pandas.io.spss\nimport time:       491 |        491 |         pandas.io.sql\nimport time:       369 |        369 |         pandas.io.xml\nimport time:       164 |       8675 |       pandas.io.api\nimport time:        79 |         79 |       pandas.util._tester\nimport time:        51 |         51 |       pandas._version_meson\nimport time:       543 |     145686 |     pandas\nimport time:       259 |        259 |       numpy._typing._add_docstring\nimport time:        87 |        346 |     numpy.typing\nimport time:       143 |        143 |         _colorize\nimport time:       653 |        795 |       traceback\nimport time:       230 |       1025 |     optimagic.exceptions\nimport time:        73 |         73 |       optimagic.optimization\nimport time:       102 |        175 |     optimagic.optimization.algo_options\nimport time:       306 |        306 |           _socket\nimport time:       771 |       1076 |         typing_extensions\nimport time:      4326 |       5402 |       annotated_types\nimport time:      1871 |       7272 |     optimagic.typing\nimport time:      3635 |     237854 |   optimagic.constraints\nimport time:       170 |        170 |                 sqlalchemy.util.preloaded\nimport time:        51 |         51 |                     sqlalchemy.cyextension\nimport time:       309 |        309 |                     sqlalchemy.cyextension.collections\nimport time:       167 |        167 |                     sqlalchemy.cyextension.immutabledict\nimport time:       132 |        132 |                     sqlalchemy.cyextension.processors\nimport time:       148 |        148 |                     sqlalchemy.cyextension.resultproxy\nimport time:        89 |         89 |                             email\nimport time:       220 |        220 |                             importlib.metadata._meta\nimport time:       154 |        154 |                             importlib.metadata._collections\nimport time:        64 |         64 |                             importlib.metadata._functools\nimport time:        50 |         50 |                             importlib.metadata._itertools\nimport time:       249 |        249 |                                   importlib.resources.abc\nimport time:       314 |        562 |                                 importlib.resources._common\nimport time:       114 |        114 |                                 importlib.resources._functional\nimport time:       151 |        827 |                               importlib.resources\nimport time:       259 |       1085 |                             importlib.abc\nimport time:       894 |       2553 |                           importlib.metadata\nimport time:       349 |       2901 |                         sqlalchemy.util.compat\nimport time:       798 |       3699 |                       sqlalchemy.exc\nimport time:       316 |       4014 |                     sqlalchemy.cyextension.util\nimport time:       153 |       4971 |                   sqlalchemy.util._has_cy\nimport time:       531 |        531 |                   sqlalchemy.util.typing\nimport time:       598 |       6100 |                 sqlalchemy.util._collections\nimport time:        69 |         69 |                         concurrent\nimport time:        35 |         35 |                             atexit\nimport time:      1203 |       1238 |                           logging\nimport time:       533 |       1770 |                         concurrent.futures._base\nimport time:       118 |       1956 |                       concurrent.futures\nimport time:       108 |        108 |                         _heapq\nimport time:       129 |        236 |                       heapq\nimport time:       168 |        168 |                         array\nimport time:       850 |       1018 |                       socket\nimport time:       875 |        875 |                         _ssl\nimport time:      1381 |       2255 |                       ssl\nimport time:       197 |        197 |                       asyncio.constants\nimport time:        82 |         82 |                       asyncio.coroutines\nimport time:        85 |         85 |                         asyncio.format_helpers\nimport time:        82 |         82 |                           asyncio.base_futures\nimport time:       134 |        134 |                           asyncio.exceptions\nimport time:        85 |         85 |                           asyncio.base_tasks\nimport time:       205 |        505 |                         _asyncio\nimport time:       324 |        912 |                       asyncio.events\nimport time:       170 |        170 |                       asyncio.futures\nimport time:       112 |        112 |                       asyncio.protocols\nimport time:       180 |        180 |                         asyncio.transports\nimport time:        63 |         63 |                         asyncio.log\nimport time:       625 |        866 |                       asyncio.sslproto\nimport time:        81 |         81 |                           asyncio.mixins\nimport time:       315 |        396 |                         asyncio.locks\nimport time:       177 |        177 |                           asyncio.queues\nimport time:       265 |        265 |                           asyncio.timeouts\nimport time:       410 |        851 |                         asyncio.tasks\nimport time:       124 |       1369 |                       asyncio.staggered\nimport time:       107 |        107 |                       asyncio.trsock\nimport time:       718 |       9993 |                     asyncio.base_events\nimport time:       205 |        205 |                     asyncio.runners\nimport time:       253 |        253 |                     asyncio.streams\nimport time:       147 |        147 |                     asyncio.subprocess\nimport time:       125 |        125 |                     asyncio.taskgroups\nimport time:        61 |         61 |                     asyncio.threads\nimport time:       178 |        178 |                       asyncio.base_subprocess\nimport time:       535 |        535 |                       asyncio.selector_events\nimport time:       571 |       1283 |                     asyncio.unix_events\nimport time:       153 |      12214 |                   asyncio\nimport time:       229 |        229 |                     greenlet._greenlet\nimport time:       121 |        349 |                   greenlet\nimport time:      1180 |       1180 |                     sqlalchemy.util.langhelpers\nimport time:       241 |       1420 |                   sqlalchemy.util._concurrency_py3k\nimport time:       198 |      14181 |                 sqlalchemy.util.concurrency\nimport time:       193 |        193 |                 sqlalchemy.util.deprecations\nimport time:       226 |      20867 |               sqlalchemy.util\nimport time:       590 |        590 |                                 sqlalchemy.event.registry\nimport time:       155 |        745 |                               sqlalchemy.event.legacy\nimport time:       546 |       1290 |                             sqlalchemy.event.attr\nimport time:       471 |       1760 |                           sqlalchemy.event.base\nimport time:       140 |       1899 |                         sqlalchemy.event.api\nimport time:       111 |       2010 |                       sqlalchemy.event\nimport time:       295 |        295 |                             sqlalchemy.log\nimport time:      1696 |       1990 |                           sqlalchemy.pool.base\nimport time:       823 |       2813 |                         sqlalchemy.pool.events\nimport time:       299 |        299 |                           sqlalchemy.util.queue\nimport time:       327 |        626 |                         sqlalchemy.pool.impl\nimport time:       176 |       3613 |                       sqlalchemy.pool\nimport time:       505 |        505 |                             sqlalchemy.sql.roles\nimport time:       295 |        295 |                             sqlalchemy.inspection\nimport time:      1101 |       1900 |                           sqlalchemy.sql._typing\nimport time:      2617 |       2617 |                             sqlalchemy.sql.visitors\nimport time:       701 |        701 |                             sqlalchemy.sql.cache_key\nimport time:       519 |        519 |                               sqlalchemy.sql.operators\nimport time:       450 |        969 |                             sqlalchemy.sql.traversals\nimport time:      1793 |       6078 |                           sqlalchemy.sql.base\nimport time:       923 |        923 |                             sqlalchemy.sql.coercions\nimport time:       265 |        265 |                                   sqlalchemy.sql.annotation\nimport time:       964 |        964 |                                       sqlalchemy.sql.type_api\nimport time:      3812 |       4776 |                                     sqlalchemy.sql.elements\nimport time:       153 |        153 |                                     sqlalchemy.util.topological\nimport time:      1189 |       6117 |                                   sqlalchemy.sql.ddl\nimport time:       104 |        104 |                                           sqlalchemy.engine._py_processors\nimport time:       112 |        215 |                                         sqlalchemy.engine.processors\nimport time:      1721 |       1936 |                                       sqlalchemy.sql.sqltypes\nimport time:      5399 |       7334 |                                     sqlalchemy.sql.selectable\nimport time:      2762 |      10095 |                                   sqlalchemy.sql.schema\nimport time:       695 |      17170 |                                 sqlalchemy.sql.util\nimport time:      2745 |      19915 |                               sqlalchemy.sql.dml\nimport time:       691 |      20606 |                             sqlalchemy.sql.crud\nimport time:      2162 |       2162 |                             sqlalchemy.sql.functions\nimport time:      4086 |      27776 |                           sqlalchemy.sql.compiler\nimport time:       149 |        149 |                             sqlalchemy.sql._dml_constructors\nimport time:       291 |        291 |                             sqlalchemy.sql._elements_constructors\nimport time:       233 |        233 |                             sqlalchemy.sql._selectable_constructors\nimport time:       622 |        622 |                             sqlalchemy.sql.lambdas\nimport time:       244 |       1537 |                           sqlalchemy.sql.expression\nimport time:       498 |        498 |                             sqlalchemy.sql.events\nimport time:       299 |        796 |                           sqlalchemy.sql.naming\nimport time:       209 |        209 |                           sqlalchemy.sql.default_comparator\nimport time:      4061 |      42354 |                         sqlalchemy.sql\nimport time:        15 |      42369 |                       sqlalchemy.sql.compiler\nimport time:      1983 |      49974 |                     sqlalchemy.engine.interfaces\nimport time:       205 |        205 |                     sqlalchemy.engine.util\nimport time:      1786 |      51963 |                   sqlalchemy.engine.base\nimport time:      1073 |      53036 |                 sqlalchemy.engine.events\nimport time:        91 |         91 |                     sqlalchemy.dialects\nimport time:       651 |        741 |                   sqlalchemy.engine.url\nimport time:       134 |        134 |                   sqlalchemy.engine.mock\nimport time:       516 |       1389 |                 sqlalchemy.engine.create\nimport time:       778 |        778 |                     sqlalchemy.engine.row\nimport time:      1289 |       2067 |                   sqlalchemy.engine.result\nimport time:       717 |       2783 |                 sqlalchemy.engine.cursor\nimport time:      1434 |       1434 |                 sqlalchemy.engine.reflection\nimport time:       189 |      58829 |               sqlalchemy.engine\nimport time:       159 |        159 |               sqlalchemy.schema\nimport time:       121 |        121 |               sqlalchemy.types\nimport time:       144 |        144 |                 sqlalchemy.engine.characteristics\nimport time:       993 |       1137 |               sqlalchemy.engine.default\nimport time:       357 |      81467 |             sqlalchemy\nimport time:       542 |        542 |                 cloudpickle.cloudpickle\nimport time:       127 |        669 |               cloudpickle\nimport time:       357 |       1025 |             optimagic.logging.base\nimport time:        46 |         46 |                           jax\nimport time:        62 |        107 |                         pybaum.config\nimport time:       104 |        210 |                       pybaum.registry_entries\nimport time:        96 |        306 |                     pybaum.registry\nimport time:        87 |         87 |                       pybaum.equality\nimport time:        47 |         47 |                       pybaum.typecheck\nimport time:       121 |        254 |                     pybaum.tree_util\nimport time:        89 |        647 |                   pybaum\nimport time:        69 |         69 |                     optimagic.parameters\nimport time:       133 |        202 |                   optimagic.parameters.tree_registry\nimport time:       390 |        390 |                     difflib\nimport time:       245 |        245 |                         scipy.__config__\nimport time:       351 |        351 |                         scipy.version\nimport time:        22 |         22 |                           scipy._distributor_init_local\nimport time:       109 |        131 |                         scipy._distributor_init\nimport time:        53 |         53 |                             cython\nimport time:       266 |        319 |                           scipy._lib._testutils\nimport time:        69 |        387 |                         scipy._lib\nimport time:       286 |        286 |                         scipy._lib._pep440\nimport time:       279 |        279 |                           scipy._lib._ccallback_c\nimport time:       189 |        467 |                         scipy._lib._ccallback\nimport time:       283 |       2147 |                       scipy\nimport time:      1439 |       1439 |                           scipy.linalg._fblas\nimport time:        29 |         29 |                           scipy.linalg._cblas\nimport time:        17 |         17 |                           scipy.linalg._fblas_64\nimport time:       319 |       1803 |                         scipy.linalg.blas\nimport time:      1050 |       1050 |                           scipy.linalg._flapack\nimport time:        31 |         31 |                           scipy.linalg._clapack\nimport time:        20 |         20 |                           scipy.linalg._flapack_64\nimport time:       575 |       1674 |                         scipy.linalg.lapack\nimport time:       160 |       3635 |                       scipy.linalg._misc\nimport time:       272 |        272 |                         scipy._cyutility\nimport time:       896 |        896 |                         scipy.linalg.cython_lapack\nimport time:       771 |        771 |                                   scipy._lib.array_api_compat.common._typing\nimport time:       347 |       1118 |                                 scipy._lib.array_api_compat.common._helpers\nimport time:       101 |       1218 |                               scipy._lib.array_api_compat.common\nimport time:       111 |       1329 |                             scipy._lib.array_api_compat\nimport time:       148 |        148 |                                   numpy.polynomial.polyutils\nimport time:       282 |        282 |                                   numpy.polynomial._polybase\nimport time:       605 |       1034 |                                 numpy.polynomial.chebyshev\nimport time:       467 |        467 |                                 numpy.polynomial.hermite\nimport time:     24976 |      24976 |                                 numpy.polynomial.hermite_e\nimport time:      1050 |       1050 |                                 numpy.polynomial.laguerre\nimport time:       891 |        891 |                                 numpy.polynomial.legendre\nimport time:      1115 |       1115 |                                 numpy.polynomial.polynomial\nimport time:      1425 |      30956 |                               numpy.polynomial\nimport time:       449 |        449 |                                   numpy._core.strings\nimport time:       211 |        211 |                                   numpy.strings\nimport time:     15984 |      16644 |                                 numpy._core.defchararray\nimport time:       608 |      17251 |                               numpy.char\nimport time:       404 |        404 |                                 numpy.f2py.diagnose\nimport time:       861 |        861 |                                     gettext\nimport time:      1229 |       2090 |                                   argparse\nimport time:       146 |        146 |                                   numpy.f2py._backends\nimport time:       105 |        105 |                                   numpy.f2py.__version__\nimport time:       249 |        249 |                                     numpy.f2py.cfuncs\nimport time:      1015 |       1263 |                                   numpy.f2py.auxfuncs\nimport time:      1982 |       1982 |                                     numpy.f2py.cb_rules\nimport time:       145 |        145 |                                     numpy.f2py._isocbind\nimport time:       181 |        181 |                                       fileinput\nimport time:      1135 |       1135 |                                             charset_normalizer.constant\nimport time:       304 |        304 |                                                 _multibytecodec\nimport time:       391 |        695 |                                               charset_normalizer.utils\nimport time:       542 |       1236 |                                             charset_normalizer.md\nimport time:       495 |        495 |                                             charset_normalizer.models\nimport time:       628 |       3492 |                                           charset_normalizer.cd\nimport time:       423 |       3915 |                                         charset_normalizer.api\nimport time:       202 |        202 |                                         charset_normalizer.legacy\nimport time:       100 |        100 |                                         charset_normalizer.version\nimport time:       302 |       4517 |                                       charset_normalizer\nimport time:      2016 |       2016 |                                       numpy.f2py.symbolic\nimport time:     11019 |      17732 |                                     numpy.f2py.crackfortran\nimport time:       509 |      20367 |                                   numpy.f2py.capi_maps\nimport time:       231 |        231 |                                     numpy.f2py.func2subr\nimport time:       266 |        496 |                                   numpy.f2py.f90mod_rules\nimport time:       182 |        182 |                                     numpy.f2py.common_rules\nimport time:       152 |        152 |                                     numpy.f2py.use_rules\nimport time:      6012 |       6344 |                                   numpy.f2py.rules\nimport time:       706 |      31514 |                                 numpy.f2py.f2py2e\nimport time:       236 |      32153 |                               numpy.f2py\nimport time:       291 |        291 |                                 numpy.fft._helper\nimport time:       209 |        209 |                                   numpy.fft._pocketfft_umath\nimport time:       319 |        527 |                                 numpy.fft._pocketfft\nimport time:       163 |        163 |                                 numpy.fft.helper\nimport time:       174 |       1155 |                               numpy.fft\nimport time:       308 |        308 |                                 numpy.core._utils\nimport time:       140 |        448 |                               numpy.core\nimport time:        91 |         91 |                               numpy.rec\nimport time:       190 |        190 |                                     unittest.util\nimport time:       224 |        414 |                                   unittest.result\nimport time:       521 |        521 |                                   unittest.case\nimport time:       269 |        269 |                                   unittest.suite\nimport time:       375 |        375 |                                   unittest.loader\nimport time:        82 |         82 |                                       unittest.signals\nimport time:       160 |        242 |                                     unittest.runner\nimport time:       189 |        430 |                                   unittest.main\nimport time:       416 |       2422 |                                 unittest\nimport time:       208 |        208 |                                 numpy.testing._private\nimport time:       152 |        152 |                                 numpy.testing.overrides\nimport time:       203 |        203 |                                 numpy.testing._private.extbuild\nimport time:      2147 |       2147 |                                 numpy.testing._private.utils\nimport time:       140 |       5270 |                               numpy.testing\nimport time:       828 |        828 |                                 numpy.ctypeslib._ctypeslib\nimport time:       167 |        995 |                               numpy.ctypeslib\nimport time:       153 |        153 |                                 scipy._lib.array_api_compat._internal\nimport time:       903 |        903 |                                 scipy._lib.array_api_compat.common._aliases\nimport time:       272 |        272 |                                   scipy._lib.array_api_compat.numpy._typing\nimport time:       166 |        437 |                                 scipy._lib.array_api_compat.numpy._info\nimport time:      3861 |       5352 |                               scipy._lib.array_api_compat.numpy._aliases\nimport time:       906 |        906 |                                 scipy._lib.array_api_compat.common._linalg\nimport time:      1321 |       2227 |                               scipy._lib.array_api_compat.numpy.linalg\nimport time:       381 |        381 |                                 scipy._lib.array_api_compat.common._fft\nimport time:      1661 |       2042 |                               scipy._lib.array_api_compat.numpy.fft\nimport time:       386 |      98321 |                             scipy._lib.array_api_compat.numpy\nimport time:       105 |        105 |                             scipy._lib._sparse\nimport time:       242 |        242 |                                 pkgutil\nimport time:       187 |        187 |                                   _pyrepl\nimport time:       169 |        355 |                                 _pyrepl.pager\nimport time:      1156 |       1752 |                               pydoc\nimport time:      1507 |       3258 |                             scipy._lib._docscrape\nimport time:      3063 |     106073 |                           scipy._lib._array_api\nimport time:       619 |     106692 |                         scipy._lib._util\nimport time:      1098 |     108955 |                       scipy.linalg._cythonized_array_utils\nimport time:      2917 |       2917 |                         scipy.linalg._decomp\nimport time:      1223 |       1223 |                         scipy.linalg._decomp_svd\nimport time:       236 |        236 |                         scipy.linalg._solve_toeplitz\nimport time:      2358 |       6733 |                       scipy.linalg._basic\nimport time:       164 |        164 |                         scipy.linalg._decomp_lu_cython\nimport time:       547 |        711 |                       scipy.linalg._decomp_lu\nimport time:       443 |        443 |                       scipy.linalg._decomp_ldl\nimport time:       683 |        683 |                       scipy.linalg._decomp_cholesky\nimport time:       803 |        803 |                       scipy.linalg._decomp_qr\nimport time:       782 |        782 |                       scipy.linalg._decomp_qz\nimport time:       659 |        659 |                       scipy.linalg._decomp_schur\nimport time:       262 |        262 |                       scipy.linalg._decomp_polar\nimport time:       116 |        116 |                         scipy._lib.deprecation\nimport time:       513 |        513 |                         scipy.linalg._expm_frechet\nimport time:       393 |        393 |                         scipy.linalg._matfuncs_schur_sqrtm\nimport time:       357 |        357 |                         scipy.linalg._matfuncs_expm\nimport time:       188 |        188 |                         scipy.linalg._linalg_pythran\nimport time:      1657 |       3222 |                       scipy.linalg._matfuncs\nimport time:       349 |        349 |                       scipy.linalg._special_matrices\nimport time:      1124 |       1124 |                       scipy.linalg._solvers\nimport time:       302 |        302 |                       scipy.linalg._procrustes\nimport time:       286 |        286 |                         scipy.linalg.cython_blas\nimport time:      1199 |       1484 |                       scipy.linalg._decomp_update\nimport time:       283 |        283 |                             scipy.sparse._sputils\nimport time:       156 |        156 |                             scipy.sparse._matrix\nimport time:       612 |       1049 |                           scipy.sparse._base\nimport time:       164 |        164 |                             scipy.sparse._sparsetools\nimport time:       247 |        247 |                               scipy.sparse._data\nimport time:       150 |        150 |                               scipy.sparse._index\nimport time:       496 |        892 |                             scipy.sparse._compressed\nimport time:       222 |       1276 |                           scipy.sparse._csr\nimport time:       174 |        174 |                           scipy.sparse._csc\nimport time:       423 |        423 |                             scipy.sparse._csparsetools\nimport time:       290 |        713 |                           scipy.sparse._lil\nimport time:       309 |        309 |                           scipy.sparse._dok\nimport time:       658 |        658 |                           scipy.sparse._coo\nimport time:       257 |        257 |                           scipy.sparse._dia\nimport time:       309 |        309 |                           scipy.sparse._bsr\nimport time:       924 |        924 |                           scipy.sparse._construct\nimport time:       116 |        116 |                           scipy.sparse._extract\nimport time:        76 |         76 |                           scipy.sparse._matrix_io\nimport time:        53 |         53 |                           scipy.sparse.base\nimport time:        51 |         51 |                           scipy.sparse.bsr\nimport time:        44 |         44 |                           scipy.sparse.compressed\nimport time:        48 |         48 |                           scipy.sparse.construct\nimport time:        69 |         69 |                           scipy.sparse.coo\nimport time:        49 |         49 |                           scipy.sparse.csc\nimport time:        47 |         47 |                           scipy.sparse.csr\nimport time:        46 |         46 |                           scipy.sparse.data\nimport time:        45 |         45 |                           scipy.sparse.dia\nimport time:        80 |         80 |                           scipy.sparse.dok\nimport time:        45 |         45 |                           scipy.sparse.extract\nimport time:        72 |         72 |                           scipy.sparse.lil\nimport time:        48 |         48 |                           scipy.sparse.sparsetools\nimport time:        43 |         43 |                           scipy.sparse.sputils\nimport time:       392 |       6981 |                         scipy.sparse\nimport time:       467 |       7447 |                       scipy.linalg._sketches\nimport time:       155 |        155 |                       scipy.linalg._decomp_cossin\nimport time:        60 |         60 |                       scipy.linalg.decomp\nimport time:        50 |         50 |                       scipy.linalg.decomp_cholesky\nimport time:        48 |         48 |                       scipy.linalg.decomp_lu\nimport time:        44 |         44 |                       scipy.linalg.decomp_qr\nimport time:        45 |         45 |                       scipy.linalg.decomp_svd\nimport time:        45 |         45 |                       scipy.linalg.decomp_schur\nimport time:        45 |         45 |                       scipy.linalg.basic\nimport time:        70 |         70 |                       scipy.linalg.misc\nimport time:        47 |         47 |                       scipy.linalg.special_matrices\nimport time:        47 |         47 |                       scipy.linalg.matfuncs\nimport time:       411 |     140795 |                     scipy.linalg\nimport time:       141 |     141324 |                   optimagic.utilities\nimport time:      1851 |     144023 |                 optimagic.optimization.fun_value\nimport time:      2682 |     146704 |               optimagic.logging.types\nimport time:       689 |     147393 |             optimagic.logging.sqlalchemy\nimport time:       403 |     230286 |           optimagic.logging.logger\nimport time:        68 |     230353 |         optimagic.logging\nimport time:        24 |     230376 |       optimagic.logging.types\nimport time:       963 |        963 |         optimagic.timing\nimport time:       794 |       1757 |       optimagic.optimization.history\nimport time:       130 |        130 |                 _multiprocessing\nimport time:       205 |        205 |                     multiprocessing.process\nimport time:       185 |        185 |                     multiprocessing.reduction\nimport time:       342 |        732 |                   multiprocessing.context\nimport time:       169 |        900 |                 multiprocessing\nimport time:       168 |       1198 |               joblib._multiprocessing_helpers\nimport time:        69 |         69 |                 joblib.externals\nimport time:        85 |         85 |                 joblib.externals.loky._base\nimport time:       177 |        177 |                       multiprocessing.util\nimport time:       201 |        378 |                     multiprocessing.synchronize\nimport time:       320 |        320 |                           _queue\nimport time:       232 |        552 |                         queue\nimport time:        50 |         50 |                           _winapi\nimport time:       388 |        438 |                         multiprocessing.connection\nimport time:       178 |        178 |                         multiprocessing.queues\nimport time:       312 |       1478 |                       concurrent.futures.process\nimport time:       115 |        115 |                       joblib.externals.loky.backend.process\nimport time:       165 |       1757 |                     joblib.externals.loky.backend.context\nimport time:        67 |       2200 |                   joblib.externals.loky.backend\nimport time:        16 |       2216 |                 joblib.externals.loky.backend.context\nimport time:        71 |         71 |                   joblib.externals.loky.backend._posix_reduction\nimport time:       344 |        344 |                     joblib.externals.cloudpickle.cloudpickle\nimport time:        93 |        437 |                   joblib.externals.cloudpickle\nimport time:       166 |        673 |                 joblib.externals.loky.backend.reduction\nimport time:        44 |         44 |                     faulthandler\nimport time:       125 |        125 |                     joblib.externals.loky.backend.queues\nimport time:        41 |         41 |                       psutil\nimport time:        83 |        124 |                     joblib.externals.loky.backend.utils\nimport time:        70 |         70 |                     joblib.externals.loky.initializers\nimport time:        38 |         38 |                     psutil\nimport time:       439 |        837 |                   joblib.externals.loky.process_executor\nimport time:       141 |        978 |                 joblib.externals.loky.reusable_executor\nimport time:       103 |        103 |                 joblib.externals.loky.cloudpickle_wrapper\nimport time:       166 |       4286 |               joblib.externals.loky\nimport time:        93 |       5576 |             joblib._cloudpickle_wrapper\nimport time:      2355 |       2355 |               joblib._utils\nimport time:       418 |        418 |               multiprocessing.pool\nimport time:       189 |        189 |                   joblib.backports\nimport time:        80 |         80 |                   joblib.disk\nimport time:       114 |        114 |                         runpy\nimport time:       120 |        234 |                       multiprocessing.spawn\nimport time:       194 |        194 |                       _posixshmem\nimport time:       169 |        596 |                     multiprocessing.resource_tracker\nimport time:       110 |        110 |                     joblib.externals.loky.backend.spawn\nimport time:       160 |        865 |                   joblib.externals.loky.backend.resource_tracker\nimport time:        47 |         47 |                       lz4\nimport time:       313 |        359 |                     joblib.compressor\nimport time:        94 |         94 |                       joblib.numpy_pickle_utils\nimport time:       137 |        230 |                     joblib.numpy_pickle_compat\nimport time:       222 |        811 |                   joblib.numpy_pickle\nimport time:       219 |       2162 |                 joblib._memmapping_reducer\nimport time:       131 |       2293 |               joblib.executor\nimport time:       165 |        165 |               joblib.pool\nimport time:       303 |       5532 |             joblib._parallel_backends\nimport time:        95 |         95 |               joblib.logger\nimport time:       349 |        444 |             joblib._store_backends\nimport time:       142 |        142 |             joblib.hashing\nimport time:       205 |        205 |               joblib.func_inspect\nimport time:       518 |        723 |             joblib.memory\nimport time:       363 |        363 |             joblib.parallel\nimport time:       152 |      12928 |           joblib\nimport time:        44 |         44 |             pathos\nimport time:        16 |         60 |           pathos.pools\nimport time:       240 |        240 |                         scipy.sparse.linalg._interface\nimport time:       110 |        110 |                         scipy.sparse.linalg._isolve.utils\nimport time:       148 |        497 |                       scipy.sparse.linalg._isolve.iterative\nimport time:        86 |         86 |                       scipy.sparse.linalg._isolve.minres\nimport time:        84 |         84 |                         scipy.sparse.linalg._isolve._gcrotmk\nimport time:        81 |        164 |                       scipy.sparse.linalg._isolve.lgmres\nimport time:        78 |         78 |                       scipy.sparse.linalg._isolve.lsqr\nimport time:        74 |         74 |                       scipy.sparse.linalg._isolve.lsmr\nimport time:        56 |         56 |                       scipy.sparse.linalg._isolve.tfqmr\nimport time:       111 |       1063 |                     scipy.sparse.linalg._isolve\nimport time:       396 |        396 |                         scipy.sparse.linalg._dsolve._superlu\nimport time:        65 |         65 |                           scikits\nimport time:        20 |         84 |                         scikits.umfpack\nimport time:       242 |        722 |                       scipy.sparse.linalg._dsolve.linsolve\nimport time:        79 |         79 |                       scipy.sparse.linalg._dsolve._add_newdocs\nimport time:        85 |        885 |                     scipy.sparse.linalg._dsolve\nimport time:       445 |        445 |                             scipy._lib.decorator\nimport time:       334 |        778 |                           scipy._lib._threadsafety\nimport time:       647 |        647 |                           scipy.sparse.linalg._eigen.arpack._arpack\nimport time:       384 |       1808 |                         scipy.sparse.linalg._eigen.arpack.arpack\nimport time:        80 |       1888 |                       scipy.sparse.linalg._eigen.arpack\nimport time:       168 |        168 |                         scipy.sparse.linalg._eigen.lobpcg.lobpcg\nimport time:       107 |        275 |                       scipy.sparse.linalg._eigen.lobpcg\nimport time:        47 |         47 |                           scipy.sparse.linalg._propack\nimport time:       418 |        418 |                           scipy.sparse.linalg._propack._spropack\nimport time:       336 |        336 |                           scipy.sparse.linalg._propack._dpropack\nimport time:       331 |        331 |                           scipy.sparse.linalg._propack._cpropack\nimport time:       333 |        333 |                           scipy.sparse.linalg._propack._zpropack\nimport time:       170 |       1633 |                         scipy.sparse.linalg._svdp\nimport time:       787 |       2419 |                       scipy.sparse.linalg._eigen._svds\nimport time:        92 |       4673 |                     scipy.sparse.linalg._eigen\nimport time:       105 |        105 |                         scipy.sparse.linalg._onenormest\nimport time:       170 |        275 |                       scipy.sparse.linalg._expm_multiply\nimport time:       216 |        491 |                     scipy.sparse.linalg._matfuncs\nimport time:        76 |         76 |                     scipy.sparse.linalg._norm\nimport time:       200 |        200 |                     scipy.sparse.linalg._special_sparse_arrays\nimport time:        61 |         61 |                     scipy.sparse.linalg.isolve\nimport time:        86 |         86 |                     scipy.sparse.linalg.dsolve\nimport time:        48 |         48 |                     scipy.sparse.linalg.interface\nimport time:        44 |         44 |                     scipy.sparse.linalg.eigen\nimport time:        45 |         45 |                     scipy.sparse.linalg.matfuncs\nimport time:       169 |       7834 |                   scipy.sparse.linalg\nimport time:        86 |         86 |                     scipy.optimize._dcsrch\nimport time:       150 |        236 |                   scipy.optimize._linesearch\nimport time:       142 |        142 |                     scipy.optimize._group_columns\nimport time:        83 |         83 |                         scipy._lib.array_api_extra._lib\nimport time:        51 |         51 |                             scipy._lib.array_api_extra._lib._utils\nimport time:       102 |        102 |                               scipy._lib._array_api_compat_vendor\nimport time:       116 |        217 |                             scipy._lib.array_api_extra._lib._utils._compat\nimport time:       116 |        116 |                               scipy._lib.array_api_extra._lib._utils._typing\nimport time:       226 |        342 |                             scipy._lib.array_api_extra._lib._utils._helpers\nimport time:       269 |        877 |                           scipy._lib.array_api_extra._lib._at\nimport time:       381 |       1258 |                         scipy._lib.array_api_extra._lib._funcs\nimport time:       130 |       1470 |                       scipy._lib.array_api_extra._delegation\nimport time:       125 |        125 |                       scipy._lib.array_api_extra._lib._lazy\nimport time:        89 |       1684 |                     scipy._lib.array_api_extra\nimport time:       204 |       2029 |                   scipy.optimize._numdiff\nimport time:       182 |        182 |                     scipy.optimize._hessian_update_strategy\nimport time:       259 |        441 |                   scipy.optimize._differentiable_functions\nimport time:      1471 |      12010 |                 scipy.optimize._optimize\nimport time:       112 |        112 |                     scipy.optimize._trustregion\nimport time:       101 |        213 |                   scipy.optimize._trustregion_dogleg\nimport time:       122 |        122 |                   scipy.optimize._trustregion_ncg\nimport time:       149 |        149 |                         scipy._lib.messagestream\nimport time:       255 |        403 |                       scipy.optimize._trlib._trlib\nimport time:        60 |        463 |                     scipy.optimize._trlib\nimport time:        55 |        517 |                   scipy.optimize._trustregion_krylov\nimport time:       165 |        165 |                   scipy.optimize._trustregion_exact\nimport time:       166 |        166 |                       scipy.optimize._constraints\nimport time:        40 |         40 |                             sksparse\nimport time:        18 |         57 |                           sksparse.cholmod\nimport time:       100 |        156 |                         scipy.optimize._trustregion_constr.projections\nimport time:        92 |         92 |                         scipy.optimize._trustregion_constr.qp_subproblem\nimport time:       118 |        365 |                       scipy.optimize._trustregion_constr.equality_constrained_sqp\nimport time:       134 |        134 |                       scipy.optimize._trustregion_constr.canonical_constraint\nimport time:       114 |        114 |                       scipy.optimize._trustregion_constr.tr_interior_point\nimport time:       126 |        126 |                       scipy.optimize._trustregion_constr.report\nimport time:       218 |       1119 |                     scipy.optimize._trustregion_constr.minimize_trustregion_constr\nimport time:        71 |       1190 |                   scipy.optimize._trustregion_constr\nimport time:       295 |        295 |                     scipy.optimize._lbfgsb\nimport time:       152 |        447 |                   scipy.optimize._lbfgsb_py\nimport time:       133 |        133 |                     scipy.optimize._moduleTNC\nimport time:       166 |        299 |                   scipy.optimize._tnc\nimport time:       112 |        112 |                   scipy.optimize._cobyla_py\nimport time:        61 |         61 |                   scipy.optimize._cobyqa_py\nimport time:       279 |        279 |                     scipy.optimize._slsqplib\nimport time:       146 |        425 |                   scipy.optimize._slsqp_py\nimport time:       263 |       3809 |                 scipy.optimize._minimize\nimport time:       120 |        120 |                     scipy.optimize._minpack\nimport time:       350 |        350 |                           scipy.optimize._lsq.common\nimport time:       123 |        473 |                         scipy.optimize._lsq.trf\nimport time:       101 |        101 |                         scipy.optimize._lsq.dogbox\nimport time:       260 |        832 |                       scipy.optimize._lsq.least_squares\nimport time:       127 |        127 |                           scipy.optimize._lsq.givens_elimination\nimport time:        97 |        224 |                         scipy.optimize._lsq.trf_linear\nimport time:        93 |         93 |                         scipy.optimize._lsq.bvls\nimport time:       107 |        423 |                       scipy.optimize._lsq.lsq_linear\nimport time:       101 |       1355 |                     scipy.optimize._lsq\nimport time:       234 |       1708 |                   scipy.optimize._minpack_py\nimport time:       107 |        107 |                   scipy.optimize._spectral\nimport time:      1105 |       1105 |                   scipy.optimize._nonlin\nimport time:       166 |       3085 |                 scipy.optimize._root\nimport time:       105 |        105 |                     scipy.optimize._zeros\nimport time:       282 |        387 |                   scipy.optimize._zeros_py\nimport time:       136 |        522 |                 scipy.optimize._root_scalar\nimport time:       305 |        305 |                 scipy.optimize._nnls\nimport time:       889 |        889 |                 scipy.optimize._basinhopping\nimport time:        51 |         51 |                       scipy.optimize._highspy\nimport time:      3149 |       3149 |                       scipy.optimize._highspy._core\nimport time:       262 |        262 |                       scipy.optimize._highspy._highs_options\nimport time:       177 |       3638 |                     scipy.optimize._highspy._highs_wrapper\nimport time:       108 |       3746 |                   scipy.optimize._linprog_highs\nimport time:        72 |         72 |                                   uarray\nimport time:       257 |        257 |                                       scipy._lib._uarray._uarray\nimport time:       277 |        534 |                                     scipy._lib._uarray._backend\nimport time:       146 |        679 |                                   scipy._lib._uarray\nimport time:       107 |        857 |                                 scipy._lib.uarray\nimport time:       523 |       1380 |                               scipy.fft._basic\nimport time:       327 |        327 |                               scipy.fft._realtransforms\nimport time:       123 |        123 |                                     scipy.special._sf_error\nimport time:       255 |        255 |                                       scipy.special._ufuncs_cxx\nimport time:       159 |        159 |                                       scipy.special._ellip_harm_2\nimport time:       402 |        402 |                                       scipy.special._special_ufuncs\nimport time:       264 |        264 |                                       scipy.special._gufuncs\nimport time:       607 |       1685 |                                     scipy.special._ufuncs\nimport time:      5017 |       5017 |                                     scipy.special._support_alternative_backends\nimport time:        69 |         69 |                                       scipy.special._input_validation\nimport time:       457 |        457 |                                       scipy.special._specfun\nimport time:       138 |        138 |                                       scipy.special._comb\nimport time:       283 |        283 |                                       scipy.special._multiufuncs\nimport time:       465 |       1410 |                                     scipy.special._basic\nimport time:       825 |        825 |                                     scipy.special._logsumexp\nimport time:       370 |        370 |                                     scipy.special._orthogonal\nimport time:        99 |         99 |                                     scipy.special._spfun_stats\nimport time:        82 |         82 |                                     scipy.special._ellip_harm\nimport time:        55 |         55 |                                     scipy.special._lambertw\nimport time:        88 |         88 |                                     scipy.special._spherical_bessel\nimport time:        55 |         55 |                                     scipy.special.add_newdocs\nimport time:        70 |         70 |                                     scipy.special.basic\nimport time:        88 |         88 |                                     scipy.special.orthogonal\nimport time:        48 |         48 |                                     scipy.special.specfun\nimport time:        43 |         43 |                                     scipy.special.sf_error\nimport time:        45 |         45 |                                     scipy.special.spfun_stats\nimport time:       443 |      10539 |                                   scipy.special\nimport time:       130 |      10668 |                                 scipy.fft._fftlog_backend\nimport time:       154 |      10821 |                               scipy.fft._fftlog\nimport time:       228 |        228 |                                     scipy.fft._pocketfft.pypocketfft\nimport time:       129 |        129 |                                     scipy.fft._pocketfft.helper\nimport time:       200 |        556 |                                   scipy.fft._pocketfft.basic\nimport time:       134 |        134 |                                   scipy.fft._pocketfft.realtransforms\nimport time:       133 |        822 |                                 scipy.fft._pocketfft\nimport time:       213 |       1035 |                               scipy.fft._helper\nimport time:       179 |        179 |                                 scipy.fft._basic_backend\nimport time:       150 |        150 |                                 scipy.fft._realtransforms_backend\nimport time:       172 |        499 |                               scipy.fft._backend\nimport time:       144 |      14204 |                             scipy.fft\nimport time:       318 |      14521 |                           scipy.linalg._decomp_interpolative\nimport time:       115 |      14635 |                         scipy.linalg.interpolative\nimport time:       112 |      14747 |                       scipy.optimize._remove_redundancy\nimport time:       295 |      15041 |                     scipy.optimize._linprog_util\nimport time:        52 |         52 |                     sksparse\nimport time:        34 |         34 |                       scikits\nimport time:        14 |         47 |                     scikits.umfpack\nimport time:       197 |      15335 |                   scipy.optimize._linprog_ip\nimport time:       110 |        110 |                   scipy.optimize._linprog_simplex\nimport time:       227 |        227 |                     scipy.optimize._bglu_dense\nimport time:       149 |        376 |                   scipy.optimize._linprog_rs\nimport time:       131 |        131 |                   scipy.optimize._linprog_doc\nimport time:       153 |      19848 |                 scipy.optimize._linprog\nimport time:       115 |        115 |                 scipy.optimize._lsap\nimport time:      1463 |       1463 |                 scipy.optimize._differentialevolution\nimport time:       183 |        183 |                   scipy.optimize._pava_pybind\nimport time:       141 |        324 |                 scipy.optimize._isotonic\nimport time:       360 |        360 |                     scipy.spatial._ckdtree\nimport time:       404 |        764 |                   scipy.spatial._kdtree\nimport time:       414 |        414 |                   scipy.spatial._qhull\nimport time:       148 |        148 |                     scipy.spatial._voronoi\nimport time:       213 |        361 |                   scipy.spatial._spherical_voronoi\nimport time:       421 |        421 |                   scipy.spatial._plotutils\nimport time:        78 |         78 |                   scipy.spatial._procrustes\nimport time:       153 |        153 |                       scipy.spatial._hausdorff\nimport time:       256 |        256 |                       scipy.spatial._distance_pybind\nimport time:       122 |        122 |                       scipy.spatial._distance_wrap\nimport time:      1994 |       2523 |                     scipy.spatial.distance\nimport time:       116 |       2639 |                   scipy.spatial._geometric_slerp\nimport time:        82 |         82 |                   scipy.spatial.ckdtree\nimport time:        57 |         57 |                   scipy.spatial.kdtree\nimport time:        50 |         50 |                   scipy.spatial.qhull\nimport time:      2752 |       2752 |                             scipy.constants._codata\nimport time:       904 |        904 |                             scipy.constants._constants\nimport time:       146 |        146 |                             scipy.constants.codata\nimport time:       170 |        170 |                             scipy.constants.constants\nimport time:       567 |       4538 |                           scipy.constants\nimport time:        95 |       4632 |                         scipy.spatial.transform._rotation_groups\nimport time:       634 |       5266 |                       scipy.spatial.transform._rotation\nimport time:       309 |       5574 |                     scipy.spatial.transform._rigid_transform\nimport time:       138 |        138 |                     scipy.spatial.transform._rotation_spline\nimport time:        59 |         59 |                     scipy.spatial.transform.rotation\nimport time:        91 |       5862 |                   scipy.spatial.transform\nimport time:        58 |         58 |                     scipy.optimize._shgo_lib\nimport time:       256 |        256 |                     scipy.optimize._shgo_lib._vertex\nimport time:       231 |        544 |                   scipy.optimize._shgo_lib._complex\nimport time:       495 |      11760 |                 scipy.optimize._shgo\nimport time:       694 |        694 |                 scipy.optimize._dual_annealing\nimport time:       156 |        156 |                 scipy.optimize._qap\nimport time:       116 |        116 |                   scipy.optimize._direct\nimport time:       237 |        352 |                 scipy.optimize._direct_py\nimport time:       116 |        116 |                 scipy.optimize._milp\nimport time:        56 |         56 |                 scipy.optimize.cobyla\nimport time:        49 |         49 |                 scipy.optimize.lbfgsb\nimport time:        50 |         50 |                 scipy.optimize.linesearch\nimport time:        54 |         54 |                 scipy.optimize.minpack\nimport time:        46 |         46 |                 scipy.optimize.minpack2\nimport time:        44 |         44 |                 scipy.optimize.moduleTNC\nimport time:        46 |         46 |                 scipy.optimize.nonlin\nimport time:        49 |         49 |                 scipy.optimize.optimize\nimport time:        67 |         67 |                 scipy.optimize.slsqp\nimport time:        50 |         50 |                 scipy.optimize.tnc\nimport time:        47 |         47 |                 scipy.optimize.zeros\nimport time:       335 |      56331 |               scipy.optimize\nimport time:       653 |      56984 |             optimagic.parameters.bounds\nimport time:       238 |      57221 |           optimagic.deprecations\nimport time:        73 |         73 |                   _plotly_utils\nimport time:       120 |        192 |                 _plotly_utils.importers\nimport time:       117 |        309 |               plotly\nimport time:        57 |         57 |                 _plotly_utils.optional_imports\nimport time:        49 |        105 |               plotly.optional_imports\nimport time:       155 |        155 |                 plotly.graph_objs\nimport time:       112 |        112 |                   PIL._version\nimport time:       944 |       1056 |                 _plotly_utils.basevalidators\nimport time:       293 |        293 |                   plotly.io\nimport time:       141 |        141 |                   plotly.express._special_inputs\nimport time:        83 |         83 |                   plotly.express.trendline_functions\nimport time:       330 |        330 |                       _plotly_utils.exceptions\nimport time:        80 |         80 |                         _plotly_utils.colors._swatches\nimport time:       104 |        104 |                         _plotly_utils.colors.colorbrewer\nimport time:       101 |        101 |                         _plotly_utils.colors.carto\nimport time:       168 |        451 |                       _plotly_utils.colors.qualitative\nimport time:        70 |         70 |                         _plotly_utils.colors.plotlyjs\nimport time:        93 |         93 |                         _plotly_utils.colors.cmocean\nimport time:       161 |        323 |                       _plotly_utils.colors.sequential\nimport time:        86 |         86 |                       _plotly_utils.colors.diverging\nimport time:        91 |         91 |                       _plotly_utils.colors.cyclical\nimport time:      2031 |       3309 |                     _plotly_utils.colors\nimport time:        96 |       3404 |                   plotly.colors\nimport time:        76 |         76 |                   packaging\nimport time:        83 |         83 |                     packaging._structures\nimport time:      1237 |       1319 |                   packaging.version\nimport time:       402 |        402 |                   plotly._subplots\nimport time:       347 |        347 |                     _plotly_utils.utils\nimport time:       138 |        138 |                     plotly.shapeannotation\nimport time:       996 |       1480 |                   plotly.basedatatypes\nimport time:      3316 |      10511 |                 plotly.express._core\nimport time:       170 |        170 |                 plotly.express.imshow_utils\nimport time:       699 |        699 |                     _plotly_utils.png\nimport time:      2398 |       2398 |                       PIL.ExifTags\nimport time:        82 |         82 |                         PIL._deprecate\nimport time:       382 |        464 |                       PIL.ImageMode\nimport time:       771 |        771 |                       PIL.TiffTags\nimport time:        98 |         98 |                       PIL._binary\nimport time:       201 |        201 |                         PIL._typing\nimport time:       215 |        416 |                       PIL._util\nimport time:        67 |         67 |                       defusedxml\nimport time:      2538 |       2538 |                       PIL._imaging\nimport time:      1792 |       8541 |                     PIL.Image\nimport time:        97 |       9335 |                   _plotly_utils.data_utils\nimport time:       138 |       9473 |                 plotly.utils\nimport time:       108 |        108 |                 xarray\nimport time:      1794 |      23265 |               plotly.express._imshow\nimport time:       224 |        224 |                 plotly.express._doc\nimport time:     32561 |      32785 |               plotly.express._chart_types\nimport time:       133 |        133 |                 plotly.data\nimport time:       171 |        303 |               plotly.express.data\nimport time:       195 |        195 |               plotly.express.colors\nimport time:       198 |      57157 |             plotly.express\nimport time:        71 |         71 |             petsc4py\nimport time:        38 |         38 |             nlopt\nimport time:        31 |         31 |             pybobyqa\nimport time:        29 |         29 |             dfols\nimport time:        30 |         30 |             pygmo\nimport time:        30 |         30 |             cyipopt\nimport time:        30 |         30 |             fides\nimport time:        30 |         30 |             jax\nimport time:        31 |         31 |             tranquilo\nimport time:        31 |         31 |             numba\nimport time:       118 |        118 |                       iminuit.pdg_format\nimport time:       110 |        110 |                         iminuit.warnings\nimport time:       126 |        236 |                       iminuit._optional_dependencies\nimport time:       167 |        519 |                     iminuit._repr_text\nimport time:       141 |        660 |                   iminuit._repr_html\nimport time:        88 |         88 |                     iminuit._parse_version\nimport time:       127 |        127 |                         quopri\nimport time:       175 |        175 |                           email._parseaddr\nimport time:       474 |        648 |                         email.utils\nimport time:       309 |        309 |                         email.errors\nimport time:       171 |        171 |                             email.quoprimime\nimport time:        93 |         93 |                             email.base64mime\nimport time:        98 |         98 |                               email.encoders\nimport time:       250 |        348 |                             email.charset\nimport time:      2458 |       3068 |                           email.header\nimport time:       391 |       3458 |                         email._policybase\nimport time:       211 |        211 |                         email._encoded_words\nimport time:        92 |         92 |                         email.iterators\nimport time:       506 |       5347 |                       email.message\nimport time:        98 |         98 |                       importlib.metadata._text\nimport time:       170 |       5614 |                     importlib.metadata._adapters\nimport time:       390 |        390 |                       email.feedparser\nimport time:       136 |        526 |                     email.parser\nimport time:       692 |       6919 |                   iminuit._deprecated\nimport time:      1273 |       1273 |                   iminuit.typing\nimport time:       649 |       9499 |                 iminuit.util\nimport time:       965 |        965 |                 iminuit._core\nimport time:       926 |      11389 |               iminuit.minuit\nimport time:       137 |        137 |               iminuit.minimize\nimport time:       464 |      11989 |             iminuit\nimport time:       187 |        187 |             nevergrad\nimport time:       163 |        163 |                   scipy.stats._warnings_errors\nimport time:       217 |        217 |                         scipy._lib.doccer\nimport time:       164 |        164 |                         scipy.stats._distr_params\nimport time:       567 |        567 |                         scipy.integrate._quadrature\nimport time:       446 |        446 |                           scipy.integrate._odepack\nimport time:       139 |        585 |                         scipy.integrate._odepack_py\nimport time:       174 |        174 |                           scipy.integrate._quadpack\nimport time:       222 |        396 |                         scipy.integrate._quadpack_py\nimport time:       412 |        412 |                           scipy.integrate._vode\nimport time:       220 |        220 |                           scipy.integrate._dop\nimport time:       368 |        368 |                           scipy.integrate._lsoda\nimport time:       377 |       1375 |                         scipy.integrate._ode\nimport time:       215 |        215 |                         scipy.integrate._bvp\nimport time:       173 |        173 |                               scipy.integrate._ivp.common\nimport time:       148 |        148 |                               scipy.integrate._ivp.base\nimport time:       329 |        648 |                             scipy.integrate._ivp.bdf\nimport time:       337 |        337 |                             scipy.integrate._ivp.radau\nimport time:       111 |        111 |                               scipy.integrate._ivp.dop853_coefficients\nimport time:       389 |        500 |                             scipy.integrate._ivp.rk\nimport time:       219 |        219 |                             scipy.integrate._ivp.lsoda\nimport time:       216 |       1917 |                           scipy.integrate._ivp.ivp\nimport time:        90 |       2007 |                         scipy.integrate._ivp\nimport time:       230 |        230 |                         scipy.integrate._quad_vec\nimport time:       131 |        131 |                           scipy._lib._elementwise_iterative_method\nimport time:       324 |        454 |                         scipy.integrate._tanhsinh\nimport time:       224 |        224 |                             scipy.integrate._rules._base\nimport time:       174 |        174 |                             scipy.integrate._rules._genz_malik\nimport time:        74 |         74 |                               scipy.integrate._rules._gauss_legendre\nimport time:       136 |        210 |                             scipy.integrate._rules._gauss_kronrod\nimport time:       132 |        738 |                           scipy.integrate._rules\nimport time:      1058 |       1796 |                         scipy.integrate._cubature\nimport time:       330 |        330 |                         scipy.integrate._lebedev\nimport time:        74 |         74 |                         scipy.integrate.dop\nimport time:        53 |         53 |                         scipy.integrate.lsoda\nimport time:       327 |        327 |                         scipy.integrate.vode\nimport time:        56 |         56 |                         scipy.integrate.odepack\nimport time:        48 |         48 |                         scipy.integrate.quadpack\nimport time:        73 |         73 |                         scipy.stats._finite_differences\nimport time:        88 |         88 |                         scipy.stats._constants\nimport time:       118 |        118 |                         scipy.stats._censored_data\nimport time:      1116 |      10278 |                       scipy.stats._distn_infrastructure\nimport time:       254 |        254 |                                 scipy.interpolate._fitpack\nimport time:       184 |        184 |                                 scipy.interpolate._dfitpack\nimport time:       320 |        757 |                               scipy.interpolate._fitpack_impl\nimport time:       161 |        161 |                                 scipy.interpolate._dierckx\nimport time:       449 |        610 |                               scipy.interpolate._bsplines\nimport time:       120 |       1486 |                             scipy.interpolate._fitpack_py\nimport time:       224 |        224 |                             scipy.interpolate._polyint\nimport time:       214 |        214 |                             scipy.interpolate._ppoly\nimport time:       261 |        261 |                             scipy.interpolate._interpnd\nimport time:       403 |       2586 |                           scipy.interpolate._interpolate\nimport time:       382 |        382 |                           scipy.interpolate._fitpack2\nimport time:       131 |        131 |                           scipy.interpolate._rbf\nimport time:       160 |        160 |                             scipy.interpolate._rbfinterp_pythran\nimport time:       144 |        304 |                           scipy.interpolate._rbfinterp\nimport time:       231 |        231 |                           scipy.interpolate._cubic\nimport time:       129 |        129 |                           scipy.interpolate._ndgriddata\nimport time:       173 |        173 |                           scipy.interpolate._fitpack_repro\nimport time:        70 |         70 |                           scipy.interpolate._pade\nimport time:       172 |        172 |                             scipy.interpolate._rgi_cython\nimport time:       393 |        393 |                             scipy.interpolate._ndbspline\nimport time:       204 |        768 |                           scipy.interpolate._rgi\nimport time:       195 |        195 |                           scipy.interpolate._bary_rational\nimport time:        63 |         63 |                           scipy.interpolate.fitpack\nimport time:        54 |         54 |                           scipy.interpolate.fitpack2\nimport time:        46 |         46 |                           scipy.interpolate.interpolate\nimport time:        51 |         51 |                           scipy.interpolate.ndgriddata\nimport time:        49 |         49 |                           scipy.interpolate.polyint\nimport time:        45 |         45 |                           scipy.interpolate.rbf\nimport time:        47 |         47 |                           scipy.interpolate.interpnd\nimport time:       259 |       5575 |                         scipy.interpolate\nimport time:       836 |        836 |                           scipy.special.cython_special\nimport time:      2042 |       2877 |                         scipy.stats._stats\nimport time:       392 |        392 |                         scipy.stats._tukeylambda_stats\nimport time:       267 |        267 |                         scipy.stats._ksstats\nimport time:     32168 |      41277 |                       scipy.stats._continuous_distns\nimport time:       332 |        332 |                         scipy.stats._biasedurn\nimport time:       193 |        193 |                         scipy.stats._stats_pythran\nimport time:      6344 |       6868 |                       scipy.stats._discrete_distns\nimport time:       226 |        226 |                         scipy.stats._levy_stable.levyst\nimport time:      1049 |       1275 |                       scipy.stats._levy_stable\nimport time:       202 |        202 |                         scipy.stats._axis_nan_policy\nimport time:       916 |       1117 |                       scipy.stats._entropy\nimport time:       211 |      61024 |                     scipy.stats.distributions\nimport time:       102 |        102 |                       scipy._lib._bunch\nimport time:      1039 |       1039 |                       scipy.stats._stats_mstats_common\nimport time:      1107 |       2247 |                     scipy.stats._mstats_basic\nimport time:       263 |        263 |                       scipy.stats._common\nimport time:      2075 |       2337 |                     scipy.stats._hypotests\nimport time:      3700 |       3700 |                     scipy.stats._resampling\nimport time:       170 |        170 |                     scipy.stats._binomtest\nimport time:     26528 |      96003 |                   scipy.stats._stats_py\nimport time:       439 |        439 |                   scipy.stats._variation\nimport time:       269 |        269 |                     scipy.stats._ansari_swilk_statistics\nimport time:       249 |        249 |                     scipy.stats._wilcoxon\nimport time:      1072 |       1072 |                     scipy.stats._fit\nimport time:       524 |        524 |                       scipy.stats._relative_risk\nimport time:       289 |        289 |                       scipy.stats._crosstab\nimport time:       161 |        161 |                       scipy.stats._odds_ratio\nimport time:       334 |       1306 |                     scipy.stats.contingency\nimport time:      5643 |       8538 |                   scipy.stats._morestats\nimport time:       148 |        148 |                         scipy.sparse.csgraph._laplacian\nimport time:       189 |        189 |                             scipy.sparse.csgraph._tools\nimport time:        93 |        281 |                           scipy.sparse.csgraph._validation\nimport time:       308 |        589 |                         scipy.sparse.csgraph._shortest_path\nimport time:       249 |        249 |                         scipy.sparse.csgraph._traversal\nimport time:       190 |        190 |                         scipy.sparse.csgraph._min_spanning_tree\nimport time:       195 |        195 |                         scipy.sparse.csgraph._flow\nimport time:       184 |        184 |                         scipy.sparse.csgraph._matching\nimport time:       199 |        199 |                         scipy.sparse.csgraph._reordering\nimport time:       167 |       1918 |                       scipy.sparse.csgraph\nimport time:       188 |        188 |                       scipy.stats._sobol\nimport time:       222 |        222 |                       scipy.stats._qmc_cy\nimport time:       974 |       3301 |                     scipy.stats._qmc\nimport time:       683 |       3983 |                   scipy.stats._multicomp\nimport time:       337 |        337 |                   scipy.stats._binned_statistic\nimport time:       218 |        218 |                       scipy.stats._covariance\nimport time:       250 |        250 |                         scipy.stats._rcont.rcont\nimport time:       138 |        387 |                       scipy.stats._rcont\nimport time:       207 |        207 |                         scipy.stats._qmvnt_cy\nimport time:       149 |        355 |                       scipy.stats._qmvnt\nimport time:      2080 |       3039 |                     scipy.stats._multivariate\nimport time:       206 |       3245 |                   scipy.stats._kde\nimport time:       345 |        345 |                     scipy.stats._mstats_extras\nimport time:       124 |        469 |                   scipy.stats.mstats\nimport time:        65 |         65 |                   scipy.stats.qmc\nimport time:       511 |        511 |                   scipy.stats._page_trend_test\nimport time:       894 |        894 |                   scipy.stats._mannwhitneyu\nimport time:       142 |        142 |                   scipy.stats._bws_test\nimport time:       849 |        849 |                   scipy.stats._sensitivity_analysis\nimport time:       713 |        713 |                   scipy.stats._survival\nimport time:       136 |        136 |                     scipy.optimize._bracket\nimport time:       147 |        147 |                     scipy.optimize._chandrupatla\nimport time:       138 |        138 |                     scipy.stats._probability_distribution\nimport time:      1752 |       2170 |                   scipy.stats._distribution_infrastructure\nimport time:      7520 |       7520 |                   scipy.stats._new_distributions\nimport time:        74 |         74 |                             scipy.ndimage._ni_support\nimport time:       200 |        200 |                             scipy.ndimage._nd_image\nimport time:       149 |        149 |                             scipy.ndimage._ni_docstrings\nimport time:       143 |        143 |                             scipy.ndimage._rank_filter_1d\nimport time:      1037 |       1602 |                           scipy.ndimage._filters\nimport time:       244 |        244 |                           scipy.ndimage._fourier\nimport time:       379 |        379 |                           scipy.ndimage._interpolation\nimport time:       219 |        219 |                             scipy.ndimage._ni_label\nimport time:       263 |        263 |                             scipy.ndimage._morphology\nimport time:       253 |        734 |                           scipy.ndimage._measurements\nimport time:       101 |       3058 |                         scipy.ndimage._ndimage_api\nimport time:       162 |        162 |                         scipy.ndimage._delegators\nimport time:       181 |       3401 |                       scipy.ndimage._support_alternative_backends\nimport time:        68 |         68 |                       scipy.ndimage.filters\nimport time:        51 |         51 |                       scipy.ndimage.fourier\nimport time:        49 |         49 |                       scipy.ndimage.interpolation\nimport time:        49 |         49 |                       scipy.ndimage.measurements\nimport time:        50 |         50 |                       scipy.ndimage.morphology\nimport time:       164 |       3830 |                     scipy.ndimage\nimport time:       407 |       4236 |                   scipy.stats._mgc\nimport time:       495 |        495 |                   scipy.stats._correlation\nimport time:       118 |        118 |                   scipy.stats._quantile\nimport time:        59 |         59 |                   scipy.stats.biasedurn\nimport time:        49 |         49 |                   scipy.stats.kde\nimport time:        59 |         59 |                   scipy.stats.morestats\nimport time:        68 |         68 |                   scipy.stats.mstats_basic\nimport time:        64 |         64 |                   scipy.stats.mstats_extras\nimport time:        53 |         53 |                   scipy.stats.mvn\nimport time:        65 |         65 |                   scipy.stats.stats\nimport time:       521 |     131815 |                 scipy.stats\nimport time:       129 |        129 |                     sklearn._config\nimport time:       175 |        175 |                       sklearn.__check_build._check_build\nimport time:        84 |        258 |                     sklearn.__check_build\nimport time:        56 |         56 |                     sklearn._distributor_init\nimport time:       168 |        168 |                       sklearn.exceptions\nimport time:        90 |         90 |                               sklearn.utils._bunch\nimport time:       458 |        548 |                             sklearn.utils._metadata_requests\nimport time:       116 |        663 |                           sklearn.utils.metadata_routing\nimport time:       168 |        168 |                                   sklearn.externals\nimport time:       160 |        160 |                                       sklearn.externals.array_api_compat.common._helpers\nimport time:        90 |        250 |                                     sklearn.externals.array_api_compat.common\nimport time:       241 |        490 |                                   sklearn.externals.array_api_compat\nimport time:        50 |         50 |                                           sklearn.externals.array_api_extra._lib._utils\nimport time:        46 |         46 |                                             sklearn.externals._array_api_compat_vendor\nimport time:        75 |        121 |                                           sklearn.externals.array_api_extra._lib._utils._compat\nimport time:       243 |        412 |                                         sklearn.externals.array_api_extra._lib._backends\nimport time:        80 |        492 |                                       sklearn.externals.array_api_extra._lib\nimport time:        44 |         44 |                                             sklearn.externals.array_api_extra._lib._utils._typing\nimport time:        92 |        135 |                                           sklearn.externals.array_api_extra._lib._utils._helpers\nimport time:       244 |        379 |                                         sklearn.externals.array_api_extra._lib._at\nimport time:       187 |        565 |                                       sklearn.externals.array_api_extra._lib._funcs\nimport time:       139 |       1195 |                                     sklearn.externals.array_api_extra._delegation\nimport time:       112 |        112 |                                     sklearn.externals.array_api_extra._lib._lazy\nimport time:       184 |       1490 |                                   sklearn.externals.array_api_extra\nimport time:       484 |        484 |                                       sklearn.externals.array_api_compat.common._aliases\nimport time:        75 |         75 |                                       sklearn.externals.array_api_compat._internal\nimport time:        86 |         86 |                                       sklearn.externals.array_api_compat.numpy._info\nimport time:       819 |       1462 |                                     sklearn.externals.array_api_compat.numpy._aliases\nimport time:       459 |        459 |                                       sklearn.externals.array_api_compat.common._linalg\nimport time:       329 |        787 |                                     sklearn.externals.array_api_compat.numpy.linalg\nimport time:        76 |         76 |                                       sklearn.externals.array_api_compat.common._fft\nimport time:       267 |        342 |                                     sklearn.externals.array_api_compat.numpy.fft\nimport time:       394 |       2984 |                                   sklearn.externals.array_api_compat.numpy\nimport time:        64 |         64 |                                       sklearn.externals._packaging\nimport time:        88 |         88 |                                       sklearn.externals._packaging._structures\nimport time:       608 |        760 |                                     sklearn.externals._packaging.version\nimport time:       171 |        171 |                                         ctypes.util\nimport time:       493 |        664 |                                       threadpoolctl\nimport time:       155 |        818 |                                     sklearn.utils.parallel\nimport time:       570 |       2147 |                                   sklearn.utils.fixes\nimport time:       425 |       7701 |                                 sklearn.utils._array_api\nimport time:       112 |        112 |                                 sklearn.utils.deprecation\nimport time:       431 |        431 |                                 sklearn.utils._isfinite\nimport time:      2587 |       2587 |                                 sklearn.utils._tags\nimport time:      4502 |      15330 |                               sklearn.utils.validation\nimport time:       465 |      15795 |                             sklearn.utils._param_validation\nimport time:       121 |      15915 |                           sklearn.utils._chunking\nimport time:       564 |        564 |                               sklearn.utils.sparsefuncs_fast\nimport time:       209 |        772 |                             sklearn.utils.extmath\nimport time:       177 |        949 |                           sklearn.utils._indexing\nimport time:        57 |         57 |                             sklearn.utils._missing\nimport time:       121 |        177 |                           sklearn.utils._mask\nimport time:        60 |         60 |                             sklearn.utils._repr_html\nimport time:       149 |        209 |                           sklearn.utils._repr_html.base\nimport time:       748 |        748 |                               html.entities\nimport time:       268 |       1016 |                             html\nimport time:       310 |       1326 |                           sklearn.utils._repr_html.estimator\nimport time:       112 |        112 |                           sklearn.utils.class_weight\nimport time:       108 |        108 |                           sklearn.utils.discovery\nimport time:       255 |        255 |                           sklearn.utils.murmurhash\nimport time:       199 |      19909 |                         sklearn.utils\nimport time:        24 |      19932 |                       sklearn.utils._metadata_requests\nimport time:       117 |        117 |                       sklearn.utils._repr_html.params\nimport time:       118 |        118 |                         sklearn.utils._available_if\nimport time:       258 |        375 |                       sklearn.utils._set_output\nimport time:       415 |      21005 |                     sklearn.base\nimport time:       282 |        282 |                       sklearn.utils._openmp_helpers\nimport time:       119 |        401 |                     sklearn.utils._show_versions\nimport time:        68 |         68 |                     sklearn._built_with_meson\nimport time:       214 |      22128 |                   sklearn\nimport time:       102 |        102 |                           sklearn.metrics.cluster._bicluster\nimport time:        98 |         98 |                               sklearn.utils._unique\nimport time:       168 |        266 |                             sklearn.utils.multiclass\nimport time:       253 |        253 |                             sklearn.metrics.cluster._expected_mutual_info_fast\nimport time:       274 |        792 |                           sklearn.metrics.cluster._supervised\nimport time:       215 |        215 |                                 sklearn.utils.sparsefuncs\nimport time:       294 |        294 |                                   sklearn.utils._encode\nimport time:       622 |        916 |                                 sklearn.preprocessing._encoders\nimport time:      3177 |       4307 |                               sklearn.preprocessing._data\nimport time:       174 |        174 |                                 sklearn.utils.stats\nimport time:       652 |        825 |                               sklearn.preprocessing._discretization\nimport time:       275 |        275 |                                 sklearn.utils.metaestimators\nimport time:       476 |        750 |                               sklearn.preprocessing._function_transformer\nimport time:       912 |        912 |                               sklearn.preprocessing._label\nimport time:       377 |        377 |                                 sklearn.preprocessing._csr_polynomial_expansion\nimport time:       787 |       1164 |                               sklearn.preprocessing._polynomial\nimport time:       361 |        361 |                                 sklearn.preprocessing._target_encoder_fast\nimport time:       501 |        862 |                               sklearn.preprocessing._target_encoder\nimport time:       162 |       8979 |                             sklearn.preprocessing\nimport time:       543 |        543 |                                   sklearn.metrics._dist_metrics\nimport time:       454 |        454 |                                       sklearn.metrics._pairwise_distances_reduction._datasets_pair\nimport time:       522 |        522 |                                       sklearn.utils._cython_blas\nimport time:       307 |       1283 |                                     sklearn.metrics._pairwise_distances_reduction._base\nimport time:       357 |        357 |                                     sklearn.metrics._pairwise_distances_reduction._middle_term_computer\nimport time:       148 |        148 |                                     sklearn.utils._heap\nimport time:       122 |        122 |                                     sklearn.utils._sorting\nimport time:       359 |       2268 |                                   sklearn.metrics._pairwise_distances_reduction._argkmin\nimport time:       279 |        279 |                                   sklearn.metrics._pairwise_distances_reduction._argkmin_classmode\nimport time:       198 |        198 |                                     sklearn.utils._vector_sentinel\nimport time:       510 |        708 |                                   sklearn.metrics._pairwise_distances_reduction._radius_neighbors\nimport time:       277 |        277 |                                   sklearn.metrics._pairwise_distances_reduction._radius_neighbors_classmode\nimport time:       337 |       4410 |                                 sklearn.metrics._pairwise_distances_reduction._dispatcher\nimport time:       135 |       4544 |                               sklearn.metrics._pairwise_distances_reduction\nimport time:       292 |        292 |                               sklearn.metrics._pairwise_fast\nimport time:       557 |       5392 |                             sklearn.metrics.pairwise\nimport time:       185 |      14555 |                           sklearn.metrics.cluster._unsupervised\nimport time:       119 |      15565 |                         sklearn.metrics.cluster\nimport time:       623 |        623 |                         sklearn.metrics._classification\nimport time:       109 |        109 |                           sklearn.metrics._plot\nimport time:        59 |         59 |                           sklearn.utils._optional_dependencies\nimport time:        77 |         77 |                             sklearn.utils._response\nimport time:       174 |        251 |                           sklearn.utils._plotting\nimport time:       185 |        603 |                         sklearn.metrics._plot.confusion_matrix\nimport time:       100 |        100 |                             sklearn.metrics._base\nimport time:       312 |        412 |                           sklearn.metrics._ranking\nimport time:       107 |        519 |                         sklearn.metrics._plot.det_curve\nimport time:       118 |        118 |                         sklearn.metrics._plot.precision_recall_curve\nimport time:       127 |        127 |                         sklearn.metrics._plot.regression\nimport time:       124 |        124 |                         sklearn.metrics._plot.roc_curve\nimport time:       219 |        219 |                         sklearn.metrics._regression\nimport time:      2227 |       2227 |                         sklearn.metrics._scorer\nimport time:       210 |      20331 |                       sklearn.metrics\nimport time:        20 |      20350 |                     sklearn.metrics.pairwise\nimport time:       922 |      21271 |                   sklearn.gaussian_process.kernels\nimport time:       764 |        764 |                     sklearn.multiclass\nimport time:       164 |        164 |                     sklearn.utils.optimize\nimport time:       599 |       1526 |                   sklearn.gaussian_process._gpc\nimport time:       286 |        286 |                   sklearn.gaussian_process._gpr\nimport time:       131 |      45341 |                 sklearn.gaussian_process\nimport time:       126 |        126 |                 bayes_opt.exception\nimport time:       203 |        203 |                         colorama.ansi\nimport time:        52 |         52 |                           msvcrt\nimport time:       102 |        102 |                           colorama.win32\nimport time:       187 |        340 |                         colorama.winterm\nimport time:       325 |        867 |                       colorama.ansitowin32\nimport time:       125 |        992 |                     colorama.initialise\nimport time:       118 |       1109 |                   colorama\nimport time:        53 |         53 |                       bayes_opt.util\nimport time:       256 |        308 |                     bayes_opt.parameter\nimport time:       140 |        448 |                   bayes_opt.constraint\nimport time:       206 |       1761 |                 bayes_opt.target_space\nimport time:       347 |     179388 |               bayes_opt.acquisition\nimport time:       136 |        136 |                 bayes_opt.domain_reduction\nimport time:       149 |        149 |                 bayes_opt.logger\nimport time:       220 |        504 |               bayes_opt.bayesian_optimization\nimport time:       554 |     180446 |             bayes_opt\nimport time:       295 |     250419 |           optimagic.config\nimport time:       101 |        101 |           optimagic.decorators\nimport time:       272 |     320999 |         optimagic.batch_evaluators\nimport time:        65 |         65 |           optimagic.differentiation\nimport time:       198 |        198 |           optimagic.differentiation.finite_differences\nimport time:       216 |        216 |           optimagic.differentiation.generate_steps\nimport time:       101 |        101 |           optimagic.differentiation.richardson_extrapolation\nimport time:       358 |        358 |           optimagic.parameters.block_trees\nimport time:      1063 |       1999 |         optimagic.differentiation.derivatives\nimport time:       796 |        796 |         optimagic.differentiation.numdiff_options\nimport time:       110 |        110 |           optimagic.parameters.process_selectors\nimport time:       525 |        525 |             optimagic.parameters.scaling\nimport time:       116 |        116 |               optimagic.parameters.kernel_transformations\nimport time:        88 |         88 |                 optimagic.parameters.check_constraints\nimport time:       177 |        177 |                 optimagic.parameters.consolidate_constraints\nimport time:       109 |        373 |               optimagic.parameters.process_constraints\nimport time:      1239 |       1726 |             optimagic.parameters.space_conversion\nimport time:       556 |       2806 |           optimagic.parameters.scale_conversion\nimport time:       328 |        328 |           optimagic.parameters.tree_conversion\nimport time:       626 |       3869 |         optimagic.parameters.conversion\nimport time:      1004 |     328664 |       optimagic.optimization.internal_optimization_problem\nimport time:       122 |        122 |       optimagic.type_conversion\nimport time:      2028 |     562944 |     optimagic.optimization.algorithm\nimport time:       287 |     563231 |   optimagic.mark\nimport time:        74 |         74 |       optimagic.optimizers\nimport time:      1228 |       1301 |     optimagic.optimizers.bayesian_optimizer\nimport time:       593 |        593 |     optimagic.optimizers.bhhh\nimport time:      1123 |       1123 |     optimagic.optimizers.fides\nimport time:       731 |        731 |     optimagic.optimizers.iminuit_migrad\nimport time:       188 |        188 |         optimagic.parameters.nonlinear_constraints\nimport time:     12546 |      12734 |       optimagic.optimizers.scipy_optimizers\nimport time:      6962 |      19695 |     optimagic.optimizers.ipopt\nimport time:      2624 |       2624 |     optimagic.optimizers.nag_optimizers\nimport time:       861 |        861 |     optimagic.optimizers.neldermead\nimport time:       902 |        902 |     optimagic.optimizers.nevergrad_optimizers\nimport time:      8049 |       8049 |     optimagic.optimizers.nlopt_optimizers\nimport time:        78 |         78 |         optimagic.optimizers._pounders\nimport time:        71 |         71 |           optimagic.optimizers._pounders._conjugate_gradient\nimport time:        76 |         76 |           optimagic.optimizers._pounders._steihaug_toint\nimport time:       112 |        112 |           optimagic.optimizers._pounders._trsbox\nimport time:       324 |        581 |         optimagic.optimizers._pounders.bntr\nimport time:       328 |        328 |         optimagic.optimizers._pounders.gqtpar\nimport time:       414 |       1399 |       optimagic.optimizers._pounders.pounders_auxiliary\nimport time:       136 |        136 |       optimagic.optimizers._pounders.pounders_history\nimport time:      1269 |       2803 |     optimagic.optimizers.pounders\nimport time:        55 |         55 |       pygmo\nimport time:     11508 |      11562 |     optimagic.optimizers.pygmo_optimizers\nimport time:        51 |         51 |       petsc4py\nimport time:       614 |        665 |     optimagic.optimizers.tao_optimizers\nimport time:      3310 |       3310 |     optimagic.optimizers.tranquilo\nimport time:    112487 |     166699 |   optimagic.algorithms\nimport time:       169 |        169 |     optimagic.benchmarking\nimport time:        90 |         90 |     optimagic.benchmarking.process_benchmark_results\nimport time:        54 |         54 |       optimagic.visualization\nimport time:       138 |        191 |     optimagic.visualization.profile_plot\nimport time:       231 |        680 |   optimagic.benchmarking.benchmark_reports\nimport time:      1850 |       1850 |       optimagic.benchmarking.more_wild\nimport time:      1021 |       2870 |     optimagic.benchmarking.cartis_roberts\nimport time:        98 |         98 |     optimagic.benchmarking.noise_distributions\nimport time:        77 |         77 |       optimagic.shared\nimport time:       164 |        240 |     optimagic.shared.process_user_function\nimport time:       164 |       3371 |   optimagic.benchmarking.get_benchmark_problems\nimport time:      1760 |       1760 |         optimagic.optimization.multistart_options\nimport time:       151 |        151 |         optimagic.optimization.scipy_aliases\nimport time:      1001 |       2911 |       optimagic.optimization.create_optimization_problem\nimport time:       225 |        225 |       optimagic.optimization.error_penalty\nimport time:        61 |         61 |         optimagic.optimization.optimization_logging\nimport time:       186 |        246 |       optimagic.optimization.multistart\nimport time:        53 |         53 |         optimagic.shared.compat\nimport time:      1041 |       1094 |       optimagic.optimization.optimize_result\nimport time:       144 |        144 |         optimagic.optimization.convergence_report\nimport time:       575 |        718 |       optimagic.optimization.process_results\nimport time:       276 |       5467 |     optimagic.optimization.optimize\nimport time:       132 |       5598 |   optimagic.benchmarking.run_benchmark\nimport time:       269 |        269 |   optimagic.logging.read_log\nimport time:        86 |         86 |   optimagic.parameters.constraint_tools\nimport time:       156 |        156 |     plotly.graph_objects\nimport time:       109 |        109 |       plotly.subplots\nimport time:       144 |        252 |     optimagic.visualization.plotting_utilities\nimport time:       117 |        524 |   optimagic.visualization.convergence_plot\nimport time:       130 |        130 |   optimagic.visualization.history_plots\nimport time:       110 |        110 |   optimagic.visualization.slice_plot\nimport time:        55 |         55 |   optimagic._version\nimport time:       236 |     978915 | optimagic\n"}}}}