# Move Optional Dependency Imports Into Functions to Improve Import Time

## Summary

This PR significantly improves optimagic's import time by moving optional dependency imports from module-level to function-level, implementing lazy loading for heavy optimization libraries. This change reduces startup time, especially when multiple optional dependencies are installed.

## Problem

Previously, optimagic imported all available optional dependencies at module load time, causing slow import performance when users had many optimization libraries installed. For example, importing `pygmo`, `cyipopt`, `fides`, and other heavy libraries added significant overhead even when these optimizers weren't being used.

## Solution

Implemented lazy loading pattern where optional dependencies are only imported when their corresponding optimization functions are actually called. This follows the pattern already established in `bayesian_optimizer.py`.

### Pattern Used

```python
# For type hints only (zero runtime cost)
if TYPE_CHECKING:
    from optional_library import SomeClass

# Runtime import only when function is called
def _solve_internal_problem(self, ...):
    if not IS_LIBRARY_INSTALLED:
        raise NotInstalledError(...)
    
    import optional_library  # Import here when actually needed
    # Use the library...
```

## Changes Made

### Libraries Modified

| **Optimizer File** | **Library** | **Status** | **Notes** |
|-------------------|-------------|------------|-----------|
| `pygmo_optimizers.py` | `pygmo` | ✅ **Modified** | Heavy genetic algorithm library |
| `ipopt.py` | `cyipopt` | ✅ **Modified** | Interior point optimization |
| `fides.py` | `fides` | ✅ **Modified** | Trust region optimizer |
| `tao_optimizers.py` | `petsc4py` | ✅ **Modified** | PETSc scientific computing |
| `nevergrad_optimizers.py` | `nevergrad` | ✅ **Modified** | Facebook's optimization library |
| `tranquilo.py` | `tranquilo` | ✅ **Modified** | Trust region optimizer |
| `nag_optimizers.py` | `pybobyqa`, `dfols` | ✅ **Modified** | NAG optimization algorithms |
| `bayesian_optimizer.py` | `bayes_opt` | ✅ **Already correct** | Was already using lazy loading |
| `iminuit_migrad.py` | `iminuit` | ✅ **Already correct** | Required dependency, proper pattern |

### Libraries Not Modified (By Design)

| **Optimizer File** | **Library** | **Reason** |
|-------------------|-------------|------------|
| `scipy_optimizers.py` | `scipy` | Required dependency (non-optional) |
| `nlopt_optimizers.py` | `nlopt` | Will become required dependency |
| `pyensmallen_optimizers.py` | `pyensmallen` | Designed for fast objective functions |
| `neldermead.py` | None | Pure Python implementation |
| `bhhh.py` | None | Pure Python implementation |
| `pounders.py` | None | Pure Python implementation |

## Performance Impact

### Expected Results
- **Minimal dependencies**: Small improvement (overhead reduction)
- **Many optional dependencies**: Significant improvement (20-30% faster import)
- **Runtime performance**: No change - libraries imported when needed

### Testing
Use the provided import time measurement script to test before/after:

```bash
# Test with minimal dependencies
pip install -e .
python test_import_timing.py

# Test with full dependencies  
conda env create -f environment.yml
python test_import_timing.py
```

## Implementation Details

### Key Changes
1. **Moved top-level imports** to `TYPE_CHECKING` blocks for type hints
2. **Added runtime imports** inside `_solve_internal_problem` methods
3. **Maintained error handling** with proper `NotInstalledError` messages
4. **Fixed helper functions** to import dependencies where needed
5. **Ensured consistency** in error checking patterns across all optimizers

### Error Handling
- Consistent use of `IS_*_INSTALLED` flags for availability checking
- Clear error messages directing users to installation instructions
- Proper `NotInstalledError` exceptions when libraries are missing

### Type Safety
- Maintained full type hint support using `TYPE_CHECKING` imports
- No runtime cost for type annotations due to `from __future__ import annotations`
- All static analysis tools continue to work correctly

## Testing

- ✅ All existing tests pass
- ✅ Import functionality verified
- ✅ Optimizer functionality unchanged
- ✅ Error handling works correctly
- ✅ Type hints remain functional

## Breaking Changes

None. This is a pure performance improvement with no API changes.

## Related Issues

Closes #617 - Move import of some optional dependencies into functions or methods

## Files Changed

- `src/optimagic/optimizers/pygmo_optimizers.py`
- `src/optimagic/optimizers/ipopt.py`
- `src/optimagic/optimizers/fides.py`
- `src/optimagic/optimizers/tao_optimizers.py`
- `src/optimagic/optimizers/nevergrad_optimizers.py`
- `src/optimagic/optimizers/tranquilo.py`
- `src/optimagic/optimizers/nag_optimizers.py`
- `test_import_timing.py` (new testing script)

## Review Notes

The changes follow the established pattern from `bayesian_optimizer.py` and maintain full backward compatibility while significantly improving import performance for users with multiple optional dependencies installed.
