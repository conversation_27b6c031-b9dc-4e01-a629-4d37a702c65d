(list_of_videos)=

# Videos

Check out our tutorials, talks and screencasts about optimagic.

## Talks and tutorials

### EuroSciPy 2023 (Talk)

```{raw} html
<iframe
src="https://www.youtube.com/embed/5xYn0v1zEsY"
style="width: 100%; aspect-ratio: 16 / 9;"
allowfullscreen>
</iframe>
```

### EuroSciPy 2023 (Tutorial)

```{raw} html
<iframe
src="https://www.youtube.com/embed/LQo5NDFKH1Q"
style="width: 100%; aspect-ratio: 16 / 9;"
allowfullscreen>
</iframe>
```

### SciPy 2022 (Tutorial)

```{raw} html
<iframe
src="https://www.youtube.com/embed/ftlw0rARrtI"
style="width: 100%; aspect-ratio: 16 / 9;"
allowfullscreen>
</iframe>
```

## Screencasts

The screencasts are part of the course _Effective Programming Practices for Economists_,
taught at the University of Bonn by
[<PERSON><PERSON><PERSON>](https://www.wiwi.uni-bonn.de/gaudecker/), and previously
also [<PERSON><PERSON><PERSON>](https://github.com/janosg). You can find all screencasts of the
course on the
[course webite](https://effective-programming-practices.vercel.app/landing-page.html).
Here, we show the screencasts about numerical optimization and optimagic.

### Introduction to numerical optimization

```{raw} html
<iframe
src="https://www.youtube.com/embed/hOZueB4Cn1Y"
style="width: 100%; aspect-ratio: 16 / 9;"
allowfullscreen>
</iframe>
```

### Using optimagic’s minimize and maximize

```{raw} html
<iframe
src="https://www.youtube.com/embed/QqTGE3nq0q8"
style="width: 100%; aspect-ratio: 16 / 9;"
allowfullscreen>
</iframe>
```

### Visualizing optimizer histories

```{raw} html
<iframe
src="https://www.youtube.com/embed/wQWWW8rlxmY"
style="width: 100%; aspect-ratio: 16 / 9;"
allowfullscreen>
</iframe>
```

### Choosing optimization algorithms

```{raw} html
<iframe
src="https://www.youtube.com/embed/tJ7Xba3wcxY"
style="width: 100%; aspect-ratio: 16 / 9;"
allowfullscreen>
</iframe>
```
