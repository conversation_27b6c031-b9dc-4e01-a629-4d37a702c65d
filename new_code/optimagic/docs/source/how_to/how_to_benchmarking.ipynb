{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["# How to Benchmark Optimization Algorithms\n", "\n", "Benchmarking optimization algorithms is an important step when developing a new algorithm or when searching for an algorithm that is good at solving a particular problem. \n", "\n", "In general, benchmarking constists of the following steps:\n", "\n", "1. Define the test problems (or get pre-implemented ones)\n", "2. Define the optimization algorithms and the tuning parameters you want to try\n", "3. Run the benchmark\n", "4. Plot the results\n", "\n", "optimagic helps you with all of these steps!"]}, {"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["## 1. Get Test Problems\n", "\n", "optimagic includes the problems of [<PERSON><PERSON> and <PERSON> (2009)](https://doi.org/10.1137/080724083) as well as [<PERSON><PERSON> and <PERSON>](https://arxiv.org/abs/1710.11005).\n", "\n", "Each problem consist of the `inputs` (the criterion function and the start parameters) and the `solution` (the optimal parameters and criterion value) and optionally provides more information.\n", "\n", "Below we load a subset of the <PERSON><PERSON> and <PERSON> problems and look at one particular Rosenbrock problem that has difficult start parameters."]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import plotly.io as pio\n", "\n", "pio.renderers.default = \"notebook_connected\"\n", "\n", "import optimagic as om"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["problems = om.get_benchmark_problems(\"example\")"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["## 2. Specify the Optimizers\n", "\n", "To select optimizers you want to benchmark on the set of problems, you can simply specify them as a list. Advanced examples - that do not only compare algorithms but also vary the `algo_options` - can be found below. "]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["optimizers = [\n", "    \"nag_dfols\",\n", "    \"scipy_neldermead\",\n", "    \"scipy_truncated_newton\",\n", "]"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["## 3. Run the Benchmark\n", "\n", "Once you have your problems and your optimizers set up, you can simply use `run_benchmark`. The results are a dictionary with one entry for each (problem, algorithm) combination. Each entry not only saves the solution but also the history of the algorithm's criterion and parameter history. "]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["results = om.run_benchmark(\n", "    problems,\n", "    optimizers,\n", ")"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["## 4a. Profile plots\n", "\n", "**Profile Plots** compare optimizers over a whole problem set. \n", "\n", "The literature distinguishes **data profiles** and **performance profiles**. Data profiles use a normalized runtime measure whereas performance profiles use an absolute one. The profile plot does not normalize runtime by default. To do this, simply set `normalize_runtime` to True. For background information, check [<PERSON><PERSON> and <PERSON> (2009)](https://doi.org/10.1137/080724083). "]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["fig = om.profile_plot(\n", "    problems=problems,\n", "    results=results,\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["The x axis shows runtime per problem. The y axis shows the share of problems each algorithm solved within that runtime. Thus, higher and further to the left values are desirable. Higher means more problems were solved and further to the left means, the algorithm found the solutions earlier. \n", "\n", "You can choose:\n", "\n", "- whether to use `n_evaluations` or `walltime` as **`runtime_measure`**\n", "- whether to normalize runtime such that the runtime of each problem is shown as a multiple of the fastest algorithm on that problem\n", "- how to determine when an evaluation is close enough to the optimum to be counted as converged. Convergence is always based on some measure of distance between the true solution and the solution found by an optimizer. Whether distiance is measured in parameter space, function space, or a combination of both can be specified. \n", "\n", "Below, we consider a problem to be solved if the distance between the parameters found by the optimizer and the true solution parameters are at most 0.1% of the distance between the start parameters and true solution parameters. "]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["fig = om.profile_plot(\n", "    problems=problems,\n", "    results=results,\n", "    runtime_measure=\"n_evaluations\",\n", "    stopping_criterion=\"x\",\n", "    x_precision=0.001,\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["## 4b. Convergence plots\n", "\n", "**Convergence Plots** look at particular problems and show the convergence of each optimizer on each problem. "]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["fig = om.convergence_plot(\n", "    problems=problems,\n", "    results=results,\n", "    n_cols=2,\n", "    problem_subset=[\"rosenbrock_good_start\", \"box_3d\"],\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["The further to the left and the lower the curve of an algorithm, the better that algorithm performed.\n", "\n", "Often we are more interested in how close each algorithm got to the true solution in parameter space, not in criterion space as above. For this. we simply set the **`distance_measure`** to `parameter_space`. "]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["fig = om.convergence_plot(\n", "    problems=problems,\n", "    results=results,\n", "    n_cols=2,\n", "    problem_subset=[\"rosenbrock_good_start\", \"box_3d\"],\n", "    distance_measure=\"parameter_distance\",\n", "    stopping_criterion=\"x\",\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["## 5a. Convergence report\n", "\n", "The **Convergence Report** shows for each problem and optimizer which problems the optimizer solved successfully, failed to do so, or where it stopped with an error. The respective strings are \"success\", \"failed\", or \"error\".\n", "Moreover, the last column of the ```pd.DataFrame``` displays the number of dimensions of the benchmark problem."]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["df = om.convergence_report(\n", "    problems=problems,\n", "    results=results,\n", "    stopping_criterion=\"y\",\n", "    x_precision=1e-4,\n", "    y_precision=1e-4,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "markdown", "id": "19", "metadata": {}, "source": ["## 5b. Rank report¶\n", "\n", "The **Rank Report** shows the ranks of the algorithms for each problem; where 0 means the algorithm was the fastest on a given benchmark problem, 1 means it was the second fastest and so on. If an algorithm did not converge on a problem, the value is \"failed\". If an algorithm did encounter an error during optimization, the value is \"error\"."]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["df = om.rank_report(\n", "    problems=problems,\n", "    results=results,\n", "    runtime_measure=\"n_evaluations\",\n", "    stopping_criterion=\"y\",\n", "    x_precision=1e-4,\n", "    y_precision=1e-4,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "markdown", "id": "22", "metadata": {}, "source": ["## 5b. <PERSON><PERSON> report¶\n", "\n", "The **Traceback Report** shows the tracebacks returned by the optimizers if they encountered an error during optimization. The resulting ```pd.DataFrame``` is empty if none of the optimizers terminated with an error, as in the example below."]}, {"cell_type": "code", "execution_count": null, "id": "23", "metadata": {}, "outputs": [], "source": ["df = om.traceback_report(problems=problems, results=results)"]}, {"cell_type": "code", "execution_count": null, "id": "24", "metadata": {}, "outputs": [], "source": ["df"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}