{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Bootstrap Monte Carlo Comparison"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this juy<PERSON>er notebook, we perform a <PERSON> exercise to illustrate the importance of using the cluster robust variant of the bootstrap when data within clusters is correlated. \n", "\n", "The main idea is to repeatedly draw clustered samples, get both uniform and clustered bootstrap estimates in these samples, and then compare how often the true null hypothesis is rejected."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Generating Process"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The true data generating process is given by\n", "\n", "$$ logit(y_{i,g}) = \\beta_0 + \\beta_1 (x_{i,g}) + \\epsilon_{i,g}, $$\n", "\n", "where the independent variable $x_{i,g} = x_i + x_g$ and the noise term $\\epsilon_{i,g} = \\epsilon_i + \\epsilon_g$ each consist of an individual and a cluster term.\n", "\n", "In the simulations we perform below, we have $\\beta_0 = \\beta_1 =0$. $x_i$ and $x_g$ are drawn from a standard normal distribution, and $\\epsilon_i$ and $\\epsilon_g$ are drawn from a normal distribution with $\\mu_0$ and $\\sigma=0.5$. The value of $\\sigma$ is chosen to not blow up rejection rates in the independent case too much."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "import scipy\n", "import statsmodels.api as sm\n", "from joblib import Parallel, delayed\n", "\n", "import estimagic as em"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def create_clustered_data(nclusters, nobs_per_cluster, true_beta=0):\n", "    \"\"\"Create a bivariate clustered dataset with specified number of\n", "    clusters and number of observations per cluster that has a population\n", "    value of true_beta for the logit coefficient on the independent variable.\n", "\n", "    Args:\n", "        nclusters (int): Number of clusters.\n", "        nobs_per_cluster (int): Number of observations per cluster.\n", "        true_beta (int): The true logit coefficient on x.\n", "\n", "    Returns:\n", "        pd.DataFrame: Clustered dataset.\n", "    \"\"\"\n", "\n", "    x_cluster = np.random.normal(size=nclusters)\n", "    x_ind = np.random.normal(size=nobs_per_cluster * nclusters)\n", "    eps_cluster = np.random.normal(size=nclusters, scale=0.5)\n", "    eps_ind = np.random.normal(size=nobs_per_cluster * nclusters, scale=0.5)\n", "\n", "    y = []\n", "    x = []\n", "    cluster = []\n", "\n", "    for g in range(nclusters):\n", "        for i in range(nobs_per_cluster):\n", "            key = (i + 1) * (g + 1) - 1\n", "\n", "            arg = (\n", "                true_beta * (x_cluster[g] + x_ind[key]) + eps_ind[key] + eps_cluster[g]\n", "            )\n", "\n", "            y_prob = 1 / (1 + np.exp(-arg))\n", "            y.append(np.random.binomial(n=1, p=y_prob))\n", "            x.append(x_cluster[g] + x_ind[(i + 1) * (g + 1) - 1])\n", "            cluster.append(g)\n", "\n", "    y = np.array(y)\n", "    x = np.array(x)\n", "    cluster = np.array(cluster)\n", "\n", "    return pd.DataFrame({\"y\": y, \"x\": x, \"cluster\": cluster})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Monte Carlo Simulation Code"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The following function computes bootstrap t-values. As suggested my <PERSON> (2015), critical values are the 0.975 quantiles from a t distribution with `n_clusters` -1 degrees of freedom."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def get_t_values(data, sample_size=200, hyp_beta=0, cluster=False):\n", "    \"\"\"Get bootstrap t-values for testing the hypothesis that beta == hyp_beta.\n", "\n", "    Args:\n", "        data (pd.DataFrame): Original dataset.\n", "        sample_size (int): Number of bootstrap samples to draw.\n", "        hyp_beta (float): Hypothesised value of beta.\n", "        cluster (bool): Whether or not to cluster on the cluster column.\n", "\n", "    Returns:\n", "        float: T-Value of hypothesis.\n", "    \"\"\"\n", "\n", "    def logit_wrap(df):\n", "        y = df[\"y\"]\n", "        x = df[\"x\"]\n", "\n", "        result = sm.Logit(y, sm.add_constant(x)).fit(disp=0).params\n", "\n", "        return pd.Series(result, index=[\"constant\", \"x\"])\n", "\n", "    if cluster is False:\n", "        result = em.bootstrap(data=data, outcome=logit_wrap, n_draws=sample_size)\n", "        estimates = pd.DataFrame(result.outcomes)[\"x\"]\n", "\n", "    else:\n", "        result = em.bootstrap(\n", "            data=data,\n", "            outcome=logit_wrap,\n", "            n_draws=sample_size,\n", "            cluster_by=\"cluster\",\n", "        )\n", "        estimates = pd.DataFrame(result.outcomes)[\"x\"]\n", "\n", "    return (estimates.mean() - hyp_beta) / estimates.std()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def monte_carlo(nsim, nclusters, nobs_per_cluster, true_beta=0, n_cores=8):\n", "    \"\"\"Run a simulation for rejection rates and a logit data generating process.\n", "\n", "    Rejection rates are based on a t distribution with nclusters-1 degrees of freedom.\n", "\n", "    Args:\n", "        nsim (int): Number of Monte Carlo draws.\n", "        nclusters (int): Number of clusters in each generated dataset.\n", "        nobs_per_cluster (int) Number of observations per cluster.\n", "        true_beta (int): Population value of logit coefficient on x.\n", "        n_cores (int): Number of jobs for Parallelization.\n", "\n", "    Returns:\n", "        pd.DataFrame: DataFrame of average rejection rates.\n", "    \"\"\"\n", "\n", "    np.zeros(nsim)\n", "\n", "    np.zeros(nsim)\n", "\n", "    def loop():\n", "        df = create_clustered_data(nclusters, nobs_per_cluster, true_beta)\n", "\n", "        return [get_t_values(df), get_t_values(df, cluster=True)]\n", "\n", "    t_value_array = np.array(\n", "        Parallel(n_jobs=n_cores)(delayed(loop)() for _ in range(nsim))\n", "    )\n", "    t_value_array = np.array([loop() for _ in range(nsim)])\n", "\n", "    crit = scipy.stats.t.ppf(0.975, nclusters - 1)\n", "\n", "    result = pd.DataFrame(np.abs(t_value_array) > crit, columns=[\"uniform\", \"cluster\"])\n", "\n", "    return result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here, we perform Monte Carlo simulations with the above functions. In each simulation, the sample size is 200, but the number of clusters varies across simulations. Be warned that the code below takes a long time to run."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["np.random.seed(505)\n", "\n", "results_list = []\n", "\n", "for g, k in [[20, 50], [100, 10], [500, 2]]:\n", "    results_list.append(monte_carlo(nsim=100, nclusters=g, nobs_per_cluster=k))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["mean_rejection_data = pd.DataFrame([x.mean() for x in results_list])\n", "mean_rejection_data[\"nclusters\"] = [20, 100, 500]\n", "mean_rejection_data.set_index(\"nclusters\", inplace=True)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 0.98, 'Comparison of Rejection Rates')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["y = mean_rejection_data\n", "x = y.index.values\n", "\n", "plt.rcParams[\"figure.figsize\"] = (8, 5)\n", "plt.xlabel(\"Number of clusters\", fontsize=12)\n", "plt.ylabel(\"Rejection rate\", fontsize=12)\n", "plt.plot(x, y[\"uniform\"], label=\"Uniform Bootstrap\", color=\"blue\", marker=\"o\")\n", "plt.plot(x, y[\"cluster\"], label=\"Cluster Bootstrap\", color=\"red\", marker=\"o\")\n", "plt.legend()\n", "plt.suptitle(\"Comparison of Rejection Rates\", fontsize=15)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see that when the number of clusters is low, it is particularly important to use the cluster robust bootstrap, since rejection with the regular bootstrap is excessive. For a large number of clusters, clustering naturally becomes less important. "]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}, "vscode": {"interpreter": {"hash": "e8a16b1bdcc80285313db4674a5df2a5a80c75795379c5d9f174c7c712f05b3a"}}}, "nbformat": 4, "nbformat_minor": 4}