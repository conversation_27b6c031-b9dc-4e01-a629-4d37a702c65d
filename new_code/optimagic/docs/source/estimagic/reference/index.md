# estimagic API

```{eval-rst}
.. currentmodule:: estimagic
```

(estimation)=

## Estimation

```{eval-rst}
.. dropdown:: estimate_ml

    .. autofunction:: estimate_ml

```

```{eval-rst}
.. dropdown:: estimate_msm

    .. autofunction:: estimate_msm

```

```{eval-rst}
.. dropdown:: get_moments_cov

    .. autofunction:: get_moments_cov

```

```{eval-rst}
.. dropdown:: lollipop_plot

    .. autofunction:: lollipop_plot

```

```{eval-rst}
.. dropdown:: estimation_table

    .. autofunction:: estimation_table

```

```{eval-rst}
.. dropdown:: render_html

    .. autofunction:: render_html

```

```{eval-rst}
.. dropdown:: render_latex

    .. autofunction:: render_latex

```

```{eval-rst}
.. dropdown:: LikelihoodResult

    .. autoclass:: LikelihoodResult
        :members:

```

```{eval-rst}
.. dropdown:: MomentsResult

    .. autoclass:: MomentsResult
        :members:



```

(bootstrap)=

## Bootstrap

```{eval-rst}
.. dropdown:: bootstrap

    .. autofunction:: bootstrap
```

```{eval-rst}
.. dropdown::  BootstrapResult

    .. autoclass:: BootstrapResult
        :members:


```
